#include <ros/ros.h>
#include <std_msgs/String.h>
// #include <pinocchio/fwd.hpp>
// #include <Q2CControlInterface.h>
#include <upper_computer/ZLGControlInterface.h>
#include <iostream>
#include <thread>
#include <csignal>
// #include "pd/PDController.h"
// #include "ql_common_package/PublicVarHolder.h"
// #include "LoongController.h"
// #include "ql_common_package/MsgPublisherHolder.h"
#include <random>
#include <fstream>
#include <vector>
#include <std_msgs/Float32.h>
#include <std_msgs/String.h>
#include <geometry_msgs/Twist.h>
// #include "ql_common_package/ParaValueStore.h"
// #include "ql_common_package/DynamicParaConfigHelper.h"
// #include "ql_common_package/LinearInterpolation.h"

int main(int argc, char **argv)
{
    ros::init(argc, argv, "node_q2c_robot_control");
    ros::NodeHandle nh;
    ros::Rate loop_rate(500); // 设置循环频率

    ZLGControlInterface zlgInterface;

    ros::spin();
    return 0;
}
