#include "upper_computer/ZLGControlInterface.h"
#include "std_msgs/String.h"
#include "std_msgs/Float64.h"
#include "std_msgs/Int32.h"
#include <sensor_msgs/JointState.h>
// #include "ql_common_package/MotorUtils.h"
// #include "ql_common_package/MsgPublisherHolder.h"
// #include "ql_common_package/PublicVarHolder.h"
// #include "ankle_ik.h"
#include <mutex>
#include "upper_computer/fixed_file_logger.h"

#include <tf/tf.h>
/**********UDP相关数据定义 - 起始**********/

struct sockaddr_in local_server_addr;
struct sockaddr_in remote_controller_addr;
struct ip_mreq mreq;

char udp_group_ip[16] = "***********";          // UDP组播地址 

uint16_t mult_udp_port_controller = 10001;      // UDP组播发送目标端口
uint16_t mult_udp_port_local_listening = 10002; // UDP组播监听端口

const int ZLG_CONTROL_HEAD_LEG = 0x585A;
const int ZLG_CONTROL_HEAD_ARM = 0x535A;

const int ZLG_CONTROL_MODE_FORCE = 0X04;
const int ZLG_CONTROL_MODE_POS = 0X08;

/**********UDP相关数据定义 - 结束**********/ 


std::mutex mtx; // 全局互斥锁


ZLGControlInterface::ZLGControlInterface()
{
    if (!initUdpSocketLocal())
    {
        std::cout << "init mutl-udp fail" << std::endl;
    }
    else
    {
        std::cout << "init mutl-udp success" << std::endl;
        _init_udp_ok = true;
        _arm_motor_state_actual_pub = _nh.advertise<sensor_msgs::JointState>(robot_constants::topic_arm_motor_state_actual, 100);
        arm_motor_error_code_pub = _nh.advertise<std_msgs::Float64MultiArray>(robot_constants::topic_arm_motor_error_code, 100);
        arm_pos_control_motor_data_sub = _nh.subscribe(robot_constants::topic_arm_pos_control_command_data, 1, &ZLGControlInterface::armPosControlMotorDataReceiveCallback, this);

        pthread_create(&_tid, NULL, runUdpReceive, (void *)this);
    }
}

ZLGControlInterface::~ZLGControlInterface()
{
    pthread_cancel(_tid);
    pthread_join(_tid, NULL);
    close(_socked_udp_mult);
    std::cout << "ZLGControlInterface Destructor" << std::endl;
}


void ZLGControlInterface::armPosControlMotorDataReceiveCallback(const std_msgs::Float64MultiArray &msg)
{
    if (msg.data.size() != hardware_constants::arm_servo_num * 2)
    {
        return;
    }

    std::vector<double> armPosVec(hardware_constants::arm_servo_num);


    for (int i = 0; i < hardware_constants::arm_servo_num; i++)
    {
        armPosVec[i] = msg.data[i];
    }

    armControlMotorCommandSend(armPosVec);
}

bool ZLGControlInterface::dataReady()
{
    ros::spinOnce();
    double now_time_sec = ros::Time::now().toSec();
    bool leg_data_ready = (now_time_sec - last_receive_frame_time_sec) < 0.1;
    return leg_data_ready;
}

bool ZLGControlInterface::waitDataReady(int timeoutSeconds)
{
    int elapsed = 0;
    while (true)
    {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        ROS_INFO_STREAM("Waiting for hardware ready ......");
        if (dataReady())
        {
            return true;
        }
        elapsed++;
        if (elapsed > timeoutSeconds)
        {
            ROS_ERROR_STREAM("Hardware not ready, exit ......");
            return false;
        }
    }
}

void *ZLGControlInterface::runUdpReceive(void *arg)
{
    ((ZLGControlInterface *)arg)->runReceiveFromControl(NULL);
}


void ZLGControlInterface::armControlMotorCommandSend(const std::vector<double> &posIn)
{
    if (!_init_udp_ok)
    {
        return;
    }
    _send_frame_count++;

    ArmControCommulationMsgStr armPosControCommulationStr;
    armPosControCommulationStr.fram_cnt = _send_frame_count;

    std_msgs::Float64MultiArray armMotorPosSendMsg;
    armMotorPosSendMsg.data.resize(hardware_constants::arm_servo_num);

    for (int i = 0; i < hardware_constants::arm_servo_num; i++)
    {
        armPosControCommulationStr.pos_desired_rad[i] = posIn[i];
    }
    // 发送数据到控制器
    sendto(_socked_udp_mult, (char *)&armPosControCommulationStr,
           sizeof(armPosControCommulationStr), 0,
           (struct sockaddr *)&remote_controller_addr, sizeof(remote_controller_addr));

    if (_send_frame_count % 1000 == 0)
    {
        std::string msg = armPosControCommulationStr.toString();
        ROS_INFO("Arm control commond send: %s", msg.c_str());
    }
}

void *ZLGControlInterface::runReceiveFromControl(void *arg)
{
    char receive_buffer[1024];
    while (true)
    {
        memset(receive_buffer, 0, sizeof(receive_buffer));
        int n = recvfrom(_socked_udp_mult, receive_buffer, sizeof(receive_buffer), 0, NULL, NULL);

        if (n == sizeof(_received_controller_state))
        {
            last_receive_frame_time_sec = ros::Time::now().toSec();
            if (sizeof(receive_buffer) >= sizeof(_received_controller_state))
            {
                receiveCount++;
                mtx.lock();
                memcpy(&_received_controller_state, receive_buffer, sizeof(_received_controller_state));
                for (int i = 0; i < hardware_constants::arm_servo_num; i++)
                {
                    _received_controller_state.stat_motor[i].act_vel = _received_controller_state.stat_motor[i].act_vel * 2 * M_PI / 60;
                }

                mtx.unlock();

                if (receiveCount % 2000 == 0)
                {
                    std::string msg = _received_controller_state.toString();
                    ROS_INFO("Motor state receive: %s", msg.c_str());
                }
                if (hardware_constants::arm_servo_num > 0)
                {
                    sensor_msgs::JointState arm_joint_state_actual_msg;
                    arm_joint_state_actual_msg.header.stamp = ros::Time::now();
                    std::stringstream armErrorCodeStream;
                    std_msgs::Float64MultiArray armErrorCodeDataMsg;
                    armErrorCodeDataMsg.data.resize(hardware_constants::arm_servo_num + 1);
                    for (int i = 0; i < hardware_constants::arm_servo_num; i++)
                    {
                        double pos = _received_controller_state.stat_motor[i].act_pos;
                        double current = _received_controller_state.stat_motor[i].act_curr / 1000.0;
                        double vel = _received_controller_state.stat_motor[i].act_vel;
                        armErrorCodeStream << _received_controller_state.error_code[i] << "\t";
                        arm_joint_state_actual_msg.position.push_back(pos);
                        arm_joint_state_actual_msg.velocity.push_back(vel);
                        arm_joint_state_actual_msg.effort.push_back(current);
                        arm_joint_state_actual_msg.name.push_back("joint" + std::to_string(i + 1));

                        armErrorCodeDataMsg.data[i + 1] = _received_controller_state.error_code[i];
                    }
                    arm_joint_state_actual_msg.header.frame_id = armErrorCodeStream.str();
                    _arm_motor_state_actual_pub.publish(arm_joint_state_actual_msg);
                    arm_motor_error_code_pub.publish(armErrorCodeDataMsg);
                }
            }
        }
        else if (n == 0)
        {
            close(_socked_udp_mult);
            printf("server closed.\n");
        }
        else if (n == -1)
        {
            close(_socked_udp_mult);
            printf("recvfrom error.\n");
        }
        else
        {
            ROS_ERROR("-----------------");
            FIXED_LOG_INFO("UDP ERROR");
        }
    }
}

bool ZLGControlInterface::initUdpSocketLocal()
{
    _socked_udp_mult = socket(AF_INET, SOCK_DGRAM, 0);
    if (_socked_udp_mult < 0)
    {
        printf("socket failed!");
        return false;
    }

    memset(&local_server_addr, 0, sizeof(local_server_addr));
    local_server_addr.sin_family = AF_INET;
    local_server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    local_server_addr.sin_port = htons(mult_udp_port_local_listening);

    int ret = bind(_socked_udp_mult, (struct sockaddr *)&local_server_addr, sizeof(local_server_addr));
    if (ret < 0)
    {
        std::cout << "bind failed !" << std::endl;
        return false;
    }
    mreq.imr_multiaddr.s_addr = inet_addr(udp_group_ip);
    mreq.imr_interface.s_addr = htonl(INADDR_ANY); // 使用默认组播接口

    // IP_ADD_MEMBERSHIP 加入多播组
    ret = setsockopt(_socked_udp_mult, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq));
    if (ret < 0)
    {
        std::cout << "socked_udp_mult setsockopt failed !" << std::endl;
        return false;
    }
    else
    {
        std::cout << "socked_rcv setsockopt success" << std::endl;
    }
    memset(&remote_controller_addr, 0, sizeof(remote_controller_addr));
    remote_controller_addr.sin_family = AF_INET;
    remote_controller_addr.sin_addr.s_addr = inet_addr(udp_group_ip);
    remote_controller_addr.sin_port = htons(mult_udp_port_controller);
    return true;
}
