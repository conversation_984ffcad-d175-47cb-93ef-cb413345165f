cmake_minimum_required(VERSION 3.0.2)
project(upper_computer)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  roslib
  std_msgs
)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES upper_computer
#  CATKIN_DEPENDS roscpp rospy std_msgs
#  DEPENDS system_lib
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

add_library(robot_lib3
  src/ZLGControlInterface.cpp
)

target_link_libraries(robot_lib3
  ${catkin_LIBRARIES}
)

add_executable(node_q2c_robot_control src/node_q2c_robot_control.cpp)
target_link_libraries(node_q2c_robot_control    
  robot_lib3 
)


