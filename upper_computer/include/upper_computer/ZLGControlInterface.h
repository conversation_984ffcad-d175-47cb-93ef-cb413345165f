#ifndef ZLGCONTROLINTERFACE_H
#define ZLGCONTROLINTERFACE_H

#include "ZLGControlDefine.h"

#include "ros/ros.h"
#include "sensor_msgs/JointState.h"
#include "std_msgs/Float64MultiArray.h"
#include "std_msgs/Int16MultiArray.h"
#include <sensor_msgs/Imu.h>
#include "common_constant.h"
// #include "data_bus.h"

#include <mutex>


class ZLGControlInterface
{
public:
    ZLGControlInterface();
    ~ZLGControlInterface();

    bool dataReady();

    bool waitDataReady(int timeoutSeconds = 5);


private:
    pthread_t _tid;
    ros::NodeHandle _nh;
    ros::Publisher _arm_motor_state_actual_pub;


    ros::Subscriber arm_pos_control_motor_data_sub;
    ros::Publisher arm_motor_error_code_pub;

    int _socked_udp_mult = -1;
    bool _init_udp_ok = false;
    ArmLegStateMsgStrUDP _received_controller_state;

    int _send_frame_count = 0;

    static void *runUdpReceive(void *arg);
    void *runReceiveFromControl(void *arg);

    void armPosControlMotorDataReceiveCallback(const std_msgs::Float64MultiArray &msg);
    void armControlMotorCommandSend(const std::vector<double> &posIn);
    bool initUdpSocketLocal();

    int receiveCount = 0;

    bool emergencyStopFlag = false;
    
    double last_receive_frame_time_sec = 0;
};

#endif