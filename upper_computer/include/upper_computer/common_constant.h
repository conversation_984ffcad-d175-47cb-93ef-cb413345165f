
#ifndef COMMON_PACKAGEXPP_COMMON_CONSTANT_H_
#define COMMON_PACKAGEXPP_COMMON_CONSTANT_H_

#include <string>

namespace robot_constants
{

  static const std::string topic_urdf_data_source("/urdf_date_source");

  static const std::string topic_motor_state_actual("motor_state/leg_motor_state_actual");

  static const std::string topic_arm_motor_state_actual("motor_state/arm_motor_state_actual");

  static const std::string topic_motor_state_theoretical("motor_state/leg_motor_state_theoretical");

  static const std::string topic_motor_state_extend_theoretical("motor_state/leg_motor_state_extend_theoretical");

  static const std::string topic_runtime_leg_motor_state_actual("motor_state/runtime_leg_motor_state_actual");

  static const std::string topic_arm_motor_state_theoretical("motor_state/arm_motor_state_theoretical");

  static const std::string topic_joint_states("/joint_states");

  static const std::string topic_leg_pos_control_command_data("motor_command/leg_position_control");

  static const std::string topic_arm_pos_control_command_data("motor_command/arm_position_control");

  static const std::string topic_leg_force_control_command_data("motor_command/leg_force_control");

  static const std::string topic_imu_data("/imu/data");

  static const std::string topic_imu_data_re("/imu/data_re");

  static const std::string topic_heart_beat("/heart_beat");

  static const std::string topic_sri_data_actucl("sensors/sridata");

  static const std::string topic_flag_toe_touch_ground("flag/toe_touch_ground");

  static const std::string topic_motor_error_code("motor_state/leg_error_code");

  static const std::string topic_fram_cnt_code("motor_state/leg_fram_cnt");

  static const std::string topic_leg_motor_vbus("motor_state/leg_vbus");

  static const std::string topic_arm_motor_error_code("motor_state/arm_error_code");

  static const std::string topic_leg_torqure_actual("motor_state/leg_torqure_actual");

  static const std::string topic_motor_pd_torqure_ff("motor_state/motor_pd_torqure_ff");

  static const std::string topic_motor_curent_send("motor_state/motor_curent_send");

  static const std::string topic_motor_pos_des_send("motor_state/motor_pos_des_send");

  static const std::string topic_motor_kp_send("motor_state/motor_kp_send");

  static const std::string topic_motor_kd_send("motor_state/motor_kd_send");

  static const std::string topic_lcm_command("lcm_command_from_upper");

  static const std::string topic_switch_to_mpc("fsm/switch_to_mpc");

  static const std::string topic_common_command("command/common_command");

  // parameters describing the robot kinematics
  static const std::string robot_parameters("/xpp/params");

  // information about terrain normals and friction coefficients
  static const std::string terrain_info("/xpp/terrain_info");

  static const std::string topic_ankle_tor_jieou_data("/motor_state/fanjieou_t_data");

}

namespace string_constants
{
  static const std::string string_init_standing_controller("init_standing_controller");
  static const std::string string_load_wbc_controller("load_wbc_controller");
}

namespace std_msg_content_constants
{
  static const std::string string_switch_to_mpc("switch_to_mpc");
}

namespace hardware_constants
{
  static const int leg_servo_num = 12;
  static const int arm_servo_num = 14;
  static const int foot_force_sensor_num = 2;
  static const int foot_touch_force_threshod = 105; // N
}

namespace temp_constants
{
  static const double joint1_bias_degree = 0;
  static const double joint2_bias_degree = 0;
  static const double joint3_bias_degree = 0;
  static const double joint4_bias_degree = 0;
  static const double joint5_bias_degree = 0;
  static const double joint6_bias_degree = 0;

  static const double joint7_bias_degree = 0;
  static const double joint8_bias_degree = 0;
  static const double joint9_bias_degree = 0;
  static const double joint10_bias_degree = 0;
  static const double joint11_bias_degree = 0;
  static const double joint12_bias_degree = 0;
}

#endif /* XPP_MSGS_TOPIC_NAMES_H_ */
