#ifndef ZLGCONTROLDEFINE_H
#define ZLGCONTROLDEFINE_H

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <math.h>
#include <pthread.h>
#include <thread>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <arpa/inet.h>
#include <deque>
#include <mutex>
#include <errno.h>

#include <sys/types.h> //基本系统数据类型
#include <sys/stat.h>  //获取一些文件相关的信息
#include <fcntl.h>     //文件控制定义
#include <termios.h>   //PPSIX终端控制定义

#include <iostream>
#include <cstring>
#include <sstream>

#include "common_constant.h"

////////////////////////////////////////////////////////////////////////////////
typedef int8_t I8;
typedef int16_t I16;
typedef int32_t I32;
typedef int64_t I64;
typedef uint8_t U8;
typedef uint16_t U16;
typedef uint32_t U32;
typedef uint64_t U64;
typedef float F32;
typedef double F64;


// 腿部控制UDP发送结构体
#pragma pack(1)
struct ArmControCommulationMsgStr
{
    U16 head; // 0x535A "SZ"; 0x585A "XZ"
    U16 data_length;
    U8 trans_identify;
    U8 ctl_mode; // 0x08 位控  0X04 力控
    U32 fram_cnt;

    F64 pos_desired_rad[hardware_constants::arm_servo_num];
    F64 tor_ff_nm[hardware_constants::arm_servo_num];
    

    ArmControCommulationMsgStr()
    {
        memset(this, 0, sizeof(ArmControCommulationMsgStr));
        head = 0x535A;
        ctl_mode = 0X80;
        data_length = 8 * hardware_constants::arm_servo_num * 2 + 3;
        trans_identify = 1;
    }

    std::string toString() const
    {
        std::stringstream ss;
        ss << "head: " << head << " ";
        ss << "data_length: " << static_cast<int>(data_length) << " ";
        ss << "trans_identify: " << static_cast<int>(trans_identify) << " ";
        ss << "ctl_mode: " << static_cast<int>(ctl_mode) << " ";
        ss << "fram_cnt: " << static_cast<int>(fram_cnt) << " ";
        ss << "pos: [";
        for (int i = 0; i < hardware_constants::arm_servo_num; ++i)
        {
            ss << ((int)(pos_desired_rad[i] * 1000)) / 1000.0;
            if (i < hardware_constants::arm_servo_num - 1)
                ss << ", ";
        }
        ss << "]";

        return ss.str();
    }
};
#pragma pack()

// 电机信息结构体
#pragma pack(1)
struct StatMotorStr
{
    F64 act_pos;
    F64 act_curr;
    F64 act_vel;
    StatMotorStr()
    {
        memset(this, 0, sizeof(StatMotorStr));
    }

    std::string toString()
    {
        // Convert struct fields to string representation
        std::ostringstream oss;
        oss << "act_pos: " << ((int)(act_pos * 1000)) / 1000.0 << ", act_curr: " << ((int)(act_curr * 1000)) / 1000.0 << ", act_vel: " << ((int)(act_vel * 1000)) / 1000.0;
        return oss.str();
    }
};
#pragma pack()

// UDP状态接收结构体
#pragma pack(1)
struct ArmLegStateMsgStrUDP
{
    U16 head;
    U16 data_length;
    U8 trans_identify;
    U8 ctl_mode;
    U32 fram_cnt;
    U16 error_code[hardware_constants::arm_servo_num];
    StatMotorStr stat_motor[hardware_constants::arm_servo_num];
    ArmLegStateMsgStrUDP()
    {
        memset(this, 0, sizeof(ArmLegStateMsgStrUDP));
    }

    std::string toString()
    {
        // Convert struct fields to string representation
        std::ostringstream oss;
        oss << "head: " << head << ", data_length: " << static_cast<unsigned>(data_length) << ", trans_identify: " << static_cast<unsigned>(trans_identify)
            << ", ctl_mode: " << static_cast<unsigned>(ctl_mode) << ", fram_cnt: " << static_cast<unsigned>(fram_cnt) << " ";
        // Append string representations of each StatMotorStr instance
        for (int i = 0; i < (hardware_constants::arm_servo_num); i++)
        {
            oss << "stat_motor[" << i << "]: " << stat_motor[i].toString() << " ";
        }

        for (int j = 0; j < (hardware_constants::arm_servo_num); j++)
        {
            oss << "error_code[" << j << "]: " << error_code[j] << " ";
        }
        return oss.str();
    }
};
#pragma pack()

#endif