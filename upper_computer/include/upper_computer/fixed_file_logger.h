#ifndef FIXED_FILE_LOGGER_H
#define FIXED_FILE_LOGGER_H

#include <iostream>
#include <fstream>
#include <chrono>
#include <iomanip>
#include <ctime>
#include <mutex>
#include <ros/package.h>
#include <sys/stat.h> // 用于获取文件大小

class FixedFileLogger {
public:
    // 获取单例实例的静态方法
    static FixedFileLogger& getInstance() {
        static FixedFileLogger instance; // 静态局部变量，保证线程安全
        return instance;
    }

    // 设置日志文件大小阈值（单位：字节）
    void setMaxFileSize(size_t maxSize) {
        std::lock_guard<std::mutex> lock(fileMutex);
        maxFileSize = maxSize;
    }

    // 记录日志的方法
    void log(const std::string& message, const std::string& level = "INFO", const std::string& filePath = "", int line = 0) {
        std::lock_guard<std::mutex> lock(fileMutex);

        // 如果是第一次记录日志，创建日志文件
        if (!fileCreated) {
            createNewLogFile();
            fileCreated = true;
        }

        // 检查文件大小是否超过阈值
        if (this->file.is_open() && getFileSize(currentFilePath) >= maxFileSize) {
            this->file.close();
            createNewLogFile();
        }

        // 如果文件未打开，尝试打开
        if (!this->file.is_open()) {
            this->file.open(currentFilePath, std::ios::app);
            if (!this->file.is_open()) {
                std::cerr << "Error: Could not open file " << currentFilePath << std::endl;
                return;
            }
        }

        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        std::time_t now_time = std::chrono::system_clock::to_time_t(now);

        // 格式化时间
        std::tm tm = *std::localtime(&now_time);
        this->file << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] "
                   << "[" << level << "] ";
        if (!currentFilePath.empty()) {
            this->file << "[" << filePath << ":" << line << "] ";
        }
        this->file << message << std::endl;
    }

private:
    // 私有构造函数，禁止外部实例化
    FixedFileLogger() : fileCreated(false) {} // 初始化时未创建文件

    // 禁止拷贝构造函数和赋值操作符
    FixedFileLogger(const FixedFileLogger&) = delete;
    FixedFileLogger& operator=(const FixedFileLogger&) = delete;

    // 析构函数，关闭文件
    ~FixedFileLogger() {
        if (file.is_open()) {
            file.close();
        }
    }

    // 获取文件大小
    std::size_t getFileSize(const std::string& path) {
        struct stat stat_buf;
        int rc = stat(path.c_str(), &stat_buf);
        return rc == 0 ? stat_buf.st_size : 0;
    }

    // 创建新的日志文件
    void createNewLogFile() {
        if (file.is_open()) {
            file.close();
        }

        // 生成新的文件名（带时间戳）
        auto now = std::chrono::system_clock::now();
        std::time_t now_time = std::chrono::system_clock::to_time_t(now);
        std::tm tm = *std::localtime(&now_time);
        char buffer[80];
        std::strftime(buffer, sizeof(buffer), "%Y%m%d_%H%M%S", &tm);
        currentFilePath = ros::package::getPath("azureloong_control") + "/log_" + std::string(buffer) + ".txt";

        // 打开新的日志文件
        file.open(currentFilePath, std::ios::app);
        if (!file.is_open()) {
            std::cerr << "Error: Could not create file " << currentFilePath << std::endl;
        }
    }

    std::ofstream file;          // 文件输出流
    std::mutex fileMutex;        // 互斥锁，确保线程安全
    std::string currentFilePath; // 当前日志文件路径
    size_t maxFileSize = 1024 * 1024; // 默认文件大小阈值为 1MB
    bool fileCreated;            // 标志位，表示是否已创建日志文件
};

// 定义日志宏（添加 FIXED_ 前缀）
#define FIXED_LOG(level, message) FixedFileLogger::getInstance().log(message, level)
#define FIXED_LOG_INFO(message) FIXED_LOG("INFO", message)
#define FIXED_LOG_WARNING(message) FIXED_LOG("WARNING", message)
#define FIXED_LOG_ERROR(message) FIXED_LOG("ERROR", message)

#endif // FIXED_FILE_LOGGER_H