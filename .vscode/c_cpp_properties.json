{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/src/robot_control/include/**", "/home/<USER>/src/arms_gen2_control/include/**", "/home/<USER>/src/assembly_control/include/**", "/home/<USER>/Hector_Software/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/src/Assem4/include/**", "/home/<USER>/src/arms_gen2_control/include/**", "/home/<USER>/Hector_Software/src/hector-ros-simulation/Hector_ROS_Simulation/common_package/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/curves/curves/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/curves/curves_ros/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/sensors_driver/fdilink_ahrs/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/graph_rviz_plugin/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/hector_control/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/kindr_ros/kindr_ros/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/kindr_ros/kindr_rviz_plugins/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/kindr_ros/multi_dof_joint_trajectory_rviz_plugins/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/robot_controllers/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/unitree_ros/unitree_controller/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/<PERSON>_ROS_Simulation/unitree_ros/unitree_legged_control/include/**", "/home/<USER>/<PERSON>_Software/src/hector-ros-simulation/Hector_ROS_Simulation/xpp_msgs/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}