{"files.associations": {"*.tcc": "cpp", "deque": "cpp", "list": "cpp", "string": "cpp", "vector": "cpp", "valarray": "cpp", "array": "cpp", "string_view": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "cmath": "cpp", "iostream": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "cctype": "cpp", "clocale": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "atomic": "cpp", "strstream": "cpp", "bitset": "cpp", "chrono": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "set": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "variant": "cpp", "bit": "cpp", "*.ipp": "cpp", "forward_list": "cpp", "hash_map": "cpp", "hash_set": "cpp", "core": "cpp", "specialfunctions": "cpp", "dense": "cpp", "lu": "cpp", "tensor": "cpp", "regex": "cpp", "csetjmp": "cpp", "any": "cpp", "charconv": "cpp", "cuchar": "cpp", "netfwd": "cpp", "rope": "cpp", "slist": "cpp", "scoped_allocator": "cpp", "image": "cpp", "ranges": "cpp", "span": "cpp", "compare": "cpp", "concepts": "cpp", "coroutine": "cpp", "numbers": "cpp", "stop_token": "cpp", "semaphore": "cpp"}, "C_Cpp.errorSquiggles": "disabled", "python.autoComplete.extraPaths": ["/home/<USER>/HumanRobot/Hector_Software/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/HumanRobot/Hector_Software/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"]}