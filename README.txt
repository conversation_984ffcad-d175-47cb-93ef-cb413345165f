
主站：
ssh root@192.168.1.136
./demo
组播通信：
sudo route add -net 224.0.1.100 netmask 255.255.255.255 dev enp3s0
cd /home/<USER>
source ./devel/setup.bash
rosrun upper_computer node_q2c_robot_control
仿真：
cd /home/<USER>/src/robot_control/steps
./rviz_sim.sh
遥控：
cd /home/<USER>
source ./devel/setup.bash
rosrun robot_control velocity_interpolator


***************
 Eigen3  >= 3.2.92 (3.3~beta1) is required
***************

***************
 kdl库
***************
git clone https://github.com/orocos/orocos_kinematics_dynamics.git

cd orocos_kinematics_dynamics/
cd orocos_kdl

mkdir build

cd build
cmake ..
make
sudo make install
***************

***************
kdl_parser库
***************
git clone https://github.com/ros/kdl_parser.git
cd kdl_parser
git checkout noetic-devel
git branch 
cd kdl_parser

mkdir build
cd build
cmake ..
make
sudo make install
***************

***************
hpp-fcl库(老版本)
***************

git clone https://github.com/qhull/qhull.git
cd qhull
mkdir build
cd build
cmake ..
make -j8
sudo make install

git clone https://github.com/humanoid-path-planner/hpp-fcl.git

CMakeLists.txt文件 "option(COAL_HAS_QHULL "use qhull library to compute convex hulls." FALSE)" FALSE改成TRUE
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
sudo make install
***************
***************
coal库：
conda create --name coal python=3.10
conda install coal -c conda-forge

trac-ik库：
sudo apt-get install ros-noetic-trac-ik

升级GLIBCXX：
sudo apt-get update && sudo apt-get install -y software-properties-common \
&& sudo add-apt-repository ppa:ubuntu-toolchain-r/test -y \
&& sudo apt-get update \
&& sudo apt-get install -y gcc-11 g++-11 libstdc++6 \
&& sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-11 110 \
&& sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-11 110 \
&& sudo update-alternatives --install /usr/bin/cc cc /usr/bin/gcc 30 \
&& sudo update-alternatives --set cc /usr/bin/gcc \
&& sudo update-alternatives --install /usr/bin/c++ c++ /usr/bin/g++ 30 \
&& sudo update-alternatives --set c++ /usr/bin/g++ \
&& sudo ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" | sudo tee /etc/timezone \
&& sudo locale-gen en_US.UTF-8 \
&& sudo locale-gen zh_CN.UTF-8 \
&& sudo apt-get clean \
&& sudo rm -rf /var/lib/apt/lists/*
***************

***************
.bashrc文件写入URDF文件路径, 用于kdl解析机械臂URDF文件
export URDF_PATH=/home/<USER>/R_ws/robot_arm_v2/src/arms_gen2/urdf/arms_gen2_urdf.urdf
export URDF_PATH3=/home/<USER>/R_ws/robot_arm_v2/src/arms_gen2/urdf/arms_gen3_urdf.urdf
***************

初始编译：
catkin_make -DCMAKE_BUILD_TYPE=RelWithDebInfo

记录数据：
rosbag record /car_pos/act /car_pos/error /car_pos/vir /motor_state/arm_motor_state_actual

rosbag record /motor_state/arm_motor_state_actual /joint_states  


防火墙：
sudo route add -net 224.0.1.100 netmask 255.255.255.255 dev enp4s0

因时手串口：
sudo chmod a+rw /dev/ttyUSB0

设置单手、双手
DataProc_Init.cpp: setFORCE(400,400,400,400,400,400,1);

抓孔钉两个手的位置
source ./devel/setup.bash
rosservice call /inspire_hand/set_angle 500 500 500 500 500 100 1

rosservice call /inspire_hand/set_angle 999 999 999 999 999 100 2   

rosservice call /inspire_hand/get_force_act 1

****************************************************************************************************
****************************************************************************************************
****************************************************************************************************
********************************************机械臂演示节点********************************************
****************************************************************************************************
****************************************************************************************************

***************
遥控节点
rosrun robot_control velocity_interpolator

cd src/robot_control/script/
python3 interpolator.py
***************

***************
ZD放门上节点：
rosrun robot_control door

rosservice call /inspire_hand/set_angle 150 150 200 200 600 100 1

rosservice call /inspire_hand/set_angle 400 400 400 400 600 100 1

rosservice call /inspire_hand/set_angle 999 999 999 999 999 100 1
***************

***************
打羽毛球节点：
rosrun robot_control play_badminton

rosservice call /inspire_hand/set_angle 50 50 50 50 300 800 2

rosservice call /inspire_hand/set_angle 999 999 500 500 500 10 1
***************

***************
双臂装配节点：
python grasp_obj.py
rosrun robot_control grasp_assembly

整机装配时桌子位置:
桌子高度108cm
桌子距离机器人19cm

孔钉位置：
0.3 0.12 -0.33 1 0.3 -0.1 -0.31 0
***************

***************
敬礼抱枪节点：
python pose_D435i.py
rosrun robot_control demo_salute  1 2

左手需要提前拿住枪 10 10 10 199 799 300 
***************

***************
挥手
rosrun arms_gen2_control Action03_welcome
***************

***************
倒水节点：
python3 juice_demo.py
rosrun arms_gen2_control Action04_pour_water
***************

***************
有/无语音抓水果
python3 grasp_orange.py
rosrun arms_gen2_control Action12_give_fruit
rosrun arms_gen2_control Action13_give_fruit_based_LLM
***************