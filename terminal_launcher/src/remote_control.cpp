/*
手柄通信节点
A:1140 
B:1302
C:1410
D:1545
E:1680
F:1860
*/

#include <iostream>
#include <iomanip>
#include <serial/serial.h>
#include <unistd.h>
#include <cmath>
#include <cstring> // for memmove

// 定义有效值及其标签
struct ValidValue {
    int value;
    char label;
    bool operator==(int other) const { return value == other; }
};

const ValidValue validValues[] = {
    {1140, 'A'},
    {1302, 'B'},
    {1410, 'C'},
    {1545, 'D'},
    {1680, 'E'},
    {1860, 'F'}
};

const int tolerance = 10; // 允许的误差范围

// 检查是否是有效值并返回对应的标签
char getValidLabel(int value) {
    for (const auto& v : validValues) {
        if (abs(value - v.value) <= tolerance) {
            return v.label;
        }
    }
    return 0; // 不是有效值
}

int main() {
    serial::Serial my_serial;
    char last_label = 0;
    
    try {
        my_serial.setPort("/dev/ttyUSB0");
        my_serial.setBaudrate(115200);
        serial::Timeout timeout = serial::Timeout::simpleTimeout(1000);
        my_serial.setTimeout(timeout);
        my_serial.open();

        if (!my_serial.isOpen()) {
            std::cerr << "Failed to open serial port!" << std::endl;
            return -1;
        }

        std::cout << "Serial port opened successfully." << std::endl;

        const size_t buffer_size = 35;
        uint8_t buffer[buffer_size];
        bool searching_header = true;

        while (true) {
            if (searching_header) {
                // Search for frame header (0x0F)
                uint8_t byte;
                while (true) {
                    if (my_serial.read(&byte, 1) == 1) {
                        if (byte == 0x0F) {
                            buffer[0] = byte;
                            break;
                        }
                    }
                }
                
                // Read remaining 34 bytes
                size_t bytes_read = my_serial.read(buffer + 1, buffer_size - 1);
                if (bytes_read == buffer_size - 1) {
                    searching_header = false;
                }
            } else {
                // We're aligned, read full frame
                size_t bytes_read = my_serial.read(buffer, buffer_size);
                if (bytes_read != buffer_size) {
                    searching_header = true;
                    continue;
                }
                
                // Verify frame header is still 0x0F
                if (buffer[0] != 0x0F) {
                    // std::cout << "buffer[0] != 0x0F" << std::endl;
                    searching_header = true;
                    continue;
                }
            }
            
            // Process the frame
            uint16_t SUSB = (buffer[17] << 8) | buffer[18];
            int current_value = static_cast<int>(std::round((SUSB - 202)/1.6 + 1000));
            char current_label = getValidLabel(current_value);
            
            if (current_label == 0) {
                // std::cout << "Received valid frame (hex): ";
                // for (size_t i = 0; i < buffer_size; ++i) {
                //     std::cout << std::hex << std::setw(2) << std::setfill('0') 
                //              << static_cast<int>(buffer[i]) << " ";
                // }
                // std::cout << "值错误 "  << std::endl;
                continue;
            }
            
            // Only print when result changes
            if (current_label != last_label) {
                std::cout << "Received valid frame (hex): ";
                for (size_t i = 0; i < buffer_size; ++i) {
                    std::cout << std::hex << std::setw(2) << std::setfill('0') 
                             << static_cast<int>(buffer[i]) << " ";
                }
                std::cout << std::dec << "\n";
                std::cout << "SUSB (hex): 0x" << std::hex << std::setw(4) 
                          << SUSB << " (dec: " << std::dec << SUSB << ")\n";
                std::cout << "Detected value: " << current_value 
                          << " (" << current_label << ")" << std::endl;
                
                last_label = current_label;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    }
    return 0;
}