#include <ros/ros.h>
#include <std_msgs/Int32.h>  // 使用 Int32 而不是 String
#include <stdlib.h> // for system()

class TerminalLauncher
{
public:
    TerminalLauncher()
    {
        // 初始化订阅者，订阅"launch_terminal"话题，消息类型改为Int32
        sub_ = nh_.subscribe("/launch_terminal", 1, &TerminalLauncher::callback, this);
        ROS_INFO("Terminal Launcher node is ready...");
    }

    void callback(const std_msgs::Int32::ConstPtr& msg)
    {
        // 可以根据接收到的整数值决定是否执行命令
        ROS_INFO("Received integer command: %d", msg->data);
        
        int msg_flag = msg->data;

        if(msg_flag == 1) {  // 例如，当收到1时执行命令
            ROS_INFO("Launching terminal...");
            
            // 要执行的命令
            std::string cmd_str = "gnome-terminal -- bash -c 'roslaunch robot_description_v2 display.launch; exec bash'";
            
            // 执行系统命令
            int result = system(cmd_str.c_str());
            
            if (result == 0) {
                ROS_INFO("Terminal launched successfully.");
            } else {
                ROS_ERROR("Failed to launch terminal. Error code: %d", result);
            }
        }else if(msg_flag == 2) {

            ROS_INFO("Launching terminal...");
            
            // 要执行的命令
            std::string cmd_str = "gnome-terminal -- bash -c 'rosrun hector_control hector_manual_ctrl; exec bash'";
            
            // 执行系统命令
            int result = system(cmd_str.c_str());
            
            if (result == 0) {
                ROS_INFO("Terminal launched successfully.");
            } else {
                ROS_ERROR("Failed to launch terminal. Error code: %d", result);
            }
        }
    }

private:
    ros::NodeHandle nh_;
    ros::Subscriber sub_;
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "terminal_launcher_node");
    TerminalLauncher launcher;
    ros::spin();
    return 0;
}