cmake_minimum_required(VERSION 3.0.2)
project(terminal_launcher)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  serial
)

catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES terminal_launcher
#  CATKIN_DEPENDS roscpp std_msgs
#  DEPENDS system_lib
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
include_directories(
# include
  ${catkin_INCLUDE_DIRS}
)

add_executable(terminal_launcher_node src/terminal_launcher_node.cpp)
target_link_libraries(terminal_launcher_node ${catkin_LIBRARIES})

add_executable(remote_control src/remote_control.cpp)
target_link_libraries(remote_control ${catkin_LIBRARIES})

