#ifndef _DEF_STRUCT_H
#define _DEF_STRUCT_H

#include "eigen3/Eigen/Core"
#include "eigen3/Eigen/Dense"
#include "eigen3/Eigen/LU"

using namespace Eigen;

struct Demon_Traj
{
    VectorXd time;
    VectorXd pos_ta1;
    VectorXd pos_ta2;
    VectorXd pos_ta3;
    VectorXd pos_ta4;
    VectorXd vel_ta1;
    VectorXd vel_ta2;
    VectorXd vel_ta3;
    VectorXd vel_ta4;
    VectorXd acc_ta1;
    VectorXd acc_ta2;
    VectorXd acc_ta3;
    VectorXd acc_ta4;
    VectorXd ArmJnt;
};
struct Train_Par
{
    int nbfs;       // 基函数个数
    double alpha_y;
    double k;
    int k_f;
};
struct Train_Res
{
    MatrixXd w;
	VectorXd c;
	VectorXd h;
	int N;
	double alpha_x;
	double alpha_y;
	double beta_y;
	double t_end;
	int k_f;
};
struct Regress_Par
{
    Vector4d x_init;
    Vector4d x_goal;
    double tau;
    double dt;
};
struct Plan_Res_MulDOF
{
    MatrixXd pos;
    MatrixXd vel;
    MatrixXd acc;
    VectorXd t;
    int error_flag = 0;
};
struct Plan_Res
{
    VectorXd pos;
    VectorXd vel;
    VectorXd acc;
    VectorXd t;
};
struct JntPos
{
    VectorXd theta;
    int error_flag = 0;
    int solved_flag = 0;
};
struct KineRes
{
    double eul_r;
    double eul_p;
    double eul_y;
    Vector3d cart_end_position;
    Matrix3d transMatrix;
};

typedef enum ctrl_mode{
    stop = 0,
    rviz_ctrl,
    gazebo_ctrl,
    robot_ctrl
}CTRL_MODE;

#endif