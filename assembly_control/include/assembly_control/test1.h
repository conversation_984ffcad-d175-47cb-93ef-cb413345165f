#pragma once

#include <stdio.h>
#include <string.h>
#include <ros/ros.h>
#include <iostream>
#include <ros/ros.h>
#include <cmath>
#include <vector>
#include <tf/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <actionlib/client/simple_action_client.h>
#include <boost/thread.hpp>

#include "std_msgs/String.h"
#include "std_msgs/Float64.h"
#include "std_msgs/Float64MultiArray.h"
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Pose.h>
#include <control_msgs/GripperCommandActionGoal.h>
#include <sensor_msgs/JointState.h>


#include <kdl/chain.hpp>
#include <kdl/chainfksolverpos_recursive.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolverpos_nr.hpp>
#include <kdl/chainiksolverpos_nr_jl.hpp>
#include <kdl/chainjnttojacsolver.hpp>
#include <kdl/chainiksolvervel_pinv.hpp>
#include <kdl/kdl.hpp>
#include <kdl/jacobian.hpp>
#include <kdl/tree.hpp>
#include <kdl/segment.hpp>
#include <kdl/chainfksolver.hpp>
#include <kdl_parser/kdl_parser.hpp>
#include <kdl/frames_io.hpp>
#include <Eigen/Core>
#include <Eigen/Dense>
// #include <OsqpEigen/OsqpEigen.h>

using namespace KDL;
using namespace std;

#ifndef PI
# define PI 3.14159265358979323846
#endif

#ifndef PI_2
# define PI_2 PI/2
#endif

#ifndef PI_4
# define PI_4 PI/4
#endif

// std::array<double, 7> l_new_pose, r_new_pose, l_cur_joint, r_cur_joint;

// std::array<double, 7> r_sub_dq, r_sub_q;

// ros::Publisher pub_l_actual_pos, pub_r_actual_pos, pub_l_virtual_pos, pub_r_virtual_pos;

// KDL::Chain l_chain;
// KDL::Chain r_chain;

// Eigen::Matrix4d T_l_arm_Link7_l_tool_frame, T_r_arm_Link7_r_tool_frame, T_world_base_link;

// theta alpha a d
double DH[7][4] =
{
	{0, 0,  0 ,     -0.2066},
	{0,  1.5708,  0 ,     0    },
	{0,  1.5708,  0.0825, 0.316},
	{0, -1.5708, -0.0825, 0    },
	{0,  1.5708,  0,      0.384},
	{0,  1.5708,  0.088,  0    },
	{0 , 0,       0,      0.107}
};

Eigen::Matrix3d rpy2rotationMatrix(double yaw, double pitch, double roll) {
    // 绕 Z 轴的旋转矩阵 (偏航)
    Eigen::Matrix3d Rz;
    Rz << cos(yaw), -sin(yaw), 0,
          sin(yaw),  cos(yaw), 0,
          0,         0,        1;

    // 绕 Y 轴的旋转矩阵 (俯仰)
    Eigen::Matrix3d Ry;
    Ry << cos(pitch), 0, sin(pitch),
          0,           1, 0,
          -sin(pitch), 0, cos(pitch);

    // 绕 X 轴的旋转矩阵 (横滚)
    Eigen::Matrix3d Rx;
    Rx << 1, 0,          0,
          0, cos(roll), -sin(roll),
          0, sin(roll),  cos(roll);

    // 按 Z-Y-X 顺序组合旋转矩阵
    return Rz * Ry * Rx;
}

geometry_msgs::Pose convertMatrixToPose(const Eigen::Matrix4d& T_l_world_tool) {
    geometry_msgs::Pose msg_l_pose;

    // 提取平移部分
    msg_l_pose.position.x = T_l_world_tool(0, 3);
    msg_l_pose.position.y = T_l_world_tool(1, 3);
    msg_l_pose.position.z = T_l_world_tool(2, 3);

    // 提取旋转部分并转换为四元数
    Eigen::Matrix3d rotation_matrix = T_l_world_tool.block<3, 3>(0, 0);
    Eigen::Quaterniond quaternion(rotation_matrix);

    // 设置四元数到msg_l_pose
    msg_l_pose.orientation.w = quaternion.w();
    msg_l_pose.orientation.x = quaternion.x();
    msg_l_pose.orientation.y = quaternion.y();
    msg_l_pose.orientation.z = quaternion.z();

    return msg_l_pose; // 返回转换后的 Pose
}

void array_cout(std::string s, std::array<double, 7> joint){
	std::cout << s << ": ";                                             
    for (size_t i = 0; i < 7; i++)
    {
        std::cout << joint[i] << " ";
    }
    std::cout << std::endl;
}

double** Interp5rdPoly_Param_fcn(double* qp0, double* qv0, double* qa0, double* qpf, double* qvf, double* qaf, double  tf)
{
	const int M = sizeof(qp0[0]);
	double** aa = new double* [M];
	for (int i = 0; i < M; i++)
		aa[i] = new double[6];
	for (size_t i = 0; i < M; i++)
	{
		aa[i][0] = qp0[i];
		aa[i][1] = qv0[i];
		aa[i][2] = qa0[i] / 2.0;
		aa[i][3] = (20 * (qpf[i] - qp0[i]) - (8 * qvf[i] + 12 * qv0[i]) * tf + (qaf[i] - 3 * qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 3));
		aa[i][4] = (-30 * (qpf[i] - qp0[i]) + (14 * qvf[i] + 16 * qv0[i]) * tf - (2 * qaf[i] - 3 * qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 4));
		aa[i][5] = (12 * (qpf[i] - qp0[i]) - (6 * qvf[i] + 6 * qv0[i]) * tf + (qaf[i] - qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 5));
	}
	return aa;
}

double** Interp5rdPoly_Data_t_fcn(double** aa, double  tt, const int nn)
{
	const int M = 3;
	double** aaa = new double* [M];
	for (int i = 0; i < M; i++)
		aaa[i] = new double[nn];
	for (size_t i = 0; i < nn; i++)
	{
		aaa[0][i] = aa[i][0] + aa[i][1] * tt + aa[i][2] * pow(tt, 2) + aa[i][3] * pow(tt, 3) + aa[i][4] * pow(tt, 4) + aa[i][5] * pow(tt, 5);
		aaa[1][i] = aa[i][1] + 2 * aa[i][2] * tt + 3 * aa[i][3] * pow(tt, 2) + 4 * aa[i][4] * pow(tt, 3) + 5 * aa[i][5] * pow(tt, 4);
		aaa[2][i] = 2 * aa[i][2] + 6 * aa[i][3] * tt + 12 * aa[i][4] * pow(tt, 2) + 20 * aa[i][5] * pow(tt, 3);
	}
	return aaa;
}

std::array<double, 7> joint_plan(std::array<double, 7> start, std::array<double, 7> end, double t, double ct){

	double qp0[7] = { 0.0 };
	double qv0[7] = { 0.0 };
	double qa0[7] = { 0.0 };
	double qpf[7] = { 0.0 }; 
	double qvf[7] = { 0.0 };
	double qaf[7] = { 0.0 };
	const int nn = sizeof(qp0) / sizeof(qp0[0]);
    for (size_t i = 0; i < nn; i++)
    {
        qp0[i] = start[i];
        qpf[i] = end[i];      
    }
    double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t);
    double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
    std::array<double, 7> p;
    for (size_t i = 0; i < nn; i++)
    {
        p[i] = pva[0][i];    
    }
    return p;
}

std::vector<ros::Publisher> createPublishers(ros::NodeHandle& nh, const std::vector<std::string>& topic_names, int queue_size) { 
    std::vector<ros::Publisher> publishers; 
    for (const auto& topic_name : topic_names) { 
        ros::Publisher pub = nh.advertise<std_msgs::Float64>(topic_name, queue_size); 
        publishers.push_back(pub); 
    } 
    return publishers; 
} 

void pub_Publishers(std::vector<ros::Publisher> publishers, std::array<double, 7> l_pose, std::array<double, 7> r_pose) { 
    for (int i = 0; i < 7; i++) { 
        std_msgs::Float64 msg;
        msg.data = l_pose[i];
        publishers[i].publish(msg); 
    } 
    for (int i = 7; i < 14; i++) { 
        std_msgs::Float64 msg;
        msg.data = r_pose[i-7];
        publishers[i].publish(msg); 
    } 
} 

KDL::Chain createRobotChain_l() {
    KDL::Chain chain;
 	
 	chain.addSegment(KDL::Segment("l_link1", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, 0, -0.2066))));
    chain.addSegment(KDL::Segment("l_link1", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(0, 0, 0))));
 	chain.addSegment(KDL::Segment("l_link1", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link2", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.0416, 0, -0.047))));
    chain.addSegment(KDL::Segment("l_link2", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5708, 0, -1.5708))));
 	chain.addSegment(KDL::Segment("l_link2", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link3", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.049, 0, 0.041))));
    chain.addSegment(KDL::Segment("l_link3", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, 1.5708))));
 	chain.addSegment(KDL::Segment("l_link3", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link4", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(-0.041, 0, -0.1996))));
    chain.addSegment(KDL::Segment("l_link4", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5708, 0, 1.5708))));
 	chain.addSegment(KDL::Segment("l_link4", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link5", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, -0.1395, 0.041))));
    chain.addSegment(KDL::Segment("l_link5", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, 0))));
 	chain.addSegment(KDL::Segment("l_link5", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link6", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, 0.0295, -0.035))));
    chain.addSegment(KDL::Segment("l_link6", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5708, 0, 0))));
 	chain.addSegment(KDL::Segment("l_link6", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("l_link7", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.036, -0.07, 0.0295))));
    chain.addSegment(KDL::Segment("l_link7", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5708, 0, -1.5708))));
 	chain.addSegment(KDL::Segment("l_link7", KDL::Joint(KDL::Joint::RotZ)));

	unsigned int c_i = chain.getNrOfJoints();
    std::cout << "左臂关节数: " << c_i << std::endl;

    return chain;
}

KDL::Chain createRobotChain_r() {
    KDL::Chain chain;
 	
 	chain.addSegment(KDL::Segment("r_link1", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, 0, 0.2016))));
    chain.addSegment(KDL::Segment("r_link1", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(0, 0, 0))));
 	chain.addSegment(KDL::Segment("r_link1", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link2", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.0356, 0, 0.052))));
    chain.addSegment(KDL::Segment("r_link2", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, -1.5708))));
 	chain.addSegment(KDL::Segment("r_link2", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link3", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.049, 0, -0.035))));
    chain.addSegment(KDL::Segment("r_link3", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5708, 0, 1.5708))));
 	chain.addSegment(KDL::Segment("r_link3", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link4", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(-0.041, 0, 0.1996))));
    chain.addSegment(KDL::Segment("r_link4", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, 1.5708))));
 	chain.addSegment(KDL::Segment("r_link4", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link5", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, -0.1395, -0.0410000000000012))));
    chain.addSegment(KDL::Segment("r_link5", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(1.5707963267949, 0, 0))));
 	chain.addSegment(KDL::Segment("r_link5", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link6", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0, 0.0295, 0.035))));
    chain.addSegment(KDL::Segment("r_link6", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, 0))));
 	chain.addSegment(KDL::Segment("r_link6", KDL::Joint(KDL::Joint::RotZ)));

 	chain.addSegment(KDL::Segment("r_link7", KDL::Joint(KDL::Joint::None), KDL::Frame(KDL::Vector(0.036, -0.07, -0.0295))));
    chain.addSegment(KDL::Segment("r_link7", KDL::Joint(KDL::Joint::None),KDL::Frame(KDL::Rotation::RPY(-1.5708, 0, -1.5708))));
 	chain.addSegment(KDL::Segment("r_link7", KDL::Joint(KDL::Joint::RotZ)));

	unsigned int c_i = chain.getNrOfJoints();
    std::cout << "右臂关节数: " << c_i << std::endl;

    return chain;
}

Eigen::Vector3d tool_to_end(Eigen::Vector3d vector, Eigen::Quaterniond quat, Eigen::Matrix4d T_Link7_tool_frame){
    Eigen::Matrix4d T_base_tool_frame = Eigen::Matrix4d::Identity();
    T_base_tool_frame.block<3, 3>(0, 0) = quat.toRotationMatrix();
    T_base_tool_frame.block<3, 1>(0, 3) = vector;
    Eigen::Matrix4d T_base_Link7 = T_base_tool_frame * T_Link7_tool_frame.inverse();

    Eigen::Vector3d vector2(T_base_Link7(0,3),T_base_Link7(1,3),T_base_Link7(2,3));

    return vector2;

}

// KDL::Vector vector, KDL::Rotation rot 工具坐标系相对于世界坐标系的
std::array<double, 7> kdl_ik2( const KDL::Chain chain, std::array<double, 7> j, Eigen::Matrix4d T_arm_Link7_tool_frame, Eigen::Matrix4d T_world_arm_Link1, Eigen::Vector3d vector2, Eigen::Quaterniond quat2) {

    Eigen::Matrix4d T_world_tool_frame = Eigen::Matrix4d::Identity();
    T_world_tool_frame.block<3, 3>(0, 0) = quat2.toRotationMatrix();
    T_world_tool_frame.block<3, 1>(0, 3) = vector2;
    Eigen::Matrix4d T_arm_Link1_arm_Link7 = T_world_arm_Link1.inverse() * T_world_tool_frame * T_arm_Link7_tool_frame.inverse();
    // std::cout << "T_arm_Link1_arm_Link7: \n" << T_arm_Link1_arm_Link7 << std::endl;
    KDL::Vector vector = KDL::Vector(T_arm_Link1_arm_Link7(0,3),T_arm_Link1_arm_Link7(1,3),T_arm_Link1_arm_Link7(2,3));

    KDL::Rotation rot(  T_arm_Link1_arm_Link7(0,0),T_arm_Link1_arm_Link7(0,1),T_arm_Link1_arm_Link7(0,2),
                        T_arm_Link1_arm_Link7(1,0),T_arm_Link1_arm_Link7(1,1),T_arm_Link1_arm_Link7(1,2),
                        T_arm_Link1_arm_Link7(2,0),T_arm_Link1_arm_Link7(2,1),T_arm_Link1_arm_Link7(2,2));

    KDL::Frame cartpos =  KDL::Frame(rot,vector);
    double eps=1E-5;
    int maxiter=500;
    double eps_joints=1E-15;
    KDL::ChainIkSolverPos_LMA iksolver = KDL::ChainIkSolverPos_LMA(chain,eps,maxiter,eps_joints);

    KDL::JntArray jointpositions =  KDL::JntArray(7);
    KDL::JntArray jointGuesspositions =  KDL::JntArray(7);
    // std::array<double, 7> j{0.559821, -0.274264, 0.129788, 0.143365, 0.23375, 0.814764, 0.429983 };
    // std::array<double, 7> j{0.0008088791521467797, -0.7750040251651917, 0.0005362309290637413, -0.7848808401906231, -0.00212017783969376, 0.7865084320670688, 0.7867815167468368 };
    for (size_t i = 0; i < 7; i++)
    {
        jointGuesspositions(i) = j[i];
    }

    bool kinematics_status;
    kinematics_status = iksolver.CartToJnt(jointGuesspositions,cartpos,jointpositions);  //末端pose给定不变
	std::array<double, 7> result;
    if(kinematics_status >= 0)
    {
        for(int i=0;i<7;i++)
        {
			result[i] = jointpositions(i);
        	// std::cout << jointpositions(i) << std::endl;
        }
        // printf("%s \n","Success, thanks KDL!");
    }
    else
    {
        printf("%s \n","Error:could not calculate backword kinematics : ");
    }
	return result;
}

// KDL::Vector vector, KDL::Rotation rot 机械臂末端相对于base_link
std::array<double, 7> kdl_ik( const KDL::Chain chain, KDL::Vector vector, KDL::Rotation rot) {
    const std::string first_joint_name = chain.getSegment(0).getName();
    // if (first_joint_name == "r_link1")
    // {
    //     std::cout << "rrrrrrr" << std::endl;
    // }else{
    //     std::cout << "lllllll" << std::endl;
    // }
    
    KDL::Frame cartpos =  KDL::Frame(rot,vector);
    double eps=1E-5;
    int maxiter=500;
    double eps_joints=1E-15;
    KDL::ChainIkSolverPos_LMA iksolver = KDL::ChainIkSolverPos_LMA(chain,eps,maxiter,eps_joints);

    KDL::JntArray jointpositions =  KDL::JntArray(7);
    KDL::JntArray jointGuesspositions =  KDL::JntArray(7);
    // std::array<double, 7> j{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    std::array<double, 7> j{1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473 };
    // std::array<double, 7> j{0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
    for (size_t i = 0; i < 7; i++)
    {
        jointGuesspositions(i) = j[i];
    }

    bool kinematics_status;
    kinematics_status = iksolver.CartToJnt(jointGuesspositions,cartpos,jointpositions);  //末端pose给定不变
	std::array<double, 7> result;
    if(kinematics_status == 0)
    {
        for(int i=0;i<7;i++)
        {
			result[i] = jointpositions(i);
        	// std::cout << jointpositions(i) << std::endl;
        }
        // printf("%s \n","Success, thanks KDL!");
    }
    else
    {
        printf("%s \n","Error:could not calculate backword kinematics : ");
    }
    //加入关节限制
    MatrixXd theta_limit(7,2);
    // theta_limit << -PI, PI,
    //                 -PI*135/180, PI*5/180,
    //                 -PI/2, PI/2,
    //                 0, PI*110/180,
    //                 -PI, PI,
    //                 -PI/2, PI/2,
    //                 -PI/2, PI/2;
    theta_limit <<  -PI, PI,
                    -PI, PI*5/180,
                    -PI, PI,
                    -PI, PI,
                    -PI, PI,
                    -PI, PI,
                    -PI, PI;
    for(int i = 0; i < 7; i++){
        if (result[i] < theta_limit(i,0) || result[i] > theta_limit(i,1)){
            cout << "error: Joint" << i+1 << " " << result[i]*180/PI << " exceeds the limit position[" << theta_limit(i,0)*180/PI << ", " << theta_limit(i,1)*180/PI << ']' << endl;
        }
    }

	return result;
}

geometry_msgs::Pose convertKDLFrameToPose(const KDL::Frame& cartpos) {
    geometry_msgs::Pose car_pos;

    // 提取平移部分
    car_pos.position.x = cartpos.p.x();
    car_pos.position.y = cartpos.p.y();
    car_pos.position.z = cartpos.p.z();

    // 提取旋转部分并转换为四元数
    KDL::Rotation rotation = cartpos.M;
    double x, y, z, w;
    rotation.GetQuaternion(x, y, z, w); // 获取四元数分量

    car_pos.orientation.x = x;
    car_pos.orientation.y = y;
    car_pos.orientation.z = z;
    car_pos.orientation.w = w;

    return car_pos; // 返回转换后的 Pose
}

geometry_msgs::Pose kdl_fk( const KDL::Chain chain, std::array<double, 7> joint){
    // Create solver based on kinematic chain
    ChainFkSolverPos_recursive fksolver = ChainFkSolverPos_recursive(chain);

    // Create joint array
    unsigned int nj = chain.getNrOfJoints();
    KDL::JntArray jointpositions = JntArray(nj);

    // Assign some values to the joint positions
    for( int i=0; i<nj; i++){
        jointpositions(i) = joint[i];
    }

    // Create the frame that will contain the results
    KDL::Frame cartpos;

    // Calculate forward position kinematics
    bool kinematics_status = fksolver.JntToCart(jointpositions,cartpos);
	std::array<double, 7> result;
    if(kinematics_status >= 0){
        // std::cout << cartpos <<std::endl;
        // printf("%s \n","Succes, thanks KDL!");
    }else{
        printf("%s \n","Error: could not calculate forward kinematics :(");
    }
    geometry_msgs::Pose car_pos = convertKDLFrameToPose(cartpos);
    
	return car_pos;
}

// void fk_pose( std::array<double, 7> l_cur_joint, std::array<double, 7> r_cur_joint, geometry_msgs::Pose& msg_l_pose, geometry_msgs::Pose& msg_r_pose){
//     geometry_msgs::Pose l_cartpos = kdl_fk(l_chain, l_cur_joint);//base坐标系
//     geometry_msgs::Pose r_cartpos = kdl_fk(r_chain, r_cur_joint);

//     Eigen::Quaterniond l_quat(l_cartpos.orientation.w, l_cartpos.orientation.x, l_cartpos.orientation.y, l_cartpos.orientation.z);
//     Eigen::Vector3d l_vector(l_cartpos.position.x, l_cartpos.position.y, l_cartpos.position.z);
//     Eigen::Quaterniond r_quat(r_cartpos.orientation.w, r_cartpos.orientation.x, r_cartpos.orientation.y, r_cartpos.orientation.z);
//     Eigen::Vector3d r_vector(r_cartpos.position.x, r_cartpos.position.y, r_cartpos.position.z);
   
//     Eigen::Matrix4d T_l_Link1_Link7 = Eigen::Matrix4d::Identity();
//     T_l_Link1_Link7.block<3, 3>(0, 0) = l_quat.toRotationMatrix();
//     T_l_Link1_Link7.block<3, 1>(0, 3) = l_vector;

//     Eigen::Matrix4d T_r_Link1_Link7 = Eigen::Matrix4d::Identity();
//     T_r_Link1_Link7.block<3, 3>(0, 0) = r_quat.toRotationMatrix();
//     T_r_Link1_Link7.block<3, 1>(0, 3) = r_vector;

//     Eigen::Matrix4d T_l_world_tool = T_world_base_link * T_l_Link1_Link7 * T_l_arm_Link7_l_tool_frame;
//     Eigen::Matrix4d T_r_world_tool = T_world_base_link * T_r_Link1_Link7 * T_r_arm_Link7_r_tool_frame;

//     msg_l_pose = convertMatrixToPose(T_l_world_tool);//世界坐标系
//     msg_r_pose = convertMatrixToPose(T_r_world_tool);
    
// }

Eigen::VectorXd admittance_ctrl_REI_classical(Eigen::VectorXd fd, Eigen::VectorXd fe, double dt)
{
    static Eigen::VectorXd x = Eigen::MatrixXd::Zero(6,1);
    static Eigen::VectorXd dx = Eigen::MatrixXd::Zero(6,1);
    static Eigen::VectorXd sum = Eigen::MatrixXd::Zero(6,1);
    Eigen::VectorXd Me(6), Be(6), ddx(6); 
    Me << 10, 10, 10, 10, 10, 10;
    Be << 20, 20, 20, 20, 20, 20;//650
    

    double ki = 0.00000000000000001;  // 0.005727;//0.4275 
    sum = sum + (fe - fd) * dt;
    ddx = (fe - fd + 0 * sum).cwiseQuotient(Me) - Be.cwiseProduct(dx).cwiseQuotient(Me);
    // std::cout<<"ddxe"<<std::endl<<ddx(2)<<std::endl;
    dx = dx + ddx * dt;
    // std::cout<<"dxe"<<std::endl<<dx(2)<<std::endl;
    x = x + dx * dt;
    // std::cout<<"xe"<<std::endl<<x(2)<<std::endl;
    return x;
}

Eigen::VectorXd f_push(double t)
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double dd = -0.0016 * t;
    a << 0, 0, 0, 0, 0, 0;
    b << 0, 0, 0, 0, 0, 0;
    c << 0, 0, 0, 0, 0, 0; 
    d << 0, 0, dd, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_rub(double t)//摩擦
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/4;//频率
    double aa = PI/6;
    a << 0, 0, 0, 0, 0, aa;
    b << 0, 0, 0, 0, 0, v;
    c << 0, 0, 0, 0, 0, 0; 
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_wiggle(double t)//摆动
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/2;//频率
    // double aa = PI/10;
    double aa = 0.005*t;//0.03
    a << 0, 0, 0, aa, aa, 0;
    b << 0, 0, 0, v, v, 0;
    c << 0, 0, 0, 0, PI_2, 0; 
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_spiral(double t)//螺旋
{       
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/2;//频率 PI
    // double aa = 0.25*sin(PI_2/20*t) + 0.05;
    double aa = 0.00065*t;//0.0003*t
    a << aa, aa, 0, 0, 0, 0;
    b << v, v, 0, 0, 0, 0;
    c << -0, PI_2, 0, 0, 0, 0; //0, PI_2, 0, 0, 0, 0
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

// 函数：计算并输出末端执行器速度
VectorXd computeEndVelocity(KDL::Chain chain, VectorXd jnt_pos_current, VectorXd jnt_vel_current) {
    int n = chain.getNrOfJoints();
    KDL::JntArray joint_angles(n);
    for (size_t i = 0; i < n; i++) {
        joint_angles(i) = jnt_pos_current[i];
    }
    // 创建用于存储雅可比矩阵的 KDL::Jacobian 对象
    KDL::Jacobian jacobian(n);

    // 创建正向运动学求解器
    KDL::ChainJntToJacSolver jnt_to_jac_solver(chain);

    // 计算雅可比矩阵
    jnt_to_jac_solver.JntToJac(joint_angles, jacobian);


    // 计算末端执行器速度 (6D 向量，包含线速度和角速度)
    VectorXd car_vel_current(6);
    MatrixXd jacobian_matrix = jacobian.data;

    car_vel_current = jacobian_matrix * jnt_vel_current;

    return car_vel_current;
    
}
