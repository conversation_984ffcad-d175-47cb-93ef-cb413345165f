cmake_minimum_required(VERSION 3.0.2)
project(assembly_control)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  serial
  visualization_msgs
)

catkin_package(
 INCLUDE_DIRS include
 LIBRARIES
#  CATKIN_DEPENDS message_generation roscpp serial std_msgs visualization_msgs
 DEPENDS roscpp
)

#############
## EIGEN3 ##
#############
find_package(Eigen3 REQUIRED)
include_directories(${EIGEN3_INCLUDE_DIRS})

#########
## COAL ##
#########
# Using coal library instead of hpp-fcl
# 设置conda环境路径
#set(CONDA_ENV_PATH $ENV{HOME}/anaconda3/envs/coal)
set(CONDA_ENV_PATH /root/miniconda3/envs/coal)
# 添加conda环境中的库路径
list(APPEND CMAKE_PREFIX_PATH ${CONDA_ENV_PATH})
# 查找coal库
find_package(coal REQUIRED)

include_directories(
include
  ${catkin_INCLUDE_DIRS}
  ${CONDA_ENV_PATH}/include
  ${CONDA_ENV_PATH}/include/coal
)


# 设置运行时库路径（RPATH）
set(CMAKE_INSTALL_RPATH "${CONDA_ENV_PATH}/lib")
#########
## KDL ##
#########
find_package(orocos_kdl REQUIRED)
include_directories(${orocos_kdl_INCLUDE_DIRS})
find_package(kdl_parser REQUIRED)
include_directories(${kdl_parser_INCLUDE_DIRS})

find_package(Python3 COMPONENTS Development NumPy)

find_package(orocos_kdl REQUIRED)

link_directories(${PCL_LIBRARY_DIRS})

catkin_package(
  INCLUDE_DIRS include
  LIBRARIES def_lib2
#  CATKIN_DEPENDS roscpp rospy std_msgs
#  DEPENDS system_lib
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  #${EIGEN3_INCLUDE_DIR}
)
include_directories(/usr/include/eigen3)

## Declare a C++ library
# add_library(${PROJECT_NAME}
#   src/${PROJECT_NAME}/assembly_control.cpp
# )
add_library(def_lib2
  include/assembly_control/Def_Struct.h
  include/assembly_control/Def_Class.h
  src/lib01_SubFunc.cpp
  src/lib02_PlanFunc.cpp
  src/lib03_PreDefinedActions.cpp
)

## Declare a C++ library
add_library(arms_gen2_control_lib
  utils/DataProc_Callback.cpp
  utils/DataProc_DataPrint.cpp
  utils/DataProc_DataSave.cpp
  utils/DataProc_Init.cpp
  utils/DataProc_InspireHand.cpp
  utils/DataProc_Move.cpp
  utils/Kinematics_Solver.cpp
  utils/Kinematics_Trans.cpp
  utils/AnomDet_Detection.cpp
  utils/AnomDet_HppFcl.cpp
  utils/TrajPlan_Adjust.cpp
  utils/TrajPlan_DMP.cpp
  utils/TrajPlan_Inter.cpp
)

target_link_libraries(def_lib2
  ${catkin_LIBRARIES}
  ${orocos_kdl_LIBRARIES}
  ${kdl_parser_LIBRARIES}
  ${COAL_LIBRARIES}
)

add_executable(Action10_assembly src/Action10_assembly.cpp)
target_link_libraries(Action10_assembly
  def_lib2
  arms_gen2_control_lib
)
add_executable(hand_test src/hand_test.cpp)
target_link_libraries(hand_test
  def_lib2
  arms_gen2_control_lib
)

