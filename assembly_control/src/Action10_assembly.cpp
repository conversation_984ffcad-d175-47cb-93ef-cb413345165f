#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Int32.h>
#include <std_msgs/Bool.h>

#include "assembly_control/Def_Class.h"
#include "assembly_control/assembly.h" 



void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_pos_current_l[i] = msg->position[i];
        // jnt_vel_current_l[i] = msg->velocity[i];
        jnt_pos_current_r[i] = msg->position[i+7];
        // jnt_vel_current_r[i] = msg->velocity[i+7];
        jnt_vel_current_l[i] = (jnt_pos_current_l[i] - jnt_pos_last_l[i]) / dt;
        jnt_vel_current_r[i] = (jnt_pos_current_r[i] - jnt_pos_last_r[i]) / dt;
        // 更新前一个位置
        jnt_pos_last_l[i] = jnt_pos_current_l[i];
        jnt_pos_last_r[i] = jnt_pos_current_r[i];
    }
    //关节位置校验
    MatrixXd theta_limit(7,2);
    theta_limit << -M_PI, M_PI,
                    -M_PI, M_PI*20/180,
                    -M_PI, M_PI,
                    -1, 130*M_PI/180,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI;
    for(int i = 0; i < 7; i++){
        if (jnt_pos_current_l[i] < theta_limit(i,0) || jnt_pos_current_l[i] > theta_limit(i,1)){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_l[i]*180/PI << " exceeds the limit position[" << theta_limit(i,0)*180/PI << ", " << theta_limit(i,1)*180/PI << ']' << endl;
            return;
        }
        if (jnt_pos_current_r[i] < theta_limit(i,0) || jnt_pos_current_r[i] > theta_limit(i,1)){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_r[i]*180/PI << " exceeds the limit position[" << theta_limit(i,0)*180/PI << ", " << theta_limit(i,1)*180/PI << ']' << endl;
            return;
        }
    }
    // car_vel_current_l = computeEndVelocity(l_chain, jnt_pos_current_l, jnt_vel_current_l);
    // car_vel_current_r = computeEndVelocity(r_chain, jnt_pos_current_r, jnt_vel_current_r);

    // std_msgs::Float64MultiArray car_msg;
    // car_msg.data.resize(12);
    // for (size_t i = 0; i < 6; i++)
    // {
    //     car_msg.data[i] = car_vel_current_l[i];
    //     car_msg.data[i+6] = car_vel_current_r[i];
    // }
    
    // car_act_vel_pub.publish(car_msg);

    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

void jointCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_pos_vir_r[i] = msg->position[i];
        jnt_vel_vir_r[i] = msg->velocity[i];
        jnt_pos_vir_l[i] = msg->position[i+7];
        jnt_vel_vir_l[i] = msg->velocity[i+7];
    }
    car_pos_vir_l = kdl_fk(l_chain, jnt_pos_vir_l);
    car_pos_vir_r = kdl_fk(r_chain, jnt_pos_vir_r);
    for (size_t i = 0; i < 6; i++)
    {
        car_vel_vir_l[i] = (car_pos_vir_l[i] - car_pos_vir_last_l[i]) / dt;
        car_vel_vir_r[i] = (car_pos_vir_r[i] - car_pos_vir_last_r[i]) / dt;
        // 更新前一个位置
        car_pos_vir_last_l[i] = car_pos_vir_l[i];
        car_pos_vir_last_r[i] = car_pos_vir_r[i];
    }

    // car_vel_vir_l = computeEndVelocity(l_chain, jnt_pos_vir_l, jnt_vel_vir_l);
    // car_vel_vir_r = computeEndVelocity(r_chain, jnt_pos_vir_r, jnt_vel_vir_r);

    std_msgs::Float64MultiArray car_pos_msg, car_vel_msg;
    car_pos_msg.data.resize(12);
    car_vel_msg.data.resize(12);
    for (size_t i = 0; i < 6; i++)
    {
        car_pos_msg.data[i] = car_pos_vir_l[i];
        car_pos_msg.data[i+6] = car_pos_vir_r[i];
        car_vel_msg.data[i] = car_vel_vir_l[i];
        car_vel_msg.data[i+6] = car_vel_vir_r[i];
    }
    //修正跳变
    car_pos_msg.data[11] = car_pos_msg.data[11] - car_pos_msg.data[9] + PI_2;

    car_vir_pos_pub.publish(car_pos_msg);
    car_vir_vel_pub.publish(car_vel_msg);

    car_pos_current_l = kdl_fk(l_chain, jnt_pos_current_l);
    car_pos_current_r = kdl_fk(r_chain, jnt_pos_current_r);
    for (size_t i = 0; i < 6; i++)
    {
        car_vel_current_l[i] = (car_pos_current_l[i] - car_pos_current_last_l[i]) / dt;
        car_vel_current_r[i] = (car_pos_current_r[i] - car_pos_current_last_r[i]) / dt;
        // 更新前一个位置
        car_pos_current_last_l[i] = car_pos_current_l[i];
        car_pos_current_last_r[i] = car_pos_current_r[i];
    }

    std_msgs::Float64MultiArray car_pos_act_msg, car_vel_act_msg;
    car_pos_act_msg.data.resize(12);
    car_vel_act_msg.data.resize(12);

    for (size_t i = 0; i < 6; i++)
    {
        car_pos_act_msg.data[i] = car_pos_current_l[i];
        car_pos_act_msg.data[i+6] = car_pos_current_r[i];
        car_vel_act_msg.data[i] = car_vel_current_l[i];
        car_vel_act_msg.data[i+6] = car_vel_current_r[i];
    }
    //修正跳变
    car_pos_act_msg.data[11] = car_pos_act_msg.data[11] - car_pos_act_msg.data[9] + PI_2;

    car_act_pos_pub.publish(car_pos_act_msg);
    car_act_vel_pub.publish(car_vel_act_msg);

    std_msgs::Float64MultiArray car_pos_err_msg;
    car_pos_err_msg.data.resize(12);
    for (size_t i = 0; i < 6; i++)
    {
        car_pos_err_msg.data[i] = (car_pos_vir_l[i] - car_pos_current_l[i])*1000;
        car_pos_err_msg.data[i+6] = (car_pos_vir_r[i] - car_pos_current_r[i])*1000;
    }
    car_err_pos_pub.publish(car_pos_err_msg);
    // cout << "car_pos_act_msg: " << car_pos_err_msg.data[7] << endl;

    return;
}


int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action10_assembly");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);
    ros::Subscriber joint_sub = nh.subscribe("joint_states",10,jointCallback);
    // ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);

    car_vir_vel_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_vel/vir",1);
    car_act_vel_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_vel/act",1);
    car_vir_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/vir",1);
    car_act_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/act",1);
    car_err_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/error",1);
    // ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);

    pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);

	//启用多线程
 	ros::AsyncSpinner spinner(6); // Use 2 threads
 	spinner.start(); //开始标志

    // kdl库初始化
    l_chain = createRobotChain_l();
    r_chain = createRobotChain_r();

    //坐标系初始化
    Eigen::Matrix4d T_l_Link7_tool = Eigen::Matrix4d::Identity();
    T_l_Link7_tool.block<3, 3>(0, 0) = Eigen::Quaterniond(0.7071068, 0.000, 0.7071068, 0.000).toRotationMatrix(); //(w,x,y,z)
    T_l_Link7_tool.block<3, 1>(0, 3) = Eigen::Vector3d(0.175, 0.000, 0.036);
    // std::cout << "4x4 变换矩阵 (包含平移向量): \n" << T_l_arm_Link7_l_tool_frame << std::endl;

    Eigen::Matrix4d T_r_Link7_tool = Eigen::Matrix4d::Identity();
    T_r_Link7_tool.block<3, 3>(0, 0) = Eigen::Quaterniond(0.000, 0.7071068, -0.000, 0.7071068).toRotationMatrix();
    T_r_Link7_tool.block<3, 1>(0, 3) = Eigen::Vector3d(0.175, -0.000, -0.036);

    jnt_pos_current_r = VectorXd::Zero(7);
    jnt_pos_current_l = VectorXd::Zero(7);
    jnt_pos_last_r = VectorXd::Zero(7);
    jnt_pos_last_l = VectorXd::Zero(7);     
    jnt_vel_current_r = VectorXd::Zero(7);
    jnt_vel_current_l = VectorXd::Zero(7);
    jnt_effort_r = VectorXd::Zero(7);
    jnt_effort_l = VectorXd::Zero(7);
    // 初始化为 6 维向量并赋值为 0
    car_vel_current_r = VectorXd::Zero(6);
    car_vel_current_l = VectorXd::Zero(6);
    car_pos_current_r = VectorXd::Zero(6);
    car_pos_current_l = VectorXd::Zero(6);
    car_pos_vir_r = VectorXd::Zero(6);
    car_pos_vir_l = VectorXd::Zero(6);    
    // 初始化虚拟关节角度和速度向量为 0
    jnt_pos_vir_r = VectorXd::Zero(7);
    jnt_pos_vir_l = VectorXd::Zero(7);
    jnt_vel_vir_r = VectorXd::Zero(7);
    jnt_vel_vir_l = VectorXd::Zero(7);

    // 初始化虚拟车速为 0
    car_vel_vir_r = VectorXd::Zero(6);
    car_vel_vir_l = VectorXd::Zero(6);

    // 轨迹点集合
    Plan_Res_MulDOF action_r;
    Plan_Res_MulDOF action_l;

    /*
    ctrl_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
   CTRL_MODE ctrl_mode = robot_ctrl;

    /*
    set init position
    */ 
    // //左臂
    // std::array<double, 7> l_jnt_init = {0,-PI*5/180,0,0,0,0,0};
    // //右臂
    // std::array<double, 7> r_jnt_init = {0,-PI*5/180,0,0,0,0,0};

    //左臂
    std::array<double, 7> r_jnt_init = {1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473};
    //右臂
    std::array<double, 7> l_jnt_init = {0.7850279760118953, -0.41392808296576633, -1.0509720167472572, 1.2498451869282914, 0.8618114725147112, 0.10562896424217197, 0.8883516196539216};

    /*
    set ready position
    */
    //左臂
    std::array<double, 7> l_jnt_ready = {-0.15, -0.28, -0.0, 1.5, 0.28, 0.32, 0.0};
    //右臂
    std::array<double, 7> r_jnt_ready = {-0.15, -0.28, -0.0, 1.5, 0.28, 0.32, 0.0};
    /*
    get grasp position
    */ 
    //左臂
    std::array<double, 7> l_jnt_grasp = {0,-PI*5/180,0,0,0,0,0};
    //右臂
    std::array<double, 7> r_jnt_grasp = {0,-PI*5/180,0,0,0,0,0};

    /*
    获取初始位置
    */
    int istest = 0;
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    if(istest == 1){
    }else{
        while(!flag && ros::ok())
        {
            ROS_INFO("waiting for initial position");
            usleep(1000);
            // ros::spinOnce();
        }
        for (size_t i = 0; i < 7; i++)
        {
           l_jnt_init[i] = jnt_pos_current_l[i];
           r_jnt_init[i] = jnt_pos_current_r[i];
        }
        array_cout("l_jnt_init", l_jnt_init);
        array_cout("r_jnt_init", r_jnt_init);
        
    }

    /*
    运动到装配位置
    */
    std::array<double, 7> r_grasp1 = kdl_ik(r_chain,  KDL::Vector(0.35+0.01 -0.025, 0.05 , 0.13+0.01),  
                                                     KDL::Rotation::RPY(-PI_2, PI_2, 0));// roll, pitch, yaw
    std::array<double, 7> r_grasp2 = kdl_ik(r_chain,  KDL::Vector(0.35, 0.005, 0.254),  
                                                     KDL::Rotation::RPY(-PI_2, PI_2, 0));
    std::array<double, 7> r_grasp0 = kdl_ik(r_chain,  KDL::Vector(0.35, 0.05, 0.2),  
                                                     KDL::Rotation::RPY(-PI_2, PI_2, 0)); 
                                                                                                     
    std::vector<std::array<double, 7>> qp_grasp;
    qp_grasp.push_back(r_jnt_init);
    // qp_grasp.push_back(r_grasp0);
    qp_grasp.push_back(r_grasp1);
    // qp_grasp.push_back(r_grasp2);
    std::vector<double> t_grasp = {2};
    action_r = planLib.multi_joint_plan(qp_grasp, t_grasp, dt);

    std::array<double, 7> l_grasp1 = kdl_ik(l_chain,  KDL::Vector(0.35-0.01, -0.15, -0.13),  
                                                     KDL::Rotation::RPY(-PI_2, -PI_2, -PI));
    std::array<double, 7> l_grasp2 = kdl_ik(l_chain,  KDL::Vector(0.35, 0.005, -0.254),  
                                                     KDL::Rotation::RPY(-PI_2, -PI_2, -PI));
    std::array<double, 7> l_grasp0 = kdl_ik(l_chain,  KDL::Vector(0.35, -0.15, -0.2),  
                                                     KDL::Rotation::RPY(-PI_2, -PI_2, -PI));                                                     
    // std::array<double, 7> l_grasp3 = kdl_ik(l_chain,  KDL::Vector(0.45, -0.2, -0.15),  
    //                                                 KDL::Rotation::RPY(PI_2, 0, 0));
    // array_cout("l_grasp0", l_grasp0);   
    // array_cout("r_grasp0", r_grasp0);   
                                       
    std::vector<std::array<double, 7>> l_qp_grasp;
    l_qp_grasp.push_back(l_jnt_init);
    // l_qp_grasp.push_back(l_grasp0);
    l_qp_grasp.push_back(l_grasp1);
    // l_qp_grasp.push_back(l_grasp2);
    action_l = planLib.multi_joint_plan(l_qp_grasp, t_grasp, dt);
    
    ROS_INFO("运动到装配位置.....");
    dataSave.save2txt(action_r,action_l,fpWrite);
    pub_motors(action_r, action_l,ctrl_mode,0);
    ROS_INFO("运动完成!");
    /*
    开始装配
    */
    // sleep(1);
    //接近
    Eigen::Vector3d p1(0.35+0.01 -0.025, 0.05 , 0.13+0.01);  
    Eigen::Vector3d rpy1(-PI_2, PI_2, 0); // roll, pitch, yaw
    action_r = planLib.assembly1(p1, rpy1, r_chain, 20, dt, 1); // 13.5
    
    int n_assembly1 = action_r.t.size();
    Eigen::VectorXd vec(7);
    std::copy(l_grasp1.begin(), l_grasp1.end(), vec.data());
    action_l = interLib.stay_still(vec, n_assembly1, dt);

    ROS_INFO("接近.....");
    dataSave.save2txt(action_r,action_l,fpWrite);
    pub_motors(action_r, action_l,ctrl_mode,1);
    ROS_INFO("接近完成!");

    // return 0;

    //螺旋搜索
    // sleep(1);
    /*获取当前位置*/
    VectorXd car_pos_assembly2;

    car_pos_assembly2 = kdl_fk(r_chain, jnt_pos_vir_r);
    std::cout << "car_pos_assembly2: " << car_pos_assembly2 << std::endl;

    Eigen::Vector3d p2(car_pos_assembly2[0], car_pos_assembly2[1], car_pos_assembly2[2]);  
    Eigen::Vector3d rpy2(car_pos_assembly2[5], car_pos_assembly2[4], car_pos_assembly2[3]); // roll, pitch, yaw
    
    action_r = planLib.assembly1(p2, rpy2, r_chain, 50, dt, 2);
    int n_assembly2 = action_r.t.size();
    action_l = interLib.stay_still(vec, n_assembly2, dt);

    ROS_INFO("搜索.....");
    dataSave.save2txt(action_r,action_l,fpWrite);
    pub_motors(action_r, action_l,ctrl_mode,2);
    ROS_INFO("搜索完成!");

    // return 0;

   //对齐
    // sleep(1);
    // /*获取当前位置*/
    // VectorXd car_pos_assembly3;

    // car_pos_assembly3 = kdl_fk(r_chain, jnt_pos_vir_r);
    // std::cout << "car_pos_assembly3: " << car_pos_assembly3 << std::endl;

    // Eigen::Vector3d p3(car_pos_assembly3[0], car_pos_assembly3[1], car_pos_assembly3[2]);  
    // Eigen::Vector3d rpy3(car_pos_assembly3[5], car_pos_assembly3[4], car_pos_assembly3[3]); 
    
    // action_r = planLib.assembly1(p3, rpy3, r_chain, 4, dt, 3); //5
    // int n_assembly3 = action_r.t.size();
    // action_l = interLib.stay_still(vec, n_assembly3, dt);

    // ROS_INFO("对齐.....");
    // dataSave.save2txt(action_r,action_l,fpWrite);
    // pub_motors(action_r, action_l,ctrl_mode,3);
    // ROS_INFO("对齐完成!");
    // return 0;
    //插入
    // sleep(1);
    VectorXd car_pos_assembly4;
    car_pos_assembly4 = kdl_fk(r_chain, jnt_pos_vir_r); 
    std::cout << "car_pos_assembly4: " << car_pos_assembly4 << std::endl;
    Eigen::Vector3d p4(car_pos_assembly4[0], car_pos_assembly4[1], car_pos_assembly4[2]);  
    Eigen::Vector3d rpy4(car_pos_assembly4[5], car_pos_assembly4[4], car_pos_assembly4[3]);

    action_r = planLib.assembly1(p4, rpy4, r_chain, 7, dt, 4);//10
    
    int n_assembly4 = action_r.t.size();
    action_l = interLib.stay_still(vec, n_assembly4, dt);

    ROS_INFO("插入.....");
    dataSave.save2txt(action_r,action_l,fpWrite);
    pub_motors(action_r, action_l,ctrl_mode, 4);
    ROS_INFO("插入完成!");

    // 要执行的命令
    std::string cmd_str = "gnome-terminal -- bash -c 'cd ~/robot_arm_v2/; source ./devel/setup.bash; rosrun robot_control grasp_f; exec bash'";
    
    // 执行系统命令
    int result = system(cmd_str.c_str());
    
    if (result == 0) {
        ROS_INFO("Terminal launched successfully.");
    } else {
        ROS_ERROR("Failed to launch terminal. Error code: %d", result);
    }

    return 0;
}
