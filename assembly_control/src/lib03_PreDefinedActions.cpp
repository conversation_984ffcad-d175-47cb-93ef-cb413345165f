#include "string"
#include <stdio.h>
#include <iostream>
#include <fstream>

#include "assembly_control/Def_Class.h"
#include "assembly_control/test1.h"

// #define PI 3.141592654

using namespace std;

TRAJ_PLAN::DMP dmpPlan;
DATA_PROC_FUNC::DATA_PUB dataPub;

namespace ACT_LIB{
    Plan_Res_MulDOF DMP::action1_dmp(Vector3d position, Vector3d pose, double dt, double tau, bool useSelfDefPhi, double phi, bool ikine_info_show, bool plan_info_show){
        string path1 = "./src/arms_gen2_control/data/demon_traj/Motion1_JntPos_v2.txt";
        string path2 = "./src/arms_gen2_control/data/demon_traj/Motion1_AngArm_v2.txt";

        VectorXd theta_dmp_init(7);
        theta_dmp_init << 0,-PI*5/180,0,0,0,0,0;

        Train_Par trainPar;
        trainPar.nbfs = 50;
        trainPar.k = 7;
        trainPar.alpha_y = 10;
        trainPar.k_f = 1;

        Vector4d jntLimit;
        jntLimit << -45,
                    -35,
                    30,
                    111;
        
        Plan_Res_MulDOF res = dmpPlan.dmp(path1,path2,trainPar,theta_dmp_init,position,pose,jntLimit,dt,tau,useSelfDefPhi,phi,ikine_info_show,plan_info_show);
        return res;
    }

    Plan_Res_MulDOF INTER::stay_still(VectorXd jnt_pos, int n, double dt){
        VectorXd t(n);
        MatrixXd pos(n,7);
        MatrixXd vel = MatrixXd::Zero(n,7);
        MatrixXd acc = MatrixXd::Zero(n,7);
        for(int i = 0; i < n; i++){
            t(i) = i*dt;
            pos.row(i) = jnt_pos;
        }

        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = pos;
        res.vel = vel;
        res.acc = acc;
        return res;
    }

    // Plan_Res_MulDOF INTERPOLATION::cart_quinitic_poly_inter(MatrixXd position, MatrixXd pose){
    // }

    void MOVE::move(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, int move_mode){
        switch(move_mode){
            case 0:
                break;
            case 1:
                dataPub.pub2rviz(motion_r, motion_l);
                break;
            case 2:
                dataPub.pub2gazebo(motion_r, motion_l);
                break;
            case 3:
                dataPub.pub2motors(motion_r, motion_l);
                break;       
        }
    }

    Plan_Res_MulDOF PLAN::joint_plan(std::array<double, 7> start, std::array<double, 7> end, double t, double dt){
        double qp0[7] = { 0.0 };
        double qv0[7] = { 0.0 };
        double qa0[7] = { 0.0 };
        double qpf[7] = { 0.0 }; 
        double qvf[7] = { 0.0 };
        double qaf[7] = { 0.0 };
        const int nn = sizeof(qp0) / sizeof(qp0[0]);
        for (size_t i = 0; i < nn; i++)
        {
            qp0[i] = start[i];
            qpf[i] = end[i];      
        }
        int num = std::round(t / dt);
        MatrixXd x = MatrixXd::Zero(num,nn);
        MatrixXd dx = MatrixXd::Zero(num,nn);
        MatrixXd ddx = MatrixXd::Zero(num,nn);
        VectorXd time(num);
        double ct = dt;

        for (size_t i = 0; i < num; i++)
        {
            double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t);
            double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
            for (size_t j = 0; j < nn; j++)
            {
                x(i,j) = pva[0][j];    
                dx(i,j) = pva[1][j];
                ddx(i,j) = pva[2][j];
            }
            time[i] = ct;
            ct += dt;
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = 0;
        return res;
    }

    Plan_Res_MulDOF PLAN::multi_joint_plan(vector<std::array<double, 7>> qp, vector<double> t, double dt){
        int n = qp.size();
       
        vector<std::array<double, 7>> qv;
        vector<std::array<double, 7>> qa;

        //速度都设为相邻速度的最小值
        std::array<double, 7> qv0 = {0, 0, 0 ,0 ,0 ,0 , 0};
        qv.push_back(qv0);
        for (size_t i = 1; i < n - 1; i++)
        {
            std::array<double, 7> qp_l = qp[i-1];
            std::array<double, 7> qp_c = qp[i];
            std::array<double, 7> qp_r = qp[i+1];
            std::array<double, 7> qvx;
            for (size_t j = 0; j < 7; j++)
            {
                double l = (qp_c[j] - qp_l[j])/t[i-1]*2;
                double r = (qp_r[j] - qp_c[j])/t[i]*2;
                if( l*r <= 0){
                    qvx[j] = 0;
                }else if(l > 0){
                    qvx[j] = min(l, r);
                }else if(l < 0){
                    qvx[j] = max(l, r);
                }
            }
            qv.push_back(qvx);
            // array_cout("qvx", qvx);
            
        }
        qv.push_back(qv0);        

        //加速度都设为0
        for (size_t i = 0; i < n; i++)
        {
           std::array<double, 7> qax = {0, 0, 0 ,0 ,0 ,0 , 0};
           qa.push_back(qax);
        }
        double tt = 0.0;
        for (size_t i = 0; i < t.size(); i++)
        {
            tt += t[i];
        }
        
        int sum = std::round(tt / dt);
        MatrixXd x = MatrixXd::Zero(sum,7);
        MatrixXd dx = MatrixXd::Zero(sum,7);
        MatrixXd ddx = MatrixXd::Zero(sum,7);
        VectorXd time(sum);
        double t_c = dt;
        int ii = 0;
        for (size_t i = 0; i < t.size(); i++)
        {
            std::array<double, 7> p0 = qp[i];
            std::array<double, 7> v0 = qv[i];
            std::array<double, 7> a0 = qa[i];
            std::array<double, 7> p1 = qp[i+1];
            std::array<double, 7> v1 = qv[i+1];
            std::array<double, 7> a1 = qa[i+1];
            double qp0[7];
            double qv0[7];
            double qa0[7];
            double qpf[7];
            double qvf[7];
            double qaf[7];
            std::copy(p0.begin(), p0.end(), qp0);
            std::copy(v0.begin(), v0.end(), qv0);
            std::copy(a0.begin(), a0.end(), qa0);
            std::copy(p1.begin(), p1.end(), qpf);
            std::copy(v1.begin(), v1.end(), qvf);
            std::copy(a1.begin(), a1.end(), qaf);

            double ct = dt;
            int num = std::round(t[i] / dt);
            for (size_t k = 0; k < num; k++)
            {
                double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t[i]);
                double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , 7);
                for (size_t j = 0; j < 7; j++)
                {
                    x(ii,j) = pva[0][j];    
                    dx(ii,j) = pva[1][j];
                    ddx(ii,j) = pva[2][j];
                }
                time[ii] = t_c;
                ct += dt;
                t_c += dt;
                ii++;
            }
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = 0;
        return res;
    }

    Plan_Res_MulDOF PLAN::line_plan(Eigen::VectorXd pose_start, Eigen::VectorXd pose_end, KDL::Chain chain, double t, double dt){

        double qp0[7] = { 0.0 };
        double qv0[7] = { 0.0 };
        double qa0[7] = { 0.0 };
        double qpf[7] = { 0.0 }; 
        double qvf[7] = { 0.0 };
        double qaf[7] = { 0.0 };
        const int nn = sizeof(qp0) / sizeof(qp0[0]);
        for (size_t i = 0; i < pose_start.size(); i++)
        {
            qp0[i] = pose_start[i];
            qpf[i] = pose_end[i];      
        }
        int num = std::round(t / dt);
        MatrixXd x = MatrixXd::Zero(num,nn);
        MatrixXd dx = MatrixXd::Zero(num,nn);
        MatrixXd ddx = MatrixXd::Zero(num,nn);
        VectorXd time(num);
        double ct = dt;

        for (size_t i = 0; i < num; i++)
        {
            double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t);
            double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
            std::array<double, 7> joint = kdl_ik(chain,  KDL::Vector(pva[0][0], pva[0][1], pva[0][2]),  
                                                            KDL::Rotation::RPY(pva[0][3], pva[0][4], pva[0][5]));
            for (size_t j = 0; j < nn; j++)
            {
                x(i,j) = joint[j];    
                // dx(i,j) = pva[1][j];
                // ddx(i,j) = pva[2][j];
            }
            time[i] = ct;
            ct += dt;
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = 0;
        return res;
    }

    Plan_Res_MulDOF PLAN::assembly1(Eigen::VectorXd p, Eigen::VectorXd rpy, KDL::Chain chain, double t, double dt, int flag){
        
        int num = std::round(t / dt);
        MatrixXd x = MatrixXd::Zero(num,7);
        MatrixXd dx = MatrixXd::Zero(num,7);
        MatrixXd ddx = MatrixXd::Zero(num,7);
        VectorXd time(num);
        double ct = dt;

        for (size_t i = 0; i < num; i++)
        {
            std::array<double, 7> joint;
            if (flag == 1)
            {
                Eigen::VectorXd pp_push = f_push(ct)* 6;//2s 1cm
                Eigen::VectorXd pp = pp_push; 
                joint = kdl_ik(chain,  KDL::Vector(p[0] , p[1] + pp[2], p[2] ),  //基于base坐标系
                                                                KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]));// roll, pitch, yaw 
            }else if(flag == 2){
                Eigen::VectorXd pp_spiral = f_spiral(ct)/1.5;  
                Eigen::VectorXd pp_push = f_push(ct)* 6/25;//1s 0.01cm
                Eigen::VectorXd pp = pp_spiral + pp_push;
                joint = kdl_ik(chain,  KDL::Vector(p[0] + pp[0] , p[1] + pp[2], p[2] - pp[1]),  //基于base坐标系
                                                                KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]));
            }else if(flag == 3){

                Eigen::VectorXd pp_wiggle = f_wiggle(ct)/1.5 * 1.5;  

                Eigen::VectorXd pp_push = f_push(ct)* 1;//1s 0.01cm 

                Eigen::VectorXd pp = pp_wiggle + pp_push;

                Eigen::Matrix3d rotationMatrix = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);

                Eigen::Matrix3d r_x = rpy2rotationMatrix(0, 0, pp[3]);
                Eigen::Matrix3d r_y = rpy2rotationMatrix(0, 0, 0);
                Eigen::Matrix3d r_z = rpy2rotationMatrix(pp[4], 0, 0);

                Eigen::Matrix3d r_d = r_x * r_y * r_z * rotationMatrix;
                // 从旋转矩阵提取欧拉角（Roll, Pitch, Yaw）
                Eigen::Vector3d euler_angles = r_d.eulerAngles(2, 1, 0); // 顺序为 ZYX（Yaw-Pitch-Roll）
                
                joint = kdl_ik(chain,  KDL::Vector(p[0], p[1] + pp[2], p[2]),  //基于base坐标系
                                                                KDL::Rotation::RPY(euler_angles[2], euler_angles[1], euler_angles[0]));
                                                                // KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]));

            }else if(flag == 4){
                Eigen::VectorXd pp_push = f_push(ct)* 6/1.5;//1s 1cm
                Eigen::VectorXd pp = pp_push; 
                Eigen::Matrix4d T_base = Eigen::Matrix4d::Identity();
                T_base.block<3, 3>(0, 0) = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                T_base.block<3, 1>(0, 3) = Eigen::Vector3d(p[0], p[1], p[2]);
                Eigen::Matrix4d T_tool = Eigen::Matrix4d::Identity();
                T_tool.block<3, 3>(0, 0) = rpy2rotationMatrix(0, 0, 0);
                T_tool.block<3, 1>(0, 3) = Eigen::Vector3d(0, 0, pp[2]);

                Eigen::Matrix4d T = T_base * T_tool;

                joint = kdl_ik(chain,  KDL::Vector(T(0, 3) , T(1, 3), T(2, 3) ),  //基于base坐标系
                                                                KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]));// roll, pitch, yaw

            }

            for (size_t j = 0; j < 7; j++)
            {
                x(i,j) = joint[j];    
                // dx(i,j) = pva[1][j];
                // ddx(i,j) = pva[2][j];
            }
            time[i] = ct;
            ct += dt;
        }
        
        // 计算速度 dx 和加速度 ddx
        for (size_t j = 0; j < 7; j++) {
            for (size_t i = 0; i < num; i++) {
                // 计算速度 dx
                if (i == 0) {
                    // 使用前向差分计算速度
                    dx(i, j) = 0;
                    // dx(i, j) = (x(i + 1, j) - x(i, j)) / dt;
                } else if (i == num - 1) {
                    // 使用后向差分计算速度
                    dx(i, j) = 0;
                    // dx(i, j) = (x(i, j) - x(i - 1, j)) / dt;
                } else {
                    // 中央差分计算速度
                    dx(i, j) = (x(i + 1, j) - x(i - 1, j)) / (2 * dt);
                }

                // 计算加速度 ddx
                if (i == 0) {
                    // 使用前向差分计算加速度
                    ddx(i, j) = 0;
                } else if (i == num - 1) {
                    // 使用后向差分计算加速度
                    ddx(i, j) = 0;
                } else {
                    // 中央差分计算加速度
                    ddx(i, j) = (dx(i + 1, j) - dx(i - 1, j)) / (2 * dt);
                }
            }
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = 0;
        return res;
    }

    Plan_Res_MulDOF PLAN::assembly2(Eigen::VectorXd p, Eigen::VectorXd rpy, KDL::Chain chain, double t, double dt, int flag){
        int num = std::round(t / dt);
        MatrixXd x = MatrixXd::Zero(num,7);
        MatrixXd dx = MatrixXd::Zero(num,7);
        MatrixXd ddx = MatrixXd::Zero(num,7);
        VectorXd time(num);
        double ct = dt;
        for (size_t i = 0; i < num; i++)
        {
            Eigen::VectorXd pp_spiral = f_spiral(ct);  
            Eigen::VectorXd pp_push = f_push(ct); 
            Eigen::VectorXd pp = pp_spiral + pp_push;

            std::array<double, 7> joint = kdl_ik(chain,  KDL::Vector(p[0] + pp[0], p[1] + pp[2], p[2] + pp[1] ),  //基于base坐标系
                                                            KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]));
            for (size_t j = 0; j < 7; j++)
            {
                x(i,j) = joint[j];    
                // dx(i,j) = pva[1][j];
                // ddx(i,j) = pva[2][j];
            }
            time[i] = ct;
            ct += dt;
        }

        // 计算速度 dx 和加速度 ddx
        for (size_t j = 0; j < 7; j++) {
            for (size_t i = 0; i < num; i++) {
                // 计算速度 dx
                if (i == 0) {
                    // 使用前向差分计算速度
                    dx(i, j) = 0;
                    // dx(i, j) = (x(i + 1, j) - x(i, j)) / dt;
                } else if (i == num - 1) {
                    // 使用后向差分计算速度
                    dx(i, j) = 0;
                    // dx(i, j) = (x(i, j) - x(i - 1, j)) / dt;
                } else {
                    // 中央差分计算速度
                    dx(i, j) = (x(i + 1, j) - x(i - 1, j)) / (2 * dt);
                }

                // 计算加速度 ddx
                if (i == 0) {
                    // 使用前向差分计算加速度
                    ddx(i, j) = 0;
                } else if (i == num - 1) {
                    // 使用后向差分计算加速度
                    ddx(i, j) = 0;
                } else {
                    // 中央差分计算加速度
                    ddx(i, j) = (dx(i + 1, j) - dx(i - 1, j)) / (2 * dt);
                }
            }
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = 0;
        return res;
    }
}
