#include "arms_gen2_control/Def_Class.h"

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Demo01");
    ros::NodeHandle nh;

    DATA_PROC::Data_Pub dataPub(true);
    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    return 0;
}
