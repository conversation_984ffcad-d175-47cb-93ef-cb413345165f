/*
姿态变换和单位转换
*/

#include "arms_gen2_control/Def_Class.h"

namespace KINEMATICS {
    /**
     * @brief 获得绕x轴旋转rad弧度的旋转矩阵
     */
    Eigen::Matrix3d Pose_Trans::rot_x(double rad){
        Eigen::Matrix3d res;
        res << 1, 0, 0,
                0, cos(rad), -sin(rad),
                0, sin(rad), cos(rad);
        return res;
    }
    /**
     * @brief 获得绕y轴旋转rad弧度的旋转矩阵
     */
    Eigen::Matrix3d Pose_Trans::rot_y(double rad){
        Eigen::Matrix3d res;
        res << cos(rad), 0, sin(rad),
                0, 1, 0,
                -sin(rad), 0, cos(rad);
        return res;
    }
    /**
     * @brief 获得绕z轴旋转rad弧度的旋转矩阵
     */
    Eigen::Matrix3d Pose_Trans::rot_z(double rad){
        Eigen::Matrix3d res;
        res << cos(rad), -sin(rad), 0,
                sin(rad), cos(rad), 0,
                0, 0, 1;
        return res;
    }

    /**
     * @brief 角度转弧度
     */
    double Unit_Conv::deg2rad(double a){
        a = a*M_PI/180;
        return a;
    }
    Eigen::VectorXd Unit_Conv::deg2rad(Eigen::VectorXd vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec(i) = vec(i)*M_PI/180;
        }
        return vec;
    }
    std::vector<double> Unit_Conv::deg2rad(std::vector<double> vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec[i] = vec[i]*M_PI/180;
        }
        return vec;
    }
    /**
     * @brief 弧度转角度
     */
    double Unit_Conv::rad2deg(double a){
        a = a/M_PI*180;
        return a;
    }
    Eigen::VectorXd Unit_Conv::rad2deg(Eigen::VectorXd vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec(i) = vec(i)/M_PI*180;
        }
        return vec;
    }
    std::vector<double> Unit_Conv::rad2deg(std::vector<double> vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec[i] = vec[i]/M_PI*180;
        }
        return vec;
    }

    /**
     * @brief 因时灵巧手setAngle 0-1000数值转弧度
     */
    void Unit_Conv::handAngle2Pos(std::vector<double>& vec){
        vec[0] = (1000-vec[0])/1000*1.7;
        vec[1] = (1000-vec[1])/1000*1.7;
        vec[2] = (1000-vec[2])/1000*1.7;
        vec[3] = (1000-vec[3])/1000*1.7;
        vec[4] = (1000-vec[4])/1000*0.6;
        vec[5] = (1000-vec[5])/1000*1.3;
    }
    void Unit_Conv::handAngle2Pos(Eigen::VectorXd& vec){
        vec(0) = (1000-vec(0))/1000*1.7;
        vec(1) = (1000-vec(1))/1000*1.7;
        vec(2) = (1000-vec(2))/1000*1.7;
        vec(3) = (1000-vec(3))/1000*1.7;
        vec(4) = (1000-vec(4))/1000*0.6;
        vec(5) = (1000-vec(5))/1000*1.3;
    }
    std::vector<int> Unit_Conv::handPos2Angle(std::vector<double> vec){
        std::vector<int> res(6);
        res[0] = std::round((1.7-vec[0])/1.7*1000);
        res[1] = std::round((1.7-vec[1])/1.7*1000);
        res[2] = std::round((1.7-vec[2])/1.7*1000);
        res[3] = std::round((1.7-vec[3])/1.7*1000);
        res[4] = std::round((0.6-vec[4])/0.6*1000);
        res[5] = std::round((1.3-vec[5])/1.3*1000);
        return res;
    }
    Eigen::VectorXi Unit_Conv::handPos2Angle(Eigen::VectorXd vec){
        Eigen::VectorXi res(6);
        res(0) = std::round((1.7-vec[0])/1.7*1000);
        res(1) = std::round((1.7-vec[1])/1.7*1000);
        res(2) = std::round((1.7-vec[2])/1.7*1000);
        res(3) = std::round((1.7-vec[3])/1.7*1000);
        res(4) = std::round((0.6-vec[4])/0.6*1000);
        res(5) = std::round((1.3-vec[5])/1.3*1000);
        return res;
    }
}