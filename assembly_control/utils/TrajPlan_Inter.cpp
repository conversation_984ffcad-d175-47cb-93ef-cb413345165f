/*
轨迹规划-插值算法
*/

#include "arms_gen2_control/Def_Class.h"

KINEMATICS::Kine_Solver kineSolver;
KINEMATICS::KDL_Solver kdlSolver;

namespace TRAJ_PLAN{
    /**
     * @brief 五次多项式插值算法（单关节）
     */
    Plan_Res Interpolation::quinitic_poly(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt){
        int num_node = pos_seg.size();
        double a0,a1,a2,a3,a4,a5;
        double tmp,ti;
        int count = 1;
        Eigen::VectorXi n(num_node);
        Eigen::VectorXd t(count),x(count),dx(count),ddx(count);
        t(0) =   t_seg(0);
        x(0) =   pos_seg(0);
        dx(0) =  0;
        ddx(0) = 0;

        for(int i = 0; i < num_node-1; i++){
            tmp = t_seg(i+1) - t_seg(i);
            n(i) = int(tmp/dt);
            t.conservativeResize(count+n(i));
            x.conservativeResize(count+n(i));
            dx.conservativeResize(count+n(i));
            ddx.conservativeResize(count+n(i));

            a0 = pos_seg(i);
            a1 = 0;
            a2 = 0 / 2;
            a3 =  20*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,3));
            a4 = -30*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,4));
            a5 =  12*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,5));

            for(int j = count; j < count+n(i); j++){
                t(j) = t_seg(i) + j*dt;
                ti = (j-count)*dt;
                x(j) =     a0 +   a1*ti +    a2*pow(ti,2) +    a3*pow(ti,3) +   a4*pow(ti,4) + a5*pow(ti,5);
                dx(j) =    a1 + 2*a2*ti +  3*a3*pow(ti,2) +  4*a4*pow(ti,3) + 5*a5*pow(ti,4);
                ddx(j) = 2*a2 + 6*a3*ti + 12*a4*pow(ti,2) + 20*a5*pow(ti,3);
            }
            count = count + n(i);
        }

        Plan_Res res;
        res.t = t;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        return res;
    }

    /**
     * @brief 三次样条曲线插值（单关节)
     */
    Plan_Res Interpolation::cub_spline(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt, double v0, double vn){
        int num_node = pos_seg.size();
        Eigen::VectorXd h = Eigen::VectorXd::Zero(num_node-1);
        Eigen::VectorXd delta_y = Eigen::VectorXd::Zero(num_node-1);
        for(int i = 0; i < num_node-1; i++){
            h(i) = t_seg(i+1) - t_seg(i);
            delta_y(i) = pos_seg(i+1) - pos_seg(i);
        }

        Eigen::VectorXd a = pos_seg;
        Eigen::VectorXd b = Eigen::VectorXd::Zero(num_node);
        Eigen::VectorXd c = Eigen::VectorXd::Zero(num_node);
        Eigen::VectorXd d = Eigen::VectorXd::Zero(num_node);

        Eigen::MatrixXd Mtx_L = Eigen::MatrixXd::Zero(num_node,num_node);
        Eigen::VectorXd Mtx_R = Eigen::VectorXd::Zero(num_node);

        Eigen::VectorXi n_seg = Eigen::VectorXi::Zero(num_node);

        for(int i = 0; i < num_node; i++){
            if(i == 0){
                Mtx_L(i,0) = 2 * h(0);
                Mtx_L(i,1) = h(0);
                Mtx_R(i) = 3 * (delta_y(0)/h(0) - v0);
                continue;
            }
            if(i == num_node - 1){
                Mtx_L(i,i-1) = h(num_node - 2);
                Mtx_L(i,i) = 2 * h(num_node - 2);
                Mtx_R(i) = 3 * (vn - delta_y(i-1)/h(i-1));
                continue;
            }
            Mtx_L(i,i-1) = h(i-1);
            Mtx_L(i,i) = 2 * (h(i-1) + h(i));
            Mtx_L(i,i+1) = h(i);
            Mtx_R(i) = 3 * (delta_y(i) / h(i) - delta_y(i-1) / h(i-1));
        }
        c = Mtx_L.lu().solve(Mtx_R);

        // cout << c << endl;

        for(int i = 0; i < num_node-1; i++){
            b(i) = delta_y(i) / h(i) - (c(i+1) + 2*c(i)) * h(i) / 3;
            d(i) = (c(i+1) - c(i)) / (3 * h(i));
            n_seg(i+1) = round((t_seg(i+1) - t_seg(i))/dt) + n_seg(i);
        }

        // cout << n_seg << endl;
        // cout << round((t_seg(num_node-1)-t_seg(0))/dt) << endl;

        Eigen::VectorXd res_t = Eigen::VectorXd::LinSpaced(n_seg(num_node-1)+1,t_seg(0),t_seg(num_node-1));
        Eigen::VectorXd res_pos = Eigen::VectorXd::Zero(n_seg(num_node-1)+1);
        Eigen::VectorXd res_vel = Eigen::VectorXd::Zero(n_seg(num_node-1)+1);
        Eigen::VectorXd res_acc = Eigen::VectorXd::Zero(n_seg(num_node-1)+1);

        for(int i = 0; i < num_node-1; i++){
            for(int j = n_seg(i); j < n_seg(i+1)+1; j++){
                res_pos(j) = a(i) + b(i)*(res_t(j)-t_seg(i)) + c(i)*pow(res_t(j)-t_seg(i),2) + d(i)*pow(res_t(j)-t_seg(i),3);
                res_vel(j) = b(i) + 2*c(i)*(res_t(j)-t_seg(i)) + 3*d(i)*pow(res_t(j)-t_seg(i),2);
                res_acc(j) = 2*c(i) + 6*d(i)*(res_t(j)-t_seg(i));
            }
        }

        // cout << res_pos(0) << endl;
        Plan_Res res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        return res;
    }

    /**
     * @brief 关节空间多关节五次多项式插值
     */
    Plan_Res_MulDOF Interpolation::jnt_quin_poly(Eigen::VectorXd& jnt_pos_init, JointInterPoints ips){
        Eigen::MatrixXd position_seg(ips.pos_p.size(), ips.pos_p[0].size());
        for (size_t i = 0; i < ips.pos_p.size(); ++i) {
            for (size_t j = 0; j < ips.pos_p[i].size(); ++j) {
                position_seg(i, j) = ips.pos_p[i][j];
            }
        }
        Eigen::VectorXd t_seg = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.time_p.data(),ips.time_p.size());
        double dt = ips.dt;
        
        int n = position_seg.cols(); //关节数
        int m = position_seg.rows()+1; //经过点数
        Eigen::MatrixXd jntPos_seg(n, m);
        jntPos_seg.col(0) = jnt_pos_init;
        for(int i = 1; i < m; i++){
            jntPos_seg.col(i) = position_seg.row(i-1);
        }
        Plan_Res res_singleDOF;
        res_singleDOF = quinitic_poly(t_seg,jntPos_seg.row(0),dt);

        int num = res_singleDOF.pos.rows(); //插值点数
        Eigen::MatrixXd x = Eigen::MatrixXd::Zero(num,n);
        Eigen::MatrixXd dx = Eigen::MatrixXd::Zero(num,n);
        Eigen::MatrixXd ddx = Eigen::MatrixXd::Zero(num,n);
        Eigen::VectorXd time(num);
        for(int i = 0; i < n; i++){
            res_singleDOF = quinitic_poly(t_seg,jntPos_seg.row(i),dt);
            x.col(i) = res_singleDOF.pos;
            dx.col(i) = res_singleDOF.vel;
            ddx.col(i) = res_singleDOF.acc;
            time = res_singleDOF.t;
        }
        // cout << x.row(num-1) << endl;
        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.n = num;

        jnt_pos_init = res.pos.row(res.n-1);
        return res;
    }

    /**
     * @brief 关节空间多关节三次样条曲线插值
     */
    Plan_Res_MulDOF Interpolation::jnt_cub_spline(Eigen::VectorXd& jnt_pos_init, JointInterPoints ips, Eigen::MatrixXd vel_ends){
        Eigen::MatrixXd position_seg(ips.pos_p.size(), ips.pos_p[0].size());
        for (size_t i = 0; i < ips.pos_p.size(); ++i) {
            for (size_t j = 0; j < ips.pos_p[i].size(); ++j) {
                position_seg(i, j) = ips.pos_p[i][j];
            }
        }
        Eigen::VectorXd t_seg = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.time_p.data(),ips.time_p.size());
        double dt = ips.dt;
        
        int n = position_seg.cols(); //关节数
        int m = position_seg.rows()+1; //经过点数
        Eigen::MatrixXd jntPos_seg(n, m);
        jntPos_seg.col(0) = jnt_pos_init;
        for(int i = 1; i < m; i++){
            jntPos_seg.col(i) = position_seg.row(i-1);
        }
        Plan_Res res_singleDOF;
        res_singleDOF = cub_spline(t_seg,jntPos_seg.row(0),dt,vel_ends(0,0),vel_ends(0,1));

        int cnt = res_singleDOF.pos.rows();
        
        Eigen::VectorXd res_t = Eigen::VectorXd::Zero(cnt);
        Eigen::MatrixXd res_pos = Eigen::MatrixXd::Zero(cnt,n);
        Eigen::MatrixXd res_vel = Eigen::MatrixXd::Zero(cnt,n);
        Eigen::MatrixXd res_acc = Eigen::MatrixXd::Zero(cnt,n);
        
        for(int i = 0; i < n; i++){
            res_singleDOF = cub_spline(t_seg,jntPos_seg.row(i),dt,vel_ends(i,0),vel_ends(i,1));
            res_t = res_singleDOF.t;
            res_pos.col(i) = res_singleDOF.pos;
            res_vel.col(i) = res_singleDOF.vel;
            res_acc.col(i) = res_singleDOF.acc;
        }

        Plan_Res_MulDOF res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        res.n = cnt;

        jnt_pos_init = res.pos.row(res.n-1);
        return res;
    }

    /**
     * @brief 笛卡尔空间多关节五次多项式插值
     */
    Plan_Res_MulDOF Interpolation::cart_quin_poly(JntEnd& jnt_pos_init, CartInterPoints ips){
        using KINEMATICS::Unit_Conv;
        Unit_Conv unitConv;
        using KINEMATICS::Pose_Trans;
        Pose_Trans poseTrans;

        Plan_Res_MulDOF res;
        
        Eigen::MatrixXd position_seg(ips.pos_p.size(), ips.pos_p[0].size());
        for (size_t i = 0; i < ips.pos_p.size(); ++i) {
            for (size_t j = 0; j < ips.pos_p[i].size(); ++j) {
                position_seg(i, j) = ips.pos_p[i][j];
            }
        }
        Eigen::MatrixXd pose_seg(ips.pose_p.size(), ips.pose_p[0].size());
        for (size_t i = 0; i < ips.pose_p.size(); ++i) {
            for (size_t j = 0; j < ips.pose_p[i].size(); ++j) {
                pose_seg(i, j) = ips.pose_p[i][j];
            }
        }
        Eigen::VectorXd phi = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.armJ_p.data(),ips.armJ_p.size());
        Eigen::VectorXd t_seg = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.time_p.data(),ips.time_p.size());
        double dt = ips.dt;
        
        Eigen::MatrixXd pos_seg(ips.pos_p.size(), ips.pos_p[0].size());

        int m = position_seg.rows() + 1; //经过点数

        Eigen::VectorXd target_hand(6);
        Eigen::Vector2d t_hand;

        unitConv.handAngle2Pos(jnt_pos_init.jnt_hand);
        if(ips.pos_hand.size() != 0){
            unitConv.handAngle2Pos(ips.pos_hand);
            res.flagHandCtrl = true;
            for(int i = 0; i < m; i++){
                pos_seg.row(i) = kineSolver.biasHand(position_seg.row(i),pose_seg.row(i),Eigen::Vector2d(ips.pos_hand[5],ips.pos_hand[4]));
            }
            for(int i = 0; i < 6; i++){
                target_hand(i) = ips.pos_hand[i];
            }
            t_hand << 0,3;
        }else{
            target_hand = jnt_pos_init.jnt_hand;
            pos_seg = position_seg;
            t_hand << 0,0.1;
        }

        ROS_INFO_STREAM("pos_seg: " << pos_seg);
        std::cout << "position_seg: " << position_seg << std::endl;

        Eigen::MatrixXd jntPos_seg(7,m);
        jntPos_seg.col(0) = jnt_pos_init.jnt_arm;
        JntPos jnt_pos;
        for(int i = 1; i < m; i++){
            jnt_pos = kdlSolver.ik_kdl(pos_seg.row(i-1), phi(i-1), pose_seg.row(i-1));
            if(!jnt_pos.solved_flag || jnt_pos.error_flag){
                ROS_WARN("The iterative method failed!\nposition[%d], solve_flag[%d], error_flag[%d]",i,jnt_pos.solved_flag,jnt_pos.error_flag);

                ROS_ERROR("planning failed![cart_quin_poly]:move to [%.4f,%.4f,%.4f],[%.2f,%.2f,%.2f]",
                                                    pos_seg(i-1,0),pos_seg(i-1,1),pos_seg(i-1,2),
                                                    unitConv.rad2deg(pose_seg(i-1,0)),unitConv.rad2deg(pose_seg(i-1,1)),unitConv.rad2deg(pose_seg(i-1,2)));
                ros::shutdown();
                exit(0);
            }
            jntPos_seg.col(i) = jnt_pos.theta;
        }
        // cout << jntPos_seg << endl;
        Plan_Res res_singleDOF;
        res_singleDOF = quinitic_poly(t_seg,jntPos_seg.row(0),dt);
        
        int n = jntPos_seg.rows(); //关节数
        // int num = int(round((t_seg(m-1) - t_seg(0))/dt) + 1);
        int num = res_singleDOF.pos.rows();
        Eigen::MatrixXd x = Eigen::MatrixXd::Zero(num,n);
        Eigen::MatrixXd dx = Eigen::MatrixXd::Zero(num,n);
        Eigen::MatrixXd ddx = Eigen::MatrixXd::Zero(num,n);
        Eigen::VectorXd time(num);
        for(int i = 0; i < n; i++){
            res_singleDOF = quinitic_poly(t_seg,jntPos_seg.row(i),dt);
            x.col(i) = res_singleDOF.pos;
            dx.col(i) = res_singleDOF.vel;
            ddx.col(i) = res_singleDOF.acc;
            time = res_singleDOF.t;
        }
        // cout << x.row(num-1) << endl;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.n = num;
        
        Plan_Res res_singleDOF_hand;
        res_singleDOF_hand = quinitic_poly(t_hand,Eigen::Vector2d(jnt_pos_init.jnt_hand(0),target_hand(0)),dt);
        int num_hand = res_singleDOF_hand.pos.rows();
        Eigen::MatrixXd x_hand = Eigen::MatrixXd::Zero(num_hand,6);

        // cout << num_hand << endl;


        for(int i = 0; i < 6; i++){
            x_hand.col(5-i) = quinitic_poly(t_hand,Eigen::Vector2d(jnt_pos_init.jnt_hand(i),target_hand(i)),dt).pos;
        }
        res.pos_hand = x_hand;
        res.n_hand = num_hand;

        jnt_pos_init.jnt_arm = res.pos.row(res.n-1);
        jnt_pos_init.jnt_hand = x_hand.row(num_hand-1);
        // cout << jnt_pos_init.jnt_arm << endl;
        // cout << jnt_pos_init.jnt_hand << endl;

        return res;
    }

    /**
     * 笛卡尔空间多关节三次样条插值
     */
    Plan_Res_MulDOF Interpolation::cart_cub_spline(JntEnd& jnt_pos_init, CartInterPoints ips, Eigen::MatrixXd vel_ends){
        using KINEMATICS::Unit_Conv;
        Unit_Conv unitConv;
        using KINEMATICS::Pose_Trans;
        Pose_Trans poseTrans;

        Plan_Res_MulDOF res;
        
        Eigen::MatrixXd position_seg(ips.pos_p.size(), ips.pos_p[0].size());
        for (size_t i = 0; i < ips.pos_p.size(); ++i) {
            for (size_t j = 0; j < ips.pos_p[i].size(); ++j) {
                position_seg(i, j) = ips.pos_p[i][j];
            }
        }
        Eigen::MatrixXd pose_seg(ips.pose_p.size(), ips.pose_p[0].size());
        for (size_t i = 0; i < ips.pose_p.size(); ++i) {
            for (size_t j = 0; j < ips.pose_p[i].size(); ++j) {
                pose_seg(i, j) = ips.pose_p[i][j];
            }
        }
        Eigen::VectorXd phi = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.armJ_p.data(),ips.armJ_p.size());
        Eigen::VectorXd t_seg = Eigen::Map<Eigen::VectorXd,Eigen::Unaligned>(ips.time_p.data(),ips.time_p.size());
        double dt = ips.dt;

        Eigen::MatrixXd pos_seg(ips.pos_p.size(), ips.pos_p[0].size());
        int m = position_seg.rows() + 1; //经过点数

        Eigen::VectorXd target_hand(6);
        Eigen::Vector2d t_hand;
        unitConv.handPos2Angle(jnt_pos_init.jnt_hand);
        if(ips.pos_hand.size() != 0){
            unitConv.handPos2Angle(ips.pos_hand);
            res.flagHandCtrl = true;
            for(int i = 0; i < m; i++){
                pos_seg.row(i) = kineSolver.biasHand(position_seg.row(i),pose_seg.row(i),Eigen::Vector2d(ips.pos_hand[5],ips.pos_hand[4]));
            }
            for(int i = 0; i < 6; i++){
                target_hand(i) = ips.pos_hand[i];
            }
            t_hand << 0,3;
        }else{
            target_hand = jnt_pos_init.jnt_hand;
            pos_seg = position_seg;
            t_hand << 0,0.1;
        }

        Eigen::MatrixXd jntPos_seg(7,m);
        jntPos_seg.col(0) = jnt_pos_init.jnt_arm;
        JntPos jnt_pos;
        for(int i = 1; i < m; i++){
            jnt_pos = kdlSolver.ik_kdl(pos_seg.row(i-1), phi(i-1), pose_seg.row(i-1));
            if(!jnt_pos.solved_flag || jnt_pos.error_flag){
                ROS_WARN("The iterative method failed!\nposition[%d], solve_flag[%d], error_flag[%d]",i,jnt_pos.solved_flag,jnt_pos.error_flag);

                ROS_ERROR("planning failed![cart_cub_spline]:move to [%.4f,%.4f,%.4f],[%.2f,%.2f,%.2f]",
                                                    pos_seg(i-1,0),pos_seg(i-1,1),pos_seg(i-1,2),
                                                    unitConv.rad2deg(pose_seg(i-1,0)),unitConv.rad2deg(pose_seg(i-1,1)),unitConv.rad2deg(pose_seg(i-1,2)));
                ros::shutdown();
                exit(0);
            }
            
            jntPos_seg.col(i) = jnt_pos.theta;
        }
        // cout << jntPos_seg << endl;
        Plan_Res res_singleDOF;
        res_singleDOF = cub_spline(t_seg,jntPos_seg.row(0),dt,vel_ends(0,0),vel_ends(0,1));
        
        int n = jntPos_seg.rows();
        // int cnt = int(round((t_seg(m-1)-t_seg(0))/dt) + 1);
        int cnt = res_singleDOF.pos.rows();
        
        Eigen::VectorXd res_t = Eigen::VectorXd::Zero(cnt);
        Eigen::MatrixXd res_pos = Eigen::MatrixXd::Zero(cnt,n);
        Eigen::MatrixXd res_vel = Eigen::MatrixXd::Zero(cnt,n);
        Eigen::MatrixXd res_acc = Eigen::MatrixXd::Zero(cnt,n);
        
        for(int i = 0; i < n; i++){
            res_singleDOF = cub_spline(t_seg,jntPos_seg.row(i),dt,vel_ends(i,0),vel_ends(i,1));
            res_t = res_singleDOF.t;
            res_pos.col(i) = res_singleDOF.pos;
            res_vel.col(i) = res_singleDOF.vel;
            res_acc.col(i) = res_singleDOF.acc;
        }

        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        res.n = cnt;

        Plan_Res res_singleDOF_hand;
        res_singleDOF_hand = quinitic_poly(t_hand,Eigen::Vector2d(jnt_pos_init.jnt_hand(0),target_hand(0)),dt);
        int num_hand = res_singleDOF_hand.pos.rows();
        Eigen::MatrixXd x_hand = Eigen::MatrixXd::Zero(num_hand,6);

        for(int i = 0; i < 6; i++){
            x_hand.col(5-i) = quinitic_poly(t_hand,Eigen::Vector2d(jnt_pos_init.jnt_hand(i),target_hand(i)),dt).pos;
        }
        res.pos_hand = x_hand;
        res.n_hand = num_hand;

        jnt_pos_init.jnt_arm = res.pos.row(res.n-1);
        jnt_pos_init.jnt_hand = x_hand.row(num_hand-1);

        return res;
    }

    /**
     * @brief 保持不动
     */
    Plan_Res_MulDOF Interpolation::stay_still(Eigen::VectorXd& jnt_pos, int n, double dt){
        Eigen::VectorXd t(n);
        Eigen::MatrixXd pos(n,7);
        Eigen::MatrixXd vel = Eigen::MatrixXd::Zero(n,7);
        Eigen::MatrixXd acc = Eigen::MatrixXd::Zero(n,7);
        for(int i = 0; i < n; i++){
            t(i) = i*dt;
            pos.row(i) = jnt_pos;
        }

        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = pos;
        res.vel = vel;
        res.acc = acc;
        res.n = n;
        return res;
    }
}