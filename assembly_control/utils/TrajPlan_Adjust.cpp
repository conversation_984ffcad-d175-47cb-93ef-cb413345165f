/*
轨迹规划-轨迹调整
*/

#include "arms_gen2_control/Def_Class.h"

namespace TRAJ_PLAN{
    Plan_Res_MulDOF Traj_Adjust::traj_splic(Eigen::VectorXd& jnt_end, Plan_Res_MulDOF act_start, Plan_Res_MulDOF act_end){
        int n1 = act_start.t.rows();
        int n2 = act_end.t.rows();
        int n = n1 + n2;
        Eigen::VectorXd res_t(n);
        Eigen::MatrixXd res_pos(n,7);
        Eigen::MatrixXd res_vel(n,7);
        Eigen::MatrixXd res_acc(n,7);
        res_t.head(n1) = act_start.t;
        res_t.tail(n2) = act_end.t.array() + act_start.t(n1-1);
        res_pos.block(0,0,n1,7) = act_start.pos;
        res_pos.block(n1,0,n2,7) = act_end.pos;
        res_vel.block(0,0,n1,7) = act_start.vel;
        res_vel.block(n1,0,n2,7) = act_end.vel;
        res_acc.block(0,0,n1,7) = act_start.acc;
        res_acc.block(n1,0,n2,7) = act_end.acc;

        Plan_Res_MulDOF res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        res.n = n;

        jnt_end = res.pos.row(res.n-1);
        return res;
    }

    Plan_Res_MulDOF Traj_Adjust::traj_extend(Plan_Res_MulDOF action, int n, double dt){
        int n0 = action.n;

        Plan_Res_MulDOF res;
        if(n0 == n){
            res = action;
            // cout << 1 << endl;
        }else if(n0 > n){
            res = action;
            ROS_ERROR("The expansion length is smaller than the original length!");
            ros::shutdown();
            exit(0);
        }else{
            Eigen::VectorXd res_t(n);
            Eigen::MatrixXd res_pos(n,7);
            Eigen::MatrixXd res_vel(n,7);
            Eigen::MatrixXd res_acc(n,7);
            res_t.head(n0) = action.t;
            res_pos.block(0,0,n0,7) = action.pos;
            res_vel.block(0,0,n0,7) = action.vel;
            res_acc.block(0,0,n0,7) = action.acc;
            for(int i = n0; i < n; i++){
                res_t(i) = action.t(n0-1) + (i-n0+1)*dt;
                res_pos.row(i) = action.pos.row(n0-1);
                res_vel.row(i) = action.vel.row(n0-1);
                res_acc.row(i) = action.acc.row(n0-1);  
            }
            res.t = res_t;
            res.pos = res_pos;
            res.vel = res_vel;
            res.acc = res_acc;
            res.n = n;
            res.pos_hand = action.pos_hand;
        }
        
        return res;
    }

    Plan_Res_MulDOF Traj_Adjust::traj_inv(Eigen::VectorXd& jnt_end, Plan_Res_MulDOF traj_ori){
        int n = traj_ori.t.size();
        Eigen::MatrixXd pos(n,7);
        Eigen::MatrixXd vel(n,7);
        Eigen::MatrixXd acc(n,7);
        
        for(int i=0;i<n;i++){
            pos.row(i) = traj_ori.pos.row(n-1-i);
            vel.row(i) = traj_ori.vel.row(n-1-i);
            acc.row(i) = traj_ori.acc.row(n-1-i);
        }
        Plan_Res_MulDOF res;
        res.pos = pos;
        res.vel = vel;
        res.acc = acc;
        res.t = traj_ori.t;
        res.n = traj_ori.n;

        jnt_end = res.pos.row(res.n-1);
        return res;
    }
}