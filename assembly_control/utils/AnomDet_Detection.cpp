/*
异常检测相关
    - 位置超限检测
    - 跳变检测
    - 子碰撞检测
*/

#include "arms_gen2_control/Def_Class.h"

namespace ANOM_DETEC {
    KINEMATICS::Pose_Trans poseTrans;
    /**
     * @brief 位置超限检测
     * @details 设置位置限制，检查各个关节角度是否超限
     */
    bool AnomalyDetection::isExceedPosLimit(Eigen::VectorXd pos_r, Eigen::VectorXd pos_l){
        bool flag = false;
        // 每个关节的角度限制（弧度）
        Eigen::MatrixXd theta_limit(7,2);
        theta_limit << -M_PI, M_PI,
                       -M_PI*135/180, M_PI*5/180,
                       -M_PI*91/180, M_PI*91/180,
                       -M_PI*1/180, M_PI*111/180,
                       -M_PI, M_PI,
                       -M_PI/2, M_PI/2,
                       -M_PI/2, M_PI/2;
        // 辅助函数，用来检查关节位置是否超出限制
        auto checkLimit = [&](const Eigen::VectorXd& pos, const std::string& arm) {
            for (int i = 0; i < 7; i++) {
                if (pos(i) < theta_limit(i, 0) || pos(i) > theta_limit(i, 1)) {
                    ROS_WARN("joint%d[%.4f] of the %s arm exceeds the limit[%.4f, %.4f]",
                            i + 1, pos(i) / M_PI * 180, arm.c_str(), 
                            theta_limit(i, 0) / M_PI * 180, theta_limit(i, 1) / M_PI * 180);
                    return true;
                }
            }
            return false;
        };
        // 检查左右手臂的关节位置
        flag = checkLimit(pos_r, "right") || checkLimit(pos_l, "left");
        return flag;
    }

    /**
     * @brief 跳变检测
     * @details 检测关节速度是否是个很大的值（1000）
     */
    bool AnomalyDetection::isExceedVelLimit(const Eigen::VectorXd& vel_r, const Eigen::VectorXd& vel_l, double limit) {
        bool flag = false;
        // 辅助函数，用来检查关节速度是否超出限制
        auto checkVel = [&](const Eigen::VectorXd& vel, const std::string& arm_name) {
            for (int i = 0; i < 7; i++) {
                if (std::abs(vel(i)) > limit) {
                    ROS_WARN("joint%d of the %s arm has undergone a mutation", i + 1, arm_name.c_str());
                    return true;
                }
            }
            return false;
        };
        // 检查左右手臂的关节速度
        flag = checkVel(vel_r, "right") || checkVel(vel_l, "left");
        return flag;
    }

    /**
     * @brief 自碰撞检测
     * @details 循环检测对应连杆是否碰撞
     */
    bool AnomalyDetection::isSelfCollision(Eigen::VectorXd theta_r, Eigen::VectorXd theta_l, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel){
        
        std::shared_ptr<coal::ConvexBase> base_link = robotModel[0];
        DynamicArray<std::shared_ptr<coal::ConvexBase>> r_arm_link;
        DynamicArray<std::shared_ptr<coal::ConvexBase>> l_arm_link;

        for(int i=0; i<7; i++){
            r_arm_link.push_back(robotModel[i+1]);
            l_arm_link.push_back(robotModel[i+8]);
        }
        
        std::vector<Eigen::Vector3d> relative_eular_r = {
            Eigen::Vector3d(0,0,0),
            Eigen::Vector3d(-M_PI/2,0,-M_PI/2),
            Eigen::Vector3d(M_PI/2,0,M_PI/2),
            Eigen::Vector3d(-M_PI/2,0,M_PI/2),
            Eigen::Vector3d(M_PI/2,0,0),
            Eigen::Vector3d(-M_PI/2,0,0),
            Eigen::Vector3d(-M_PI/2,0,-M_PI/2)
        };
        std::vector<Eigen::Vector3d> relative_position_r = {
            Eigen::Vector3d(0, 0, 0.2016),
            Eigen::Vector3d(0.0356, 0, 0.052),
            Eigen::Vector3d(0.049, 0, -0.035),
            Eigen::Vector3d(-0.041, 0, 0.1996),
            Eigen::Vector3d(0, -0.1395, -0.041),
            Eigen::Vector3d(0, 0.0295, 0.035),
            Eigen::Vector3d(0.0295, -0.07, -0.0295)
        };
        std::vector<Eigen::Vector3d> relative_eular_l = {
            Eigen::Vector3d(0, 0, 0),
            Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2),
            Eigen::Vector3d(-M_PI / 2, 0, M_PI / 2),
            Eigen::Vector3d(M_PI / 2, 0, M_PI / 2),
            Eigen::Vector3d(-M_PI / 2, 0, 0),
            Eigen::Vector3d(M_PI / 2, 0, 0),
            Eigen::Vector3d(M_PI / 2, 0, -M_PI / 2)
        };
        std::vector<Eigen::Vector3d> relative_position_l = {
            Eigen::Vector3d(0, 0, -0.2066),
            Eigen::Vector3d(0.0416, 0, -0.047),
            Eigen::Vector3d(0.049, 0, 0.041),
            Eigen::Vector3d(-0.041, 0, -0.1996),
            Eigen::Vector3d(0, -0.1395, 0.041),
            Eigen::Vector3d(0, 0.0295, -0.035),
            Eigen::Vector3d(0.0295, -0.07, 0.0295)
        };

        // Self-Collision Matrix
        Eigen::MatrixXd SCM = Eigen::MatrixXd::Zero(15,15);
        SCM(2,0) = 1;
        SCM(3,0) = 1; 
        SCM(4,0) = 1; 
        SCM(5,0) = 1; SCM(5,3) = 1;
        SCM(6,0) = 1; SCM(6,1) = 1; SCM(6,2) = 1; SCM(6,3) = 1; SCM(6,4) = 1;
        SCM(7,0) = 1; SCM(7,1) = 1; SCM(7,2) = 1; SCM(7,3) = 1; SCM(7,4) = 1; SCM(7,5) = 1;
        SCM(8,6) = 1; SCM(8,7) = 1;
        SCM(9,0) = 1; SCM(9,5) = 1; SCM(9,6) = 1; SCM(9,7) = 1;
        SCM(10,0) = 1; SCM(10,4) = 1; SCM(10,5) = 1; SCM(10,6) = 1; SCM(10,7) = 1;
        SCM(11,0) = 1; SCM(11,3) = 1; SCM(11,4) = 1; SCM(11,5) = 1; SCM(11,6) = 1; SCM(11,7) = 1;
        SCM(12,0) = 1; SCM(12,2) = 1; SCM(12,3) = 1; SCM(12,4) = 1; SCM(12,5) = 1; SCM(12,6) = 1; SCM(12,7) = 1; SCM(12,10) = 1;
        SCM(13,0) = 1; SCM(13,1) = 1; SCM(13,2) = 1; SCM(13,3) = 1; SCM(13,4) = 1; SCM(13,5) = 1; SCM(13,6) = 1; SCM(13,7) = 1; SCM(13,8) = 1; SCM(13,9) = 1; SCM(13,10) = 1; SCM(13,11) = 1;
        SCM(14,0) = 1; SCM(14,1) = 1; SCM(14,2) = 1; SCM(14,3) = 1; SCM(14,4) = 1; SCM(14,5) = 1; SCM(14,6) = 1; SCM(14,7) = 1; SCM(14,8) = 1; SCM(14,9) = 1; SCM(14,10) = 1; SCM(14,11) = 1; SCM(14,12) = 1; 
    
        std::vector<bool> cd_res(15,false);

        coal::Transform3s T1;
        coal::Transform3s T2;
        std::shared_ptr<coal::ConvexBase> link1;
        std::shared_ptr<coal::ConvexBase> link2;
        std::string link_name1;
        std::string link_name2;

        Eigen::Matrix3d T0 = poseTrans.rot_x(M_PI/2);
        Eigen::Quaterniond Q0(T0);
        Q0.normalize();
        Eigen::VectorXd Qtd0(4);
        Qtd0 << Q0.coeffs();
        Eigen::Vector3d pos0 = Eigen::Vector3d(0, 0, 0.8);
        coal::Transform3s T_fcl0 = setPose_fcl_(Qtd0,pos0);

        DynamicArray<mesh_pose> arm_pose_r;
        DynamicArray<mesh_pose> arm_pose_l;
        arm_pose_r = get_arm_pose_(relative_eular_r,relative_position_r,theta_r);
        arm_pose_l = get_arm_pose_(relative_eular_l,relative_position_l,theta_l);

        coal::CollisionRequest request;
        coal::CollisionResult result;

        bool colli_flag = false;


        for(int i=0; i<15; i++){
            for(int j=0; j<15; j++){
                if(SCM(i,j)){
                    if(i == 0){
                        T1 = T_fcl0;
                        link1 = base_link;
                        link_name1 = "base_link";
                    }else if(i < 8){
                        T1 = arm_pose_r[i-1].T_fcl;
                        link1 = r_arm_link[i-1];
                        link_name1 = "right_link" + std::to_string(i);
                    }else{
                        T1 = arm_pose_l[i-8].T_fcl;
                        link1 = l_arm_link[i-8];
                        link_name1 = "left_link" + std::to_string(i-7);
                    }

                    if(j == 0){
                        T2 = T_fcl0;
                        link2 = base_link;
                        link_name2 = "base_link";
                    }else if(j < 8){
                        T2 = arm_pose_r[j-1].T_fcl;
                        link2 = r_arm_link[j-1];
                        link_name2 = "right_link" + std::to_string(j);
                    }else{
                        T2 = arm_pose_l[j-8].T_fcl;
                        link2 = l_arm_link[j-8];
                        link_name2 = "left_link" + std::to_string(j-7);
                    }

                    collide(link1.get(), T1, link2.get(), T2, request, result);
                    if(result.isCollision()){
                        cd_res[i] = true;
                        cd_res[j] = true;
                        ROS_WARN("Collision detected! ---- %s & %s",link_name1.c_str(),link_name2.c_str());
                        colli_flag = true;
                    }
                    result.clear();
                }
            }
        }
        return colli_flag;
    }

    bool AnomalyDetection::anomalyDetection(Plan_Res jnts_r, Plan_Res jnts_l, bool is_colli_det, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel){
        bool mutation_flag = false;

        Eigen::VectorXd pos_r = jnts_r.pos;
        Eigen::VectorXd pos_l = jnts_l.pos;
        Eigen::VectorXd vel_r = jnts_r.vel;
        Eigen::VectorXd vel_l = jnts_l.vel;

        bool flag1 = isExceedPosLimit(pos_r, pos_l);
        bool flag2 = isExceedVelLimit(vel_r, vel_l);
        if(flag1 || flag2){
            mutation_flag = true;
        }
        if(is_colli_det){
            bool flag3 = isSelfCollision(pos_r, pos_l, robotModel);
            if(flag3){
                mutation_flag = true;
            }
        }

        return mutation_flag;
    }
}