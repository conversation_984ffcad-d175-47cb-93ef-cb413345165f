#!/usr/bin/env python3
import sys
import rospy
from std_msgs.msg import Float64MultiArray
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QSlider, QLabel,
    QPushButton, QHBoxLayout, QGridLayout
)
from PyQt5.QtCore import Qt

class MotorGUI(QWidget):
    def __init__(self, n_motors=14):
        super().__init__()
        self.pub = rospy.Publisher('target_velocity', Float64MultiArray, queue_size=1)
        self.values = [0.0] * n_motors
        self.sliders = []
        self.value_labels = []  # 存储每个电机的值标签

        layout = QVBoxLayout()

        # 一键全部置零按钮
        zero_all_btn = QPushButton("0")
        zero_all_btn.clicked.connect(self.zero_all)
        layout.addWidget(zero_all_btn)

        # 为每个电机创建控件
        for i in range(n_motors):
            # 顶部行：标签 + 当前值
            top_row = QHBoxLayout()
            label = QLabel(f"Motor {i+1}")
            # label.setFixedWidth(60)
            value_label = QLabel("0.00")  # 初始值
            # value_label.setFixedWidth(60)
            top_row.addWidget(label)
            top_row.addWidget(value_label)
            top_row.addStretch(1)

            # 滑块
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(-250)   # 对应 -1.00
            slider.setMaximum(250)    # 对应 +1.00
            slider.setValue(0)
            slider.setSingleStep(1)   # 每次变化 0.01
            # 注意：lambda 捕获要用默认参数绑定当前循环变量
            slider.valueChanged.connect(
                lambda val, idx=i: self.update_value(idx, val)
            )

            # 底部刻度行（-1.00 | 0 | +1.00）
            marks = QGridLayout()
            neg = QLabel("-2.50")
            zero = QLabel("0")
            pos = QLabel("+2.50")
            for lab in (neg, zero, pos):
                lab.setAlignment(Qt.AlignHCenter)
                lab.setStyleSheet("color: #666; font-size:10pt;")
            marks.addWidget(neg, 0, 0)
            marks.addWidget(zero, 0, 1)
            marks.addWidget(pos, 0, 2)
            marks.setColumnStretch(0, 1)
            marks.setColumnStretch(1, 1)
            marks.setColumnStretch(2, 1)

            layout.addLayout(top_row)
            layout.addWidget(slider)
            layout.addLayout(marks)

            self.sliders.append(slider)
            self.value_labels.append(value_label)

        self.setLayout(layout)
        self.setWindowTitle("Target Velocity Controller")

    def update_value(self, idx, val):
        real_val = val / 100.0  # 转换到 -1.00 ~ +1.00
        self.values[idx] = real_val
        self.value_labels[idx].setText(f"{real_val:+.2f}")  # 保留两位小数
        msg = Float64MultiArray(data=self.values)
        self.pub.publish(msg)

    def zero_all(self):
        for slider in self.sliders:
            slider.setValue(0)  # 会触发 update_value 自动更新标签

if __name__ == "__main__":
    rospy.init_node("target_velocity_gui", anonymous=True)
    app = QApplication(sys.argv)
    gui = MotorGUI()
    gui.show()
    sys.exit(app.exec_())
