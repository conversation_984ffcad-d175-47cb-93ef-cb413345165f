
***************
 Eigen3  >= 3.2.92 (3.3~beta1) is required
***************

***************
 kdl库
***************
git clone https://github.com/orocos/orocos_kinematics_dynamics.git

cd orocos_kinematics_dynamics/
cd orocos_kdl

mkdir build

cd build
cmake ..
make
sudo make install
***************

***************
kdl_parser库
***************
git clone https://github.com/ros/kdl_parser.git
cd kdl_parser
git checkout noetic-devel
git branch 
cd kdl_parser

mkdir build
cd build
cmake ..
make
sudo make install
***************

***************
.bashrc文件写入URDF文件路径
export URDF_PATH=/home/<USER>/R_ws/robot_arm_v2/src/arms_gen2/urdf/arms_gen2_urdf.urdf
***************

***************
git clone https://github.com/humanoid-path-planner/hpp-fcl
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
sudo make install
***************

初始编译：
catkin_make -DCMAKE_BUILD_TYPE=RelWithDebInfo

记录数据：
rosbag record /car_pos/act /car_pos/error /car_pos/vir /motor_state/arm_motor_state_actual

rosbag record /motor_state/arm_motor_state_actual /joint_states  


抓孔钉两个手的位置
source ./devel/setup.bash
rosservice call /inspire_hand/set_angle 500 500 500 500 500 100 1

rosservice call /inspire_hand/set_angle 999 999 999 999 999 100 2   

rosservice call /inspire_hand/get_force_act 1

防火墙：
sudo route add -net 224.0.1.100 netmask 255.255.255.255 dev enp45s0

因时手串口：
sudo chmod a+rw /dev/ttyUSB0


***************
装配相机节点：
python grasp_obj.py
rosrun robot_control grasp_assembly
rosrun assembly_control Action10_assembly

装配时桌子位置:
桌子108cm
桌子距离机器人19cm
***************

***************
敬礼抱枪节点：
python pose_D435i.py
rosrun robot_control demo_salute
***************