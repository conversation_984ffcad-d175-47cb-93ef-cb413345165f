#ifndef _DEF_STRUCT_H
#define _DEF_STRUCT_H

#include <vector>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/LU>
#include <stdexcept> // For std::out_of_range

#include <visualization_msgs/Marker.h>
#include <geometry_msgs/Pose.h>

#include <coal/math/transform.h>
#include <coal/mesh_loader/loader.h>
#include <coal/BVH/BVH_model.h>
#include <coal/collision.h>
#include <coal/collision_data.h>
#include <memory>


struct CartInterPoints{
    std::vector<double> time_p;
    std::vector<double> armJ_p;
    std::vector<std::vector<double>> pos_p;
    std::vector<std::vector<double>> pose_p;
    double dt;
    std::vector<double> pos_hand;
};
struct JointInterPoints{
    std::vector<double> time_p;
    std::vector<std::vector<double>> pos_p;
    double dt;
};
struct DmpTargetPoints{
    std::vector<double> pos_p;
    std::vector<double> pose_p;
    double dt;
    double armJ_p = -3.141592653589793238462643383279/2;
    double tau = 1;
};

struct JntEnd{
    Eigen::VectorXd jnt_arm;
    Eigen::VectorXd jnt_hand;
};

struct Demon_Traj
{
    Eigen::VectorXd time;
    Eigen::VectorXd pos_ta1;
    Eigen::VectorXd pos_ta2;
    Eigen::VectorXd pos_ta3;
    Eigen::VectorXd pos_ta4;
    Eigen::VectorXd vel_ta1;
    Eigen::VectorXd vel_ta2;
    Eigen::VectorXd vel_ta3;
    Eigen::VectorXd vel_ta4;
    Eigen::VectorXd acc_ta1;
    Eigen::VectorXd acc_ta2;
    Eigen::VectorXd acc_ta3;
    Eigen::VectorXd acc_ta4;
    Eigen::VectorXd ArmJnt;
};
struct Train_Par
{
    int nbfs;       // 基函数个数
    double alpha_y;
    double k;
    int k_f;
};
struct Train_Res
{
    Eigen::MatrixXd w;
	Eigen::VectorXd c;
	Eigen::VectorXd h;
	int N;
	double alpha_x;
	double alpha_y;
	double beta_y;
	double t_end;
	int k_f;
};
struct Regress_Par
{
    Eigen::Vector4d x_init;
    Eigen::Vector4d x_goal;
    double tau;
    double dt;
};
struct Plan_Res_MulDOF
{
    Eigen::MatrixXd pos;
    Eigen::MatrixXd vel;
    Eigen::MatrixXd acc;
    Eigen::VectorXd t;
    int error_flag = 0;
    int n;
    bool flagHandCtrl = false;
    Eigen::MatrixXd pos_hand; 
    int n_hand;
};
struct Plan_Res
{
    Eigen::VectorXd pos;
    Eigen::VectorXd vel;
    Eigen::VectorXd acc;
    Eigen::VectorXd t;
};
struct JntPos
{
    Eigen::VectorXd theta;
    int error_flag = 0;
    int solved_flag = 0;
};
struct KineRes
{
    double eul_r;
    double eul_p;
    double eul_y;
    double jnt_arm;
    Eigen::Vector3d cart_end_position;
    Eigen::Matrix3d transMatrix;
};

struct ArmTraj{
    Plan_Res_MulDOF right;
    Plan_Res_MulDOF left;
};

struct mesh_pose{
    geometry_msgs::Pose pose;
    coal::Transform3s T_fcl;
};

template<typename T>
class DynamicArray {
private:
    T* data;         // 指向动态分配的数组
    size_t capacity; // 数组的容量
    size_t length;   // 数组当前长度

public:
    // 构造函数
    DynamicArray(size_t initial_capacity = 10) : capacity(initial_capacity), length(0) {
        data = new T[capacity];
    }
    // 析构函数
    ~DynamicArray() {
        delete[] data;
    }
    // 拷贝构造函数
    DynamicArray(const DynamicArray& other) : capacity(other.capacity), length(other.length) {
        data = new T[capacity];
        for (size_t i = 0; i < length; ++i) {
            data[i] = other.data[i];
        }
    }
    // 赋值运算符
    DynamicArray& operator=(const DynamicArray& other) {
        if (this != &other) {
            delete[] data;
            capacity = other.capacity;
            length = other.length;
            data = new T[capacity];
            for (size_t i = 0; i < length; ++i) {
                data[i] = other.data[i];
            }
        }
        return *this;
    }
    // 添加元素
    void push_back(const T& value) {
        if (length == capacity) {
            reserve(capacity * 2);
        }
        data[length++] = value;
    }
    // 获取元素
    T& operator[](size_t index) {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取常量元素
    const T& operator[](size_t index) const {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取数组长度
    size_t size() const {
        return length;
    }
    // 调整数组容量
    void reserve(size_t new_capacity) {
        if (new_capacity > capacity) {
            T* new_data = new T[new_capacity];
            for (size_t i = 0; i < length; ++i) {
                new_data[i] = data[i];
            }
            delete[] data;
            data = new_data;
            capacity = new_capacity;
        }
    }
};


#endif