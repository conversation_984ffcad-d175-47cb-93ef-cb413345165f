#ifndef _DEF_CLASS_H
#define _DEF_CLASS_H

#include "Def_Struct.h"

#include <stdio.h>
#include <iostream>
#include <fstream>
#include <string>

#include <serial/serial.h>

#include <chrono>
#include <ctime>

#include <mutex>
#include <future>
#include <thread>

#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl_parser/kdl_parser.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainfksolver.hpp>

#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>

#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Float64.h>
#include <sensor_msgs/JointState.h>

#include "ros/ros.h"
#include "ros/callback_queue.h"

namespace DATA_PROC {
    extern Eigen::VectorXd jnt_pos_actual_r;
    extern Eigen::VectorXd jnt_pos_actual_l;

    void fileWrite(FILE *fpWrite, Eigen::VectorXd vec_r, Eigen::VectorXd vec_l);
    void printJointAngles(const std::string &arm_side, const Eigen::VectorXd &jnt_pos);

    class Data_Pub {
        public:
            Data_Pub(bool flag_colli_det = true);
            ~Data_Pub();

            int initPort(serial::Serial *port, uint8_t hand_id);
            void checkPortOpen(serial::Serial *com_port);
            bool setANGLE(int angle0, int angle1, int angle2, int angle3, int angle4, int angle5, uint8_t hand_id);
            bool setSPEED(int speed0, int speed1, int speed2, int speed3, int speed4, int speed5, uint8_t hand_id);
            bool setFORCE(int force0, int force1, int force2, int force3, int force4, int force5, uint8_t hand_id);
            void rightHand();
            void rightHand(const std::vector<int>& pos_cmd);
            void rightHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd);
            void leftHand();
            void leftHand(const std::vector<int>& pos_cmd);
            void leftHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd);
            void doubleHand();
            void doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& pos_cmd_l);
            void doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& vel_cmd_r, const std::vector<int>& pos_cmd_l, const std::vector<int>& vel_cmd_l);

            void move(ArmTraj traj, bool isSim);

        private:
            ros::NodeHandle nh_;
            // 话题发布对象
            ros::Publisher pub_motor_;
            ros::Publisher pub_rviz_;
            // 话题数据载体
            sensor_msgs::JointState rviz_msgs_;
            std_msgs::Float64MultiArray motor_msgs_;
            // 因时手通信相关
            std::string port_name_;
            uint8_t hand_id1_;
            uint8_t hand_id2_;
            uint32_t baudrate_;
            uint8_t test_flags_;
            uint8_t Serial_flags_;
            serial::Serial *com_port_;
            float act_position_;
            uint8_t hand_state_;
            static constexpr double WAIT_FOR_RESPONSE_INTERVAL_ = 0.5;
            static constexpr double INPUT_BUFFER_SIZE_ = 64;
            // 因时手默认位置
            std::vector<int> default_pos_;
            // 文件指针
            FILE *fwPubPos_;
            FILE *fwPubVel_;
            FILE *fwPubAcc_;
            // 机器人convex模型
            DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel_;
            // 是否进行碰撞检测
            bool is_colli_det_;
    };

    class Data_Sub {
        public:
            Data_Sub();
            ~Data_Sub();
        public:
            void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg);
            void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg);

            void getJntPos(Eigen::VectorXd &jnt_pos_right, Eigen::VectorXd &jnt_pos_left);

            bool waitForCVData(size_t expected_size);
            void extractTargetPosition(Eigen::Vector3d &target_pos, size_t start_index);
            void getCVPos(Eigen::Vector3d &target_pos);
            void getCVPos(Eigen::Vector3d &target_pos, int &obj_type);
            void getCVPos(Eigen::Vector3d &target_pos1, Eigen::Vector3d &target_pos2);
            void getCVPos(Eigen::Vector3d &target_pos1, int &obj_type1, Eigen::Vector3d &target_pos2, int &obj_type2);
        public:    
            std::vector<double> container;
            bool ActPos_flag;
            bool subCV_flag;
        private:
            ros::NodeHandle nh1_;
            ros::NodeHandle nh2_;
            ros::Subscriber subActPos_;
            ros::Subscriber subCVPos_;

            ros::CallbackQueue* queue1_;
            ros::CallbackQueue* queue2_;

            ros::AsyncSpinner* spinner1_;
            ros::AsyncSpinner* spinner2_;

            FILE *fwSubPos_;
    };
}

namespace ANOM_DETEC {
    class AnomalyDetection {
        public:    
            bool isExceedPosLimit(Eigen::VectorXd pos_r, Eigen::VectorXd pos_l);
            bool isExceedVelLimit(const Eigen::VectorXd& vel_r, const Eigen::VectorXd& vel_l, double limit = 1000.0);

            DynamicArray<std::shared_ptr<coal::ConvexBase>> loadRobotModel_();
            std::shared_ptr<coal::ConvexBase> loadConvexMesh_(const std::string& file_name);
            geometry_msgs::Pose setPose_mesh_(Eigen::VectorXd q, Eigen::Vector3d position);
            coal::Transform3s setPose_fcl_(Eigen::VectorXd q, Eigen::Vector3d position);
            DynamicArray<mesh_pose> get_arm_pose_(std::vector<Eigen::Vector3d> relative_eular, std::vector<Eigen::Vector3d> relative_position, Eigen::VectorXd theta);
            bool isSelfCollision(Eigen::VectorXd theta_r, Eigen::VectorXd theta_l, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel);

            bool anomalyDetection(Plan_Res jnts_r, Plan_Res jnts_l, bool is_colli_det, DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel);
    };
}

namespace KINEMATICS {
    class Pose_Trans {
        public:
            Eigen::Matrix3d rot_x(double rad);
            Eigen::Matrix3d rot_y(double rad);
            Eigen::Matrix3d rot_z(double rad);
    };
    class Unit_Conv {
        public:
            double deg2rad(double a);
            Eigen::VectorXd deg2rad(Eigen::VectorXd vec);
            std::vector<double> deg2rad(std::vector<double> vec);
            double rad2deg(double a);
            Eigen::VectorXd rad2deg(Eigen::VectorXd vec);
            std::vector<double> rad2deg(std::vector<double> vec);

            void handAngle2Pos(std::vector<double>& vec);
            void handAngle2Pos(Eigen::VectorXd& vec);
            std::vector<int> handPos2Angle(std::vector<double> vec);
            Eigen::VectorXi handPos2Angle(Eigen::VectorXd vec);
    };

    class Kine_Solver {
        public:
            Eigen::Vector3d biasHand(Eigen::Vector3d pos, Eigen::Vector3d pose, Eigen::Vector2d jnt_thumb);
    };
    class KDL_Solver {
        public:
            KDL_Solver();
            ~KDL_Solver();
            JntPos ik_kdl(Eigen::Vector3d g, double armJnt, Eigen::Vector3d pose);
            KDL::Frame fk_kdl(Eigen::VectorXd jntPos, Eigen::Vector2d thumbPos);
            KDL::Frame fk_kdl(Eigen::VectorXd jntPos);

        public:
            unsigned int nj;
            double eps = 1e-10;
            double eps_joints = 1e-15;
            int maxiter = 500;

            KDL::Tree my_tree;
            KDL::Chain chain; 
            KDL::ChainFkSolverPos_recursive fksolver = KDL::ChainFkSolverPos_recursive(chain);
            KDL::ChainIkSolverPos_LMA iksolver = KDL::ChainIkSolverPos_LMA(chain, eps, maxiter, eps_joints);
    };
}

namespace TRAJ_PLAN {
    class Interpolation{
        public:
            Plan_Res quinitic_poly(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt);
            Plan_Res cub_spline(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt, double v0, double vn);

            Plan_Res_MulDOF jnt_quin_poly(Eigen::VectorXd& jnt_pos_init, JointInterPoints ips);
            Plan_Res_MulDOF jnt_cub_spline(Eigen::VectorXd& jnt_pos_init, JointInterPoints ips, Eigen::MatrixXd vel_ends = Eigen::MatrixXd::Zero(7,2));
            Plan_Res_MulDOF cart_quin_poly(JntEnd& jnt_pos_init, CartInterPoints ips);
            Plan_Res_MulDOF cart_cub_spline(JntEnd& jnt_pos_init, CartInterPoints ips, Eigen::MatrixXd vel_ends = Eigen::MatrixXd::Zero(7,2));
            
            Plan_Res_MulDOF stay_still(Eigen::VectorXd& jnt_pos, int n, double dt);
    };         
    
    class DMP{
        public:
            Plan_Res_MulDOF action1_dmp(Eigen::VectorXd& jnt_end, DmpTargetPoints target, bool check_start_pos = true, bool ikine_info_show = false, bool plan_info_show = false);
        private:
            Demon_Traj dataExt_(std::string pathName1, std::string pathName2);
            Train_Res dmpTrain_(Demon_Traj data, Train_Par par);
            Plan_Res_MulDOF lnrCmp_(Plan_Res_MulDOF traj, Eigen::Vector4d g);
            Plan_Res_MulDOF dmpRegress_(Train_Res trainRes, Regress_Par par);
            Plan_Res_MulDOF limitAmplitude_(Plan_Res_MulDOF traj, Eigen::Vector4d y0, Eigen::Vector4d g, Eigen::Vector4d jntLimit);
            Plan_Res_MulDOF dmp_(std::string pathJntPos, std::string pathAngArm, Train_Par trainPar, Eigen::VectorXd theta_init, DmpTargetPoints target, Eigen::Vector4d jntLimit, bool ikine_info_show = false, bool plan_info_show = false);
    };

    class Traj_Adjust{
        public:
            Plan_Res_MulDOF traj_splic(Eigen::VectorXd& jnt_end, Plan_Res_MulDOF action1, Plan_Res_MulDOF action2);
            Plan_Res_MulDOF traj_extend(Plan_Res_MulDOF action, int n, double dt);
            Plan_Res_MulDOF traj_inv(Eigen::VectorXd& jnt_end, Plan_Res_MulDOF traj_ori);
    };
}
#endif