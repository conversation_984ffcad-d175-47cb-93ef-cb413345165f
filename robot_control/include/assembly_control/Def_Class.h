#ifndef _DEF_CLASS_H2
#define _DEF_CLASS_H2

#include "Def_Struct.h"
#include "string"
// #include "ros/ros.h"
// #include "sensor_msgs/JointState.h"
#include <kdl/chain.hpp>

using namespace std;

namespace DATA_PROC_FUNC {
    class DATA_SAVE {
        public:
            void save2txt(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, FILE *fpWrite);
    };
    class DATA_PUB {
        public:
            void pub2rviz(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
            void pub2gazebo(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
            void pub2motors(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
    };
}

namespace PLANNING_FUNC {
    class DMP_FUNC {
        public:
            Demon_Traj dataExt(string pathName1, string pathName2);
            Train_Res dmpTrain(Demon_Traj data, Train_Par par);
            Plan_Res_MulDOF dmpRegress(Train_Res trainRes, Regress_Par par);
            Plan_Res_MulDOF lnrCmp(Plan_Res_MulDOF traj, Vector4d g);
            Plan_Res_MulDOF limitAmplitude(Plan_Res_MulDOF traj, Vector4d y0, Vector4d g, Vector4d jntLimit);
    };
    class KINEMATICS {
        public:
            JntPos invKine_JntPos(Vector3d g, double armJnt, Vector3d pose);
            KineRes fkine(VectorXd theta);
            Vector3d biasHand(Vector3d pose, Vector3d baisVec);
    };
    class INTERPOLATION {
        public:
            Plan_Res quinitic_poly_inter(VectorXd t_seg, VectorXd pos_seg, double dt);
            Plan_Res cub_spline_inter(VectorXd t_seg, VectorXd pos_seg, double dt, double v0=0.0, double vn=0.0);
    };
}

namespace TRAJ_PLAN_old {
    class DMP{
        public:
            Plan_Res_MulDOF dmp(string pathJntPos, string pathAngArm, Train_Par trainPar, VectorXd theta_init, VectorXd end_pose, Vector4d jntLimit, double dt, double tau = 1, bool useSelfDefPhi = false, double phi = 0, bool ikine_info_show = false, bool plan_info_show = false);
    };
    class INTERPOLATION{
        public:
            Plan_Res_MulDOF quinitic_poly_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, double dt);
            Plan_Res_MulDOF cub_spline_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, MatrixXd vel_ends, double dt);
    };
    class ADJUST{
        public:
            Plan_Res_MulDOF traj_splic(Plan_Res_MulDOF action1, Plan_Res_MulDOF action2);
            Plan_Res_MulDOF traj_extend(Plan_Res_MulDOF action, int n, double dt);
    };
}

namespace ACT_LIB {
    class DMP{
        public:
            Plan_Res_MulDOF action1_dmp(VectorXd initial_pose, VectorXd end_pose, double dt, double tau = 1, bool useSelfDefPhi = false, double phi = 0, bool ikine_info_show = false, bool plan_info_show = false);
    };
    class INTER{
        public:
            Plan_Res_MulDOF stay_still(VectorXd jnt_pos, int n, double dt);
    };
    class MOVE{
        public:
            void move(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, int move_mode);
    };
    class PLAN{
        public:
            Plan_Res_MulDOF joint_plan(std::array<double, 7> start, std::array<double, 7> end, double t, double dt);
            Plan_Res_MulDOF multi_joint_plan(vector<std::array<double, 7>> joint_point, vector<double> t, double dt);
            Plan_Res_MulDOF line_plan(Eigen::VectorXd pose_start, Eigen::VectorXd pose_end, KDL::Chain chain, double t, double dt);
            Plan_Res_MulDOF assembly1(Eigen::VectorXd p, Eigen::VectorXd rpy, KDL::Chain chain, double t, double dt, int flag);
            Plan_Res_MulDOF assembly2(Eigen::VectorXd p, Eigen::VectorXd rpy, KDL::Chain chain, double t, double dt, int flag);
    };    
}
#endif