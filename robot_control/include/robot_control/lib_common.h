#pragma once

#include <stdio.h>
#include <iostream>
#include <vector>
#include <Eigen/Core>
#include <Eigen/Dense>
#include "robot_control/struct.h"
#include <sensor_msgs/JointState.h>
using namespace std;
using namespace Eigen;


// 滑动平均滤波函数：对 Plan_Res_Arm 的 pos 进行滤波
MatrixXd movingAverageFilter(def_msgs::Plan_Res_Arm& plan, int window_size) {
    int rows = plan.pos.rows();
    int cols = plan.pos.cols();

    // 创建一个新的 MatrixXd 来存储滤波后的结果
    MatrixXd smoothed_pos = plan.pos;

    // 遍历每一列，进行滑动平均处理
    for (int col = 0; col < cols; ++col) {
        for (int row = 0; row < rows; ++row) {
            int count = 0;
            double sum = 0.0;
            
            // 计算滑动窗口内的平均值
            for (int k = row; k >= 0 && count < window_size; --k, ++count) {
                sum += plan.pos(k, col);
            }
            
            // 计算滑动平均并存入 smoothed_pos
            smoothed_pos(row, col) = sum / count;
        }
    }

    return smoothed_pos;
}

std::array<double, 7> arr_give(double num0,double num1,double num2,double num3,double num4,double num5,double num6) {
	std::array<double, 7> result;
	result[0] = num0;
	result[1] = num1;
	result[2] = num2;
	result[3] = num3;
	result[4] = num4;
	result[5] = num5;
	result[6] = num6;
	return result;
}

double** Interp5rdPoly_Param_fcn(double* qp0, double* qv0, double* qa0, double* qpf, double* qvf, double* qaf, double  tf)
{
	const int M = sizeof(qp0[0]);
	double** aa = new double* [M];
	for (int i = 0; i < M; i++)
		aa[i] = new double[6];
	for (size_t i = 0; i < M; i++)
	{
		aa[i][0] = qp0[i];
		aa[i][1] = qv0[i];
		aa[i][2] = qa0[i] / 2.0;
		aa[i][3] = (20 * (qpf[i] - qp0[i]) - (8 * qvf[i] + 12 * qv0[i]) * tf + (qaf[i] - 3 * qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 3));
		aa[i][4] = (-30 * (qpf[i] - qp0[i]) + (14 * qvf[i] + 16 * qv0[i]) * tf - (2 * qaf[i] - 3 * qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 4));
		aa[i][5] = (12 * (qpf[i] - qp0[i]) - (6 * qvf[i] + 6 * qv0[i]) * tf + (qaf[i] - qa0[i]) * pow(tf, 2)) / (2 * pow(tf, 5));
	}
	return aa;
}

double** Interp5rdPoly_Data_t_fcn(double** aa, double  tt, const int nn)
{
	const int M = 3;
	double** aaa = new double* [M];
	for (int i = 0; i < M; i++)
		aaa[i] = new double[nn];
	for (size_t i = 0; i < nn; i++)
	{
		aaa[0][i] = aa[i][0] + aa[i][1] * tt + aa[i][2] * pow(tt, 2) + aa[i][3] * pow(tt, 3) + aa[i][4] * pow(tt, 4) + aa[i][5] * pow(tt, 5);
		aaa[1][i] = aa[i][1] + 2 * aa[i][2] * tt + 3 * aa[i][3] * pow(tt, 2) + 4 * aa[i][4] * pow(tt, 3) + 5 * aa[i][5] * pow(tt, 4);
		aaa[2][i] = 2 * aa[i][2] + 6 * aa[i][3] * tt + 12 * aa[i][4] * pow(tt, 2) + 20 * aa[i][5] * pow(tt, 3);
	}
	return aaa;
}

Eigen::VectorXd admittance_ctrl_REI_classical(Eigen::VectorXd fd, Eigen::VectorXd fe, double dt)
{
    static Eigen::VectorXd x = Eigen::MatrixXd::Zero(6,1);
    static Eigen::VectorXd dx = Eigen::MatrixXd::Zero(6,1);
    static Eigen::VectorXd sum = Eigen::MatrixXd::Zero(6,1);
    Eigen::VectorXd Me(6), Be(6), ddx(6); 
    Me << 10, 10, 10, 10, 10, 10;
    Be << 20, 20, 20, 20, 20, 20;//650
    

    double ki = 0.00000000000000001;  // 0.005727;//0.4275 
    sum = sum + (fe - fd) * dt;
    ddx = (fe - fd + 0 * sum).cwiseQuotient(Me) - Be.cwiseProduct(dx).cwiseQuotient(Me);
    // std::cout<<"ddxe"<<std::endl<<ddx(2)<<std::endl;
    dx = dx + ddx * dt;
    // std::cout<<"dxe"<<std::endl<<dx(2)<<std::endl;
    x = x + dx * dt;
    // std::cout<<"xe"<<std::endl<<x(2)<<std::endl;
    return x;
}

void array_cout(std::string s, std::array<double, 7> joint){
	std::cout << s << ": ";                                             
    for (size_t i = 0; i < 7; i++)
    {
        std::cout << joint[i] << " ";
    }
    std::cout << std::endl;
}

void pose_cout(std::string s, def_msgs::Pose pose){
	std::cout << s << ": " << std::endl 
			<< "  T:" << std::endl << pose.T << std::endl
			<< "  p: " << pose.position[0] << " " << pose.position[1] << " " << pose.position[2] << std::endl
			<< "  quat(w, x, y, z): " << pose.quat.w() << " " << pose.quat.x() << " " << pose.quat.y() << " " << pose.quat.z() << std::endl
			<< "  rpy: " << pose.rpy[0] << " " << pose.rpy[1] << " " << pose.rpy[2] << std::endl;
}

void EigenMatrixToPose(const Eigen::Matrix4d& T, def_msgs::Pose& pose) {
    // 赋值齐次变换矩阵
    pose.T = T;

    // 提取平移向量
    pose.position = T.block<3, 1>(0, 3);

    // 提取旋转矩阵并转为四元数
    Eigen::Matrix3d rotation_matrix = T.block<3, 3>(0, 0);
    pose.quat = Eigen::Quaterniond(rotation_matrix);
	pose.quat.normalize();

    // 从旋转矩阵计算欧拉角
	Vector3d rpy = rotation_matrix.eulerAngles(2, 1, 0); // p y r
    pose.rpy[0] = rpy[2];
	pose.rpy[1] = rpy[1];
	pose.rpy[2] = rpy[0];
}

void Getpos(sensor_msgs::JointState JointState, std::array<double, 7>& l_pose, std::array<double, 7>& r_pose) {
	for (size_t i = 0; i < 7; i++)
	{
		l_pose[i] = JointState.position[i];
		r_pose[i] = JointState.position[i+7];
	}
	array_cout("l_pose", l_pose);
    array_cout("r_pose", r_pose);
	return;
}
void Getpos(sensor_msgs::JointState JointState, std::array<double, 7>& l_pose, std::array<double, 7>& r_pose, int f) {

	for (size_t i = 0; i < 7; i++)
	{
		l_pose[i] = JointState.position[i];
	}

	for (size_t i = 0; i < 4; i++)
	{
		r_pose[i] = JointState.position[i+7];
	}
    r_pose[4] = 0;
    r_pose[5] = 0;
    r_pose[6] = 0;
	array_cout("l_pose", l_pose);
    array_cout("r_pose", r_pose);
	return;
}
void Getpos(sensor_msgs::JointState JointState, std::array<double, 7>& pose) {
	for (size_t i = 0; i < 7; i++)
	{
		pose[i] = JointState.position[i];
	}
    array_cout("pose", pose);
	return;
}

void Getpos(sensor_msgs::JointState JointState, std::array<double, 7>& pose, int f) {
	for (size_t i = 0; i < 4; i++)
	{
		pose[i] = JointState.position[i];
	}
    pose[4] = 0;
    pose[5] = 0;
    pose[6] = 0;
    array_cout("pose", pose);
	return;
}

Eigen::Matrix3d rpy2rotationMatrix(double yaw, double pitch, double roll) {
    // 绕 Z 轴的旋转矩阵 (偏航)
    Eigen::Matrix3d Rz;
    Rz << cos(yaw), -sin(yaw), 0,
          sin(yaw),  cos(yaw), 0,
          0,         0,        1;

    // 绕 Y 轴的旋转矩阵 (俯仰)
    Eigen::Matrix3d Ry;
    Ry << cos(pitch), 0, sin(pitch),
          0,           1, 0,
          -sin(pitch), 0, cos(pitch);

    // 绕 X 轴的旋转矩阵 (横滚)
    Eigen::Matrix3d Rx;
    Rx << 1, 0,          0,
          0, cos(roll), -sin(roll),
          0, sin(roll),  cos(roll);

    // 按 Z-Y-X 顺序组合旋转矩阵
    return Rz * Ry * Rx;
}
