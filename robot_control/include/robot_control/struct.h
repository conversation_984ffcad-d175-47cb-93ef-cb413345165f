#ifndef STRUCT_H
#define STRUCT_H

#include "eigen3/Eigen/Core"
#include "eigen3/Eigen/Dense"
#include "eigen3/Eigen/LU"

using namespace Eigen;

namespace def_msgs{ 
    struct Pose
    {
        Matrix4d T;
        Vector3d position;
        Quaterniond quat; //w, x, y, z
        Vector3d rpy; //roll, pitch, yaw 
    };

    typedef enum arm{
        left_arm,
        right_arm,
        dual_arm,
    }ARM;

    typedef enum ctrl_mode_type{
        stop = 0,
        rviz_ctrl,
        gazebo_ctrl,
        robot_ctrl
    }CONTROL_MODE_TYPE;


    struct Plan_Res_Arm
    {
        MatrixXd pos;
        MatrixXd vel;
        MatrixXd acc;
        VectorXd t;
        int error_flag = 0;
    };

    struct Plan_Res_Dual{
        Plan_Res_Arm left;
        Plan_Res_Arm right;
    };

}

#endif