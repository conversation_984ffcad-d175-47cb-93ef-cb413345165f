#pragma once

#include "robot_control/lib_common.h"

#include <string.h>
#include <ros/ros.h>
#include <vector>
#include <cmath>
#include <tf/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <actionlib/client/simple_action_client.h>
#include <boost/thread.hpp>
#include "thread"

#include "std_msgs/String.h"
#include "std_msgs/Float64.h"
#include "std_msgs/Float64MultiArray.h"
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Pose.h>
#include <control_msgs/GripperCommandActionGoal.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Int8.h>

#include <kdl/chain.hpp>
#include <kdl/chainfksolverpos_recursive.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolverpos_nr.hpp>
#include <kdl/chainiksolverpos_nr_jl.hpp>
#include <kdl/chainjnttojacsolver.hpp>
#include <kdl/chainiksolvervel_pinv.hpp>
#include <kdl/kdl.hpp>
#include <kdl/jacobian.hpp>
#include <kdl/tree.hpp>
#include <kdl/segment.hpp>
#include <kdl/chainfksolver.hpp>
#include <kdl_parser/kdl_parser.hpp>
#include <kdl/frames_io.hpp>

#include "robot_control/set_angle.h"
#include "robot_control/set_speed.h"
#include "robot_control/set_current_limit.h"
#include "robot_control/get_angle_act.h"
#include "robot_control/get_current.h"
#include "robot_control/get_force_act.h"
#include "robot_control/get_error.h"

using namespace KDL;
using namespace std;
using namespace Eigen;
using namespace def_msgs;

array<double, 7> jnt_effort,jnt_position;
array<double, 7> jnt_effort_l,jnt_position_l,jnt_effort_r,jnt_position_r, jnt_pos_current_l2, jnt_pos_current_r2, jnt_pos_vir_l2, jnt_pos_vir_r2;
def_msgs::Pose car_pos_vir_l2, car_pos_vir_r2, car_pos_current_l2, car_pos_current_r2;
MatrixXd theta_limit;
MatrixXd effort_limit;
double dt = 0.001;
int istest = 0;
CONTROL_MODE_TYPE ctrl_mode = rviz_ctrl;

array<double, 80> container;
bool detect_flag = false;

int flag_cam = 0;
int flag_door = 0;

namespace ROBOT_CONTROL{
    class robot_control
    {
        private:
            Chain l_chain, r_chain;
            ros::NodeHandle nh;  
            ros::Publisher pub_motor, pub_rviz;
            ros::Subscriber current_sub;
            bool CurSubFlag;
            sensor_msgs::JointState CurrentState;
            // MatrixXd theta_limit;
            // MatrixXd effort_limit;

        public:
            MatrixXd theta_limit;
            MatrixXd effort_limit;
            robot_control();
            robot_control(const char* urdf_path, const char* base_link, const char* l_end_link, const char* r_end_link);
            // ~robot_control();
            MatrixXd computejacobian(std::array<double, 7> jnt_pos_current, int arm);
            int kdl_fk(std::array<double, 7> joint, def_msgs::Pose& pose, int arm);
            int kdl_ik(std::array<double, 7> j, Eigen::Vector3d vec, Eigen::Vector3d rpy, std::array<double, 7>& result, int arm);
            bool getCurrentState(sensor_msgs::JointState& JointState);
            void CurrentStateCallback(const sensor_msgs::JointState::ConstPtr &msg);
            Plan_Res_Arm four_joint_plan(std::array<double, 4> qp, std::array<double, 4> qv, std::array<double, 4> qa, std::array<double, 4> qp2, double t, double dt);
            Plan_Res_Arm multi_joint_plan(vector<array<double, 7>> joint_point, vector<double> t, double dt);
            Plan_Res_Arm line_plan(array<double, 7> q_init, VectorXd pose_start, VectorXd pose_end, double t, int arm);
            Plan_Res_Arm circle_plan(array<double, 7> q_init, Vector3d vec, Vector3d rpy, double t, int arm);
            void move(Plan_Res_Dual motion, int move_mode);
            void move(Plan_Res_Dual motion, int move_mode, int mode);
            void move(Plan_Res_Arm motion, int move_mode);
            void move(Plan_Res_Arm motion, int move_mode, int four);
            void move_ele(Plan_Res_Dual motion, int move_mode);
            void move_ele(Plan_Res_Dual motion, int move_mode, int mode);

            
    };
}

namespace CAMERA_READ{
    class camera_read
    {
        private:
            vector<double> container;
            bool detect_flag, detect_flag2;
            ros::NodeHandle nh;
            ros::Subscriber vision_pose_base, door_plane_normal;
            std_msgs::Float64MultiArray msg_camera, msg_camera2;
        public:
            camera_read();
            // ~camera_read();
            bool getCurrentState(std_msgs::Float64MultiArray& camera_data);
            bool get_plane_normal(std_msgs::Float64MultiArray& camera_data);
            void CurrentStateCallback(const std_msgs::Float64MultiArray::ConstPtr& msg);
            void CurrentStateCallback2(const std_msgs::Float64MultiArray::ConstPtr& msg);
    };
}

namespace INSPIRE_HAND{
    class Pos_Ctrl{
        public:
            Pos_Ctrl(){
                nh_ = ros::NodeHandle();

                set_speed1_ = nh_.serviceClient<robot_control::set_speed>("inspire_hand/set_speed");
                set_angle1_ = nh_.serviceClient<robot_control::set_angle>("inspire_hand/set_angle");
                get_angle1_ = nh_.serviceClient<robot_control::get_angle_act>("inspire_hand/get_angle_act");
                get_force1_ = nh_.serviceClient<robot_control::get_force_act>("inspire_hand/get_force_act");
                get_current1_ = nh_.serviceClient<robot_control::get_current>("inspire_hand/get_current");

                set_speed2_ = nh_.serviceClient<robot_control::set_speed>("inspire_hand/set_speed");
                set_angle2_ = nh_.serviceClient<robot_control::set_angle>("inspire_hand/set_angle");
                get_angle2_ = nh_.serviceClient<robot_control::get_angle_act>("inspire_hand/get_angle_act");
                get_force2_ = nh_.serviceClient<robot_control::get_force_act>("inspire_hand/get_force_act");
                get_current2_ = nh_.serviceClient<robot_control::get_current>("inspire_hand/get_current");

                default_pos_ = {999,999,999,999,999,999};
                default_vel_ = {200,200,200,200,200,200};
            }
            ~Pos_Ctrl(){}
            
            void serviceCall(const vector<int>& pos_cmd, const vector<int>& vel_cmd, int hand_id);

            void rightHand();
            void rightHand(const vector<int>& pos_cmd);
            void rightHand(const vector<int>& pos_cmd, const vector<int>& vel_cmd);
            void leftHand();
            void leftHand(const vector<int>& pos_cmd);
            void leftHand(const vector<int>& pos_cmd, const vector<int>& vel_cmd);
            void doubleHand();
            void doubleHand(const vector<int>& pos_cmd_r, const vector<int>& pos_cmd_l);
            void doubleHand(const vector<int>& pos_cmd_r, const vector<int>& vel_cmd_r, const vector<int>& pos_cmd_l, const vector<int>& vel_cmd_l);

        private:
            ros::NodeHandle nh_;
            
            ros::ServiceClient set_angle1_;
            ros::ServiceClient set_speed1_;
            ros::ServiceClient get_angle1_;
            ros::ServiceClient get_force1_;
            ros::ServiceClient get_current1_;

            ros::ServiceClient set_angle2_;
            ros::ServiceClient set_speed2_;
            ros::ServiceClient get_angle2_;
            ros::ServiceClient get_force2_;
            ros::ServiceClient get_current2_;

            robot_control::set_angle fingle_angle1_;
            robot_control::set_speed fingle_speed1_;
            robot_control::get_angle_act fingle_angle_act1_;
            robot_control::get_force_act fingle_force_act1_;
            robot_control::get_current fingle_current1_;

            robot_control::set_angle fingle_angle2_;
            robot_control::set_speed fingle_speed2_;
            robot_control::get_angle_act fingle_angle_act2_;
            robot_control::get_force_act fingle_force_act2_;
            robot_control::get_current fingle_current2_;

            vector<int> default_pos_;
            vector<int> default_vel_;

    };
}