/*
机械臂运动学运算
    - KDL求解器（正逆运动学）
    - 计算手腕到拇指指腹的偏置
*/

#include "arms_gen2_control/Def_Class.h"

namespace KINEMATICS {
    /**
     * @brief KDL_Solver类的构造函数
     * @details 初始化KDL::Tree对象，解析指定的urdf文件
     */
    KDL_Solver::KDL_Solver(){
        kdl_parser::treeFromFile("/home/<USER>/src/arms_gen2_control/model/urdf/arms_gen2_hand.urdf",my_tree); 
    }

    KDL_Solver::~KDL_Solver(){}

    /**
     * @brief KDL逆运动学求解
     * @details 根据腕关节处的位姿和臂角求解7个关节的角度
     */
    JntPos KDL_Solver::ik_kdl(Eigen::Vector3d g, double phi, Eigen::Vector3d pose){
        if(!my_tree.getChain("base_footprint","r_hand_base_Link",chain)){ 
            ROS_ERROR("Error:could not find chain"); 
            exit(0);
        } 
        nj = chain.getNrOfJoints();
        iksolver.updateInternalDataStructures();
        fksolver.updateInternalDataStructures();

        KDL::Rotation rot = KDL::Rotation::RPY(pose(2),pose(1),pose(0));
        KDL::Vector vec(g(0), g(1), g(2)+0.8);
        KDL::Frame T_base_goal(rot,vec);

        bool kinematics_status;
        KDL::JntArray jointpositions(nj); 
        KDL::JntArray q_init(nj);

        for(unsigned int i = 0; i < nj;i++){
            q_init(i) = 0;
            if(i == 2){
                q_init(i) = phi;
            }
        }
        
        JntPos res;
        Eigen::VectorXd theta(nj); 
        kinematics_status = iksolver.CartToJnt(q_init, T_base_goal, jointpositions);
        if(kinematics_status == 0){
            for(unsigned int i = 0; i < nj;i++){
                theta(i) = jointpositions(i);
            }
            res.solved_flag = 1;
            res.theta = theta;
        }else{
            res.error_flag = 1;
            ROS_WARN("[KDL]ik solver failed!");
        }

        Eigen::MatrixXd theta_limit(7,2);
        theta_limit << -M_PI, M_PI,
                       -M_PI*135/180, M_PI*5/180,
                       -M_PI/2, M_PI/2,
                       0, M_PI*110/180,
                       -M_PI, M_PI,
                       -M_PI/2, M_PI/2,
                       -M_PI/2, M_PI/2;
        for(int i = 0; i < 7; i++){
            if (res.theta(i) < theta_limit(i,0) || res.theta(i) > theta_limit(i,1)){
                res.error_flag = 2;
                ROS_WARN("[KDL]Joint%d %.4f exceeds the limit position[%.4f, %.4f]",i+1,res.theta(i)*180/M_PI,theta_limit(i,0)*180/M_PI,theta_limit(i,1)*180/M_PI);
                ROS_WARN("[KDL]The result of the solution:\n"
                "%.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
                res.theta(0)*180/M_PI, res.theta(1)*180/M_PI,
                res.theta(2)*180/M_PI, res.theta(3)*180/M_PI,
                res.theta(4)*180/M_PI, res.theta(5)*180/M_PI,
                res.theta(6)*180/M_PI);
            }
        }

        return res;
    }

    /**
     * @brief KDL正运动学求解
     * @details 根据臂的关节角度和手部大拇指关节角度解算末端位姿
     */
    KDL::Frame KDL_Solver::fk_kdl(Eigen::VectorXd jntPos, Eigen::Vector2d thumbPos){
        if(!my_tree.getChain("base_footprint","r_set_tool",chain)){ 
            ROS_ERROR("Error:could not find chain"); 
            exit(0);
        } 
        nj = chain.getNrOfJoints();
        iksolver.updateInternalDataStructures();
        fksolver.updateInternalDataStructures();
        
        KDL::JntArray jointposition = KDL::JntArray(nj);
        jointposition(0) = jntPos(0);
        jointposition(1) = jntPos(1);
        jointposition(2) = jntPos(2);
        jointposition(3) = jntPos(3);
        jointposition(4) = jntPos(4);
        jointposition(5) = jntPos(5);
        jointposition(6) = jntPos(6);
        jointposition(7) = thumbPos(0);
        jointposition(8) = thumbPos(1);
        jointposition(9) = thumbPos(1)*1.6;
        jointposition(10) = thumbPos(1)*2.4;

        KDL::Frame fk_res;
        KDL::Vector position;
        KDL::Rotation rotation;
        double roll, pitch, yaw;
        double qx, qy, qz, qw;
        if(fksolver.JntToCart(jointposition,fk_res)){
            position = fk_res.p;
            rotation = fk_res.M;
            rotation.GetRPY(roll, pitch, yaw);
            rotation.GetQuaternion(qx, qy, qz, qw);
            ROS_INFO("position: [%.4f, %.4f, %.4f]", position.x(), position.y(), position.z());
            ROS_INFO("rpy: [%.4f, %.4f, %.4f]", roll, pitch, yaw);
            ROS_INFO("quaternion: [%.4f, %.4f, %.4f, %.4f]", qx, qy, qz, qw);
        } 
        else{ 
            ROS_WARN("Error:could not calculate forward kinematics");
        }
        return fk_res; 
    }

    KDL::Frame KDL_Solver::fk_kdl(Eigen::VectorXd jntPos){
        if(!my_tree.getChain("base_footprint","r_hand_base_Link",chain)){ 
            ROS_ERROR("Error:could not find chain"); 
            exit(0);
        } 
        nj = chain.getNrOfJoints();
        iksolver.updateInternalDataStructures();
        fksolver.updateInternalDataStructures();
        
        KDL::JntArray jointposition = KDL::JntArray(nj);
        jointposition(0) = jntPos(0);
        jointposition(1) = jntPos(1);
        jointposition(2) = jntPos(2);
        jointposition(3) = jntPos(3);
        jointposition(4) = jntPos(4);
        jointposition(5) = jntPos(5);
        jointposition(6) = jntPos(6);

        KDL::Frame fk_res;
        KDL::Vector position;
        KDL::Rotation rotation;
        double roll, pitch, yaw;
        double qx, qy, qz, qw;
        if(fksolver.JntToCart(jointposition,fk_res)){
            position = fk_res.p;
            rotation = fk_res.M;
            rotation.GetRPY(roll, pitch, yaw);
            rotation.GetQuaternion(qx, qy, qz, qw);
            ROS_INFO("position: [%.4f, %.4f, %.4f]", position.x(), position.y(), position.z());
            ROS_INFO("rpy: [%.4f, %.4f, %.4f]", roll, pitch, yaw);
            ROS_INFO("quaternion: [%.4f, %.4f, %.4f, %.4f]", qx, qy, qz, qw);
        } 
        else{ 
            ROS_WARN("Error:could not calculate forward kinematics");
        }
        return fk_res; 
    }

    /**
     * @brief 拇指正运动学求解
     * @details 根据臂的腕部位姿和手部大拇指关节角度，计算腕部和拇指指腹之间的位置偏差
     */
    Eigen::Vector3d Kine_Solver::biasHand(Eigen::Vector3d pos, Eigen::Vector3d pose, Eigen::Vector2d jnt_thumb){
        KINEMATICS::Pose_Trans poseTrans;
        
        std::vector<Eigen::Vector3d> relative_eular_r = {
            Eigen::Vector3d(0, 1.5708, 0),
            Eigen::Vector3d(1.5708, 1.1727, -3.0531),
            Eigen::Vector3d(0, 0.018104, 1.744),
            Eigen::Vector3d(-0.0014192, 0.22451, 0.076172),
            Eigen::Vector3d(0, -0.27, 0)
        };

        std::vector<Eigen::Vector3d> relative_position_r = {
            Eigen::Vector3d(0.0844, 0.01496, -0.01705),
            Eigen::Vector3d(-0.012162, 0.0069529, -0.00925),
            Eigen::Vector3d(-0.017367, 0.05324, -0.0008),
            Eigen::Vector3d(0.022543, -0.0024047, -0.00079204),
            Eigen::Vector3d(0.017, 0.006, -0.002)
        };

        Eigen::Matrix3d T = poseTrans.rot_z(M_PI/2-pose(0))*poseTrans.rot_y(pose(1))*poseTrans.rot_x(pose(2));

        Eigen::Matrix3d T1 = T * poseTrans.rot_z(relative_eular_r[0](2))
                        * poseTrans.rot_y(relative_eular_r[0](1))
                        * poseTrans.rot_x(relative_eular_r[0](0))
                        * poseTrans.rot_z(-jnt_thumb(0));
        Eigen::Matrix3d T2 = T1 * poseTrans.rot_z(relative_eular_r[1](2))
                         * poseTrans.rot_y(relative_eular_r[1](1))
                         * poseTrans.rot_x(relative_eular_r[1](0))
                         * poseTrans.rot_z(jnt_thumb(1));
        Eigen::Matrix3d T3 = T2 * poseTrans.rot_z(relative_eular_r[2](2))
                         * poseTrans.rot_y(relative_eular_r[2](1))
                         * poseTrans.rot_x(relative_eular_r[2](0))
                         * poseTrans.rot_z(jnt_thumb(1)*1.6);
        Eigen::Matrix3d T4 = T3 * poseTrans.rot_z(relative_eular_r[3](2))
                         * poseTrans.rot_y(relative_eular_r[3](1))
                         * poseTrans.rot_x(relative_eular_r[3](0))
                         * poseTrans.rot_z(jnt_thumb(1)*2.4);
        Eigen::Matrix3d T5 = T4 * poseTrans.rot_z(relative_eular_r[4](2))
                         * poseTrans.rot_y(relative_eular_r[4](1))
                         * poseTrans.rot_x(relative_eular_r[4](0));
                        
        Eigen::Vector3d arm_tool;               
        arm_tool = pos - T *relative_position_r[0]
                       - T1*relative_position_r[1]
                       - T2*relative_position_r[2]
                       - T3*relative_position_r[3]
                       - T4*relative_position_r[4];
        return arm_tool;
    }
}