/*
因时灵巧手的api
    - 打开串口
    - 设置手指位置
    - 设置手指速度
    - 设置手指力
*/

#include "arms_gen2_control/Def_Class.h"

namespace DATA_PROC{
    /**
     * @brief 测试串口是否通信正常
     * @details checkPortOpen的子函数
     */
    int Data_Pub::initPort(serial::Serial *port, uint8_t hand_id){
        std::vector<uint8_t> output;
        output.push_back(0xEB);
        output.push_back(0x90);
        output.push_back(hand_id);
        output.push_back(0x04);
        output.push_back(0x11);
        output.push_back(0xFE);
        output.push_back(0x05);
        output.push_back(0x0C);

        unsigned int check_num = 0;

        int len = output[3] + 5;
        for (int i = 2; i < len - 1; i++)
            check_num = check_num + output[i];

        //Add checksum to the output buffer
        output.push_back(check_num & 0xff);

        //Send message to the module and wait for response
        port->write(output);

        ros::Duration(0.015).sleep();

        //Read response
        std::vector<uint8_t> input;
        port->read(input, (size_t)64);
        //ROS_INFO("ok");
        if (input.empty())
            return 0;
        else
            return 1;
    }

    /**
     * @brief 测试串口通信是否正常
     */
    void Data_Pub::checkPortOpen(serial::Serial *com_port){
        if (com_port->isOpen())
        {
            ROS_INFO_STREAM("Hand: Serial port " << port_name_ << " openned");
            int id_state1 = 0;
            while (1)
            {
                id_state1 = initPort(com_port_,hand_id1_);
                if (id_state1 == 1)
                    break;
                hand_id1_++;
                if (hand_id1_ >= 256)
                {
                    ROS_INFO("Id error!!!");
                    hand_id1_ = 1;
                }
            }

            //Get initial state and discard input buffer
            while (hand_state_ == 0xff)
            {
                hand_state_ = 0x00;
                //hand_state_ = getERROR(com_port_);
                ros::Duration(WAIT_FOR_RESPONSE_INTERVAL_).sleep();
            }
        }
        else
            ROS_ERROR_STREAM("Hand: Serial port " << port_name_ << " not opened");
    }
    
    /**
     * @brief 读取手指力
     */
    void Data_Pub::getFORCE_ACT(std::array<float, 6>& curforce_, int control_hand_id)
    {
        std::vector<uint8_t> output;
        //message from master to module
        output.push_back(0xEB);
        output.push_back(0x90);
        //module id
        // output.push_back(hand_id_);
        output.push_back(control_hand_id);
        //Data Length
        output.push_back(0x04);
        //Command get state
        output.push_back(0x11);
        output.push_back(0x2E);
        output.push_back(0x06);
        output.push_back(0x0C);
        //Checksum calculation
        unsigned int check_num = 0;
        int len = output[3] + 5;
        for (int i = 2; i < len - 1; i++)
            check_num = check_num + output[i];
        //Add checksum to the output buffer
        output.push_back(check_num & 0xff);
        //Send message to the module
        com_port_->write(output);

        ros::Duration(0.015).sleep();

        std::string s1;
        for (int i = 0; i < output.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", output[i]);
            s1 = s1 + str + " ";
        }
        //if (test_flags == 1)
        //ROS_INFO_STREAM("Write: " << s1);

        //Read response
        std::vector<uint8_t> input;
        while (input.empty())
        {
            com_port_->read(input, (size_t)64);
        }
        std::string s2;
        for (int i = 0; i < input.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", input[i]);
            s2 = s2 + str + " ";
        }
        //if (test_flags == 1)
        //ROS_INFO_STREAM("Read: " << s2);

        int temp[10] = {0};
        for (int j = 0; j < 6; j++)
        {
            temp[j] = ((input[8 + j * 2] << 8) & 0xff00) + input[7 + j * 2];
            if (temp[j] > 32768)
                temp[j] = temp[j] - 65536;
        }

        //ROS_INFO("Gripper: No error detected");
        /*ROS_INFO_STREAM("hand: current force: "
                        << temp[0]
                        << " "
                        << temp[1]
                        << " "
                        << temp[2]
                        << " "
                        << temp[3]
                        << " "
                        << temp[4]
                        << " "
                        << temp[5]
                );*/

        curforce_[0] = float(temp[0]);
        curforce_[1] = float(temp[1]);
        curforce_[2] = float(temp[2]);
        curforce_[3] = float(temp[3]);
        curforce_[4] = float(temp[4]);
        curforce_[5] = float(temp[5]);
    }

    /**
     * @brief 设置手指角度
     */
    bool Data_Pub::setANGLE(int angle0, int angle1, int angle2, int angle3, int angle4, int angle5, uint8_t hand_id){
        std::vector<uint8_t> output;
        //message from master to module
        output.push_back(0xEB);
        output.push_back(0x90);
        //module id
        // output.push_back(hand_id_);
        output.push_back(hand_id);
        //Data Length
        output.push_back(0x0D);
        //Command get state
        output.push_back(0x54);
        //output.push_back(0xCE);
        //output.push_back(0x05);

        int temp_int1, temp_int2, temp_int3, temp_int4, temp_int5, temp_int6;
        temp_int1 = angle0;
        temp_int2 = angle1;
        temp_int3 = angle2;
        temp_int4 = angle3;
        temp_int5 = angle4;
        temp_int6 = angle5;

        output.push_back(temp_int1 & 0xff);
        output.push_back((temp_int1 >> 8) & 0xff);
        output.push_back(temp_int2 & 0xff);
        output.push_back((temp_int2 >> 8) & 0xff);
        output.push_back(temp_int3 & 0xff);
        output.push_back((temp_int3 >> 8) & 0xff);
        output.push_back(temp_int4 & 0xff);
        output.push_back((temp_int4 >> 8) & 0xff);
        output.push_back(temp_int5 & 0xff);
        output.push_back((temp_int5 >> 8) & 0xff);
        output.push_back(temp_int6 & 0xff);
        output.push_back((temp_int6 >> 8) & 0xff);
        //Checksum calculation

        unsigned int check_num = 0;
        int len = output[3] + 5;

        for (int i = 2; i < len - 1; i++)
            check_num = check_num + output[i];
        //Add checksum to the output buffer
        output.push_back(check_num & 0xff);

        //Send message to the module
        com_port_->write(output);

        ros::Duration(0.015).sleep();

        std::string s1;
        for (int i = 0; i < output.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", output[i]);
            s1 = s1 + str + " ";
        }

        com_port_->flush();  // 清空串口缓冲区
        std::vector<uint8_t> input;
        auto start_time = std::chrono::steady_clock::now();
        while (input.empty())
        {
            com_port_->read(input, (size_t)64);
            auto current_time = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time).count() > 2000) {
                // 超时处理
                ROS_WARN("Read timeout");
                return false;
            }
        }

        std::string s2;
        for (int i = 0; i < input.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", input[i]);
            s2 = s2 + str + " ";
        }

        int temp[10] = {0};
        for (int j = 0; j < 1; j++)
            temp[j] = input[5];
        if (temp[0] == 1)
            return true;
        else
            return false;
    }

    /**
     * @brief 设置手指速度
     */
    bool Data_Pub::setSPEED(int speed0, int speed1, int speed2, int speed3, int speed4, int speed5, uint8_t hand_id){
        std::vector<uint8_t> output;
        //message from master to module
        output.push_back(0xEB);
        output.push_back(0x90);
        //module id
        // output.push_back(hand_id_);
        output.push_back(hand_id);
        //Data Length
        output.push_back(0x0F);
        //Command get state
        output.push_back(0x12);
        output.push_back(0xF2);
        output.push_back(0x05);

        unsigned int temp_int1, temp_int2, temp_int3, temp_int4, temp_int5, temp_int6;
        temp_int1 = (unsigned int)speed0;
        temp_int2 = (unsigned int)speed1;
        temp_int3 = (unsigned int)speed2;
        temp_int4 = (unsigned int)speed3;
        temp_int5 = (unsigned int)speed4;
        temp_int6 = (unsigned int)speed5;

        output.push_back(temp_int1 & 0xff);
        output.push_back((temp_int1 >> 8) & 0xff);
        output.push_back(temp_int2 & 0xff);
        output.push_back((temp_int2 >> 8) & 0xff);
        output.push_back(temp_int3 & 0xff);
        output.push_back((temp_int3 >> 8) & 0xff);
        output.push_back(temp_int4 & 0xff);
        output.push_back((temp_int4 >> 8) & 0xff);
        output.push_back(temp_int5 & 0xff);
        output.push_back((temp_int5 >> 8) & 0xff);
        output.push_back(temp_int6 & 0xff);
        output.push_back((temp_int6 >> 8) & 0xff);

        //Checksum calculation
        unsigned int check_num = 0;
        int len = output[3] + 5;

        for (int i = 2; i < len - 1; i++)
            check_num = check_num + output[i];

        //Add checksum to the output buffer
        output.push_back(check_num & 0xff);

        //Send message to the module
        com_port_->write(output);

        ros::Duration(0.015).sleep();

        std::string s1;

        for (int i = 0; i < output.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", output[i]);
            s1 = s1 + str + " ";
        }

        std::vector<uint8_t> input;

        while (input.empty())
        {
            com_port_->read(input, (size_t)64);
        }

        std::string s2;
        for (int i = 0; i < input.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", input[i]);
            s2 = s2 + str + " ";
        }

        int temp[10] = {0};
        for (int j = 0; j < 1; j++)
            temp[j] = input[7];
        if (temp[0] == 1)
            return true;
        else
            return false;
    }

    /**
     * @brief 设置手指力
     */
    bool Data_Pub::setFORCE(int force0, int force1, int force2, int force3, int force4, int force5, uint8_t hand_id){
        std::vector<uint8_t> output;

        //message from master to module
        output.push_back(0xEB);
        output.push_back(0x90);
        //module id
        output.push_back(hand_id);
        //Data Length
        output.push_back(0x0F);
        //Command get state
        output.push_back(0x12);
        output.push_back(0xDA);
        output.push_back(0x05);

        unsigned int temp_int1, temp_int2, temp_int3, temp_int4, temp_int5, temp_int6;
        temp_int1 = (unsigned int)force0;
        temp_int2 = (unsigned int)force1;
        temp_int3 = (unsigned int)force2;
        temp_int4 = (unsigned int)force3;
        temp_int5 = (unsigned int)force4;
        temp_int6 = (unsigned int)force5;

        output.push_back(temp_int1 & 0xff);
        output.push_back((temp_int1 >> 8) & 0xff);
        output.push_back(temp_int2 & 0xff);
        output.push_back((temp_int2 >> 8) & 0xff);
        output.push_back(temp_int3 & 0xff);
        output.push_back((temp_int3 >> 8) & 0xff);
        output.push_back(temp_int4 & 0xff);
        output.push_back((temp_int4 >> 8) & 0xff);
        output.push_back(temp_int5 & 0xff);
        output.push_back((temp_int5 >> 8) & 0xff);
        output.push_back(temp_int6 & 0xff);
        output.push_back((temp_int6 >> 8) & 0xff);

        //Checksum calculation
        unsigned int check_num = 0;
        int len = output[3] + 5;

        for (int i = 2; i < len - 1; i++)
            check_num = check_num + output[i];

        //Add checksum to the output buffer
        output.push_back(check_num & 0xff);

        //Send message to the module
        com_port_->write(output);

        ros::Duration(0.015).sleep();

        std::string s1;
        for (int i = 0; i < output.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", output[i]);
            s1 = s1 + str + " ";
        }
        if (test_flags_ == 1)
            ROS_INFO_STREAM("Write: " << s1);
        std::vector<uint8_t> input;

        while (input.empty())
        {
            com_port_->read(input, (size_t)64);
        }

        std::string s2;
        for (int i = 0; i < input.size(); ++i)
        {
            char str[16];
            sprintf(str, "%02X", input[i]);
            s2 = s2 + str + " ";
        }
        if (test_flags_ == 1)
            ROS_INFO_STREAM("Read: " << s2);
        int temp[10] = {0};
        for (int j = 0; j < 1; j++)
            temp[j] = input[7];
        if (temp[0] == 1)
            return true;
        else
            return false;
    }

    void Data_Pub::rightHand(){
        setANGLE(default_pos_[0],default_pos_[1],default_pos_[2],default_pos_[3],default_pos_[4],default_pos_[5],2);
    }
    void Data_Pub::leftHand(){
        setANGLE(default_pos_[0],default_pos_[1],default_pos_[2],default_pos_[3],default_pos_[4],default_pos_[5],1);
    }
    void Data_Pub::doubleHand(){
        // 使用lambda表达式绑定this指针
        std::thread t1([this]() { this->rightHand(); }); // 创建线程执行右手控制
        std::thread t2([this]() { this->leftHand(); });  // 创建线程执行左手控制

        t1.join(); // 等待右手线程结束
        t2.join(); // 等待左手线程结束
    }

    void Data_Pub::rightHand(const std::vector<int>& pos_cmd){
        setANGLE(pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5],2);
    }
    void Data_Pub::leftHand(const std::vector<int>& pos_cmd){
        setANGLE(pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5],1);
    }
    void Data_Pub::doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& pos_cmd_l){
        std::thread t1([this, pos_cmd_r]() { this->rightHand(pos_cmd_r); });
        std::thread t2([this, pos_cmd_l]() { this->leftHand(pos_cmd_l); });

        t1.join(); // 等待右手线程结束
        t2.join(); // 等待左手线程结束
    }
    void Data_Pub::rightHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd){
        setSPEED(vel_cmd[0],vel_cmd[1],vel_cmd[2],vel_cmd[3],vel_cmd[4],vel_cmd[5],2);
        setANGLE(pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5],2);
    }
    void Data_Pub::leftHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd){
        setSPEED(vel_cmd[0],vel_cmd[1],vel_cmd[2],vel_cmd[3],vel_cmd[4],vel_cmd[5],1);
        setANGLE(pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5],1);
    }
    void Data_Pub::doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& vel_cmd_r, const std::vector<int>& pos_cmd_l, const std::vector<int>& vel_cmd_l){
        std::thread t1([this, pos_cmd_r, vel_cmd_r]() { this->rightHand(pos_cmd_r, vel_cmd_r); });
        std::thread t2([this, pos_cmd_l, vel_cmd_l]() { this->leftHand(pos_cmd_l, vel_cmd_l); });

        t1.join(); // 等待右手线程结束
        t2.join(); // 等待左手线程结束
    }

}