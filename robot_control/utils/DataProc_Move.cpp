#include "arms_gen2_control/Def_Class.h"

namespace DATA_PROC{
    void Data_Pub::move(ArmTraj traj, bool isSim){
        TRAJ_PLAN::Traj_Adjust trajAdj;
        ANOM_DETEC::AnomalyDetection anomDet;
        std::mutex mtx_lock;

        Plan_Res_MulDOF motion_r = traj.right;
        Plan_Res_MulDOF motion_l = traj.left;

        int len_max = std::max(motion_r.n,motion_l.n);
        motion_r = trajAdj.traj_extend(motion_r,len_max,motion_r.t(1)-motion_r.t(0));
        motion_l = trajAdj.traj_extend(motion_l,len_max,motion_l.t(1)-motion_l.t(0));

        motor_msgs_.data.resize(28);

        int n = motion_r.n;
        int num = 7;

        int n_hand = motion_r.pos_hand.rows();
        int num_hand = 6;

        ros::Rate rate(1000);

        bool flag = false;
        Plan_Res jnts_r;
        Plan_Res jnts_l;
        while(ros::ok()){
            mtx_lock.lock();
            for(int i = 0; i < n; i++){
                jnts_r.pos = motion_r.pos.row(i);
                jnts_l.pos = motion_l.pos.row(i);
                jnts_r.vel = motion_r.vel.row(i);
                jnts_l.vel = motion_l.vel.row(i);
                if(anomDet.anomalyDetection(jnts_r,jnts_l,is_colli_det_,robotModel_)){
                    ROS_ERROR("Anomaly detected!");
                    ros::shutdown();
                    exit(0);
                }
                if(anomDet.isExceedPosLimit(jnt_pos_actual_r,jnt_pos_actual_l)){
                    ROS_ERROR("Anomaly detected!: Actual position");
                    ros::shutdown();
                    exit(0);
                }
                
                rviz_msgs_.header.stamp = ros::Time::now();
                rviz_msgs_.header.frame_id = "pub2rviz";
                rviz_msgs_.name.resize(26);
                rviz_msgs_.position.resize(26);
                for(int j = 0; j < num; j++){
                    rviz_msgs_.name[j] = "r_arm_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j] = motion_r.pos(i,j);
                    rviz_msgs_.name[j+num+6] = "l_arm_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+num+6] = motion_l.pos(i,j);
                    // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

                    motor_msgs_.data[j] = motion_l.pos(i,j);
                    motor_msgs_.data[j+num] = motion_r.pos(i,j);
                    motor_msgs_.data[j+2*num] = 0;
                    motor_msgs_.data[j+3*num] = 0;
                }
                for(int j = 0; j < num_hand; j++){
                    rviz_msgs_.name[j+7] = "r_hand_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+7] = motion_r.pos_hand(0,j);
                    rviz_msgs_.name[j+num+13] = "l_hand_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+num+13] = motion_l.pos_hand(0,j);
                }
                fileWrite(fwPubPos_,motion_r.pos.row(i),motion_l.pos.row(i));
                fileWrite(fwPubVel_,motion_r.vel.row(i),motion_l.vel.row(i));
                fileWrite(fwPubAcc_,motion_r.acc.row(i),motion_l.acc.row(i));
                
                pub_rviz_.publish(rviz_msgs_);
                if(!isSim){
                    pub_motor_.publish(motor_msgs_);
                }
                rate.sleep();
            }

            for(int i = 0; i < n_hand; i++){
                rviz_msgs_.header.stamp = ros::Time::now();
                rviz_msgs_.header.frame_id = "pub2rviz";
                rviz_msgs_.name.resize(26);
                rviz_msgs_.position.resize(26);
                for(int j = 0; j < num; j++){
                    rviz_msgs_.name[j] = "r_arm_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j] = motion_r.pos(n-1,j);
                    rviz_msgs_.name[j+num+6] = "l_arm_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+num+6] = motion_l.pos(n-1,j);
                }
                for(int j = 0; j < num_hand; j++){
                    rviz_msgs_.name[j+7] = "r_hand_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+7] = motion_r.pos_hand(i,j);
                    rviz_msgs_.name[j+num+13] = "l_hand_Joint" + std::to_string(j+1);
                    rviz_msgs_.position[j+num+13] = motion_l.pos_hand(i,j);
                }
                pub_rviz_.publish(rviz_msgs_);
                rate.sleep();
            }
            
            mtx_lock.unlock();
            break;
        }
    }
}