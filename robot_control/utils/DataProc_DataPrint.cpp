/*
数据打印
*/

#include "arms_gen2_control/Def_Class.h"

namespace DATA_PROC{
    void printJointAngles(const std::string &arm_side, const Eigen::VectorXd &jnt_pos) {
        // 打印表头
        ROS_INFO("%s Joint Angles:", arm_side.c_str());
        
        // 使用格式化输出确保对齐
        std::stringstream angle_list;
        for (int i = 0; i < jnt_pos.size(); ++i) {
            // 保证每个数字的宽度相同，设置固定宽度15个字符，4位小数
            angle_list << std::setw(8) << std::fixed << std::setprecision(4) 
                    << jnt_pos(i) / M_PI * 180;
            if (i < jnt_pos.size() - 1) {
                angle_list << ", ";  // 每个角度之间用逗号隔开
            }
        }

        ROS_INFO("%s", angle_list.str().c_str());
    }
}