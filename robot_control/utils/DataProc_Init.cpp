/*
数据发布和订阅类的构造函数和析构函数
*/

#include "arms_gen2_control/Def_Class.h"

namespace DATA_PROC{
    Eigen::VectorXd jnt_pos_actual_r = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd jnt_pos_actual_l = Eigen::VectorXd::Zero(7);
    /**
     * @brief 数据发布类的构造函数
     * @details 包含臂手数据发布和数据保存相关的初始化设置
     */
    // Data_Pub::Data_Pub(bool flag_colli_det, int istest){
    //     if (!istest)
    //     {
    //         nh_ = ros::NodeHandle();

    //         pub_motor_ = nh_.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    //         pub_rviz_ = nh_.advertise<sensor_msgs::JointState>("joint_states",1000);

    //         hand_id1_ = 1;
    //         hand_id2_ = 2;
    //         port_name_ = "/dev/ttyUSB0";
    //         baudrate_ = 115200;
    //         test_flags_ = 1;
    //         Serial_flags_ = 1;

    //         act_position_ = -1;
    //         hand_state_ = 0xff;

    //         //Initialize and open serial port
    //         com_port_ = new serial::Serial(port_name_, (uint32_t)baudrate_, serial::Timeout::simpleTimeout(100));
    //         checkPortOpen(com_port_);

    //         default_pos_ = {999,999,999,999,999,999};

    //         setFORCE(400,400,400,400,400,400,1);
    //         setFORCE(400,400,400,400,400,400,2);
    //         setSPEED(400,400,400,400,400,400,1);
    //         setSPEED(400,400,400,400,400,400,2);        

    //         auto now = std::chrono::system_clock::now();
    //         std::time_t now_c = std::chrono::system_clock::to_time_t(now);
    //         struct tm now_tm = *std::localtime(&now_c);
    //         std::string fnPubPos = "./src/arms_gen2_control/data/record/txt/pub_pos/pub_pos_"
    //                             + std::to_string(now_tm.tm_year + 1900) + "-"
    //                             + std::to_string(now_tm.tm_mon + 1) + "-"
    //                             + std::to_string(now_tm.tm_mday) + "-"
    //                             + std::to_string(now_tm.tm_hour) + "-"
    //                             + std::to_string(now_tm.tm_min) + "-"
    //                             + std::to_string(now_tm.tm_sec)
    //                             + ".txt";
    //         std::string fnPubVel = "./src/arms_gen2_control/data/record/txt/pub_vel/pub_vel_"
    //                             + std::to_string(now_tm.tm_year + 1900) + "-"
    //                             + std::to_string(now_tm.tm_mon + 1) + "-"
    //                             + std::to_string(now_tm.tm_mday) + "-"
    //                             + std::to_string(now_tm.tm_hour) + "-"
    //                             + std::to_string(now_tm.tm_min) + "-"
    //                             + std::to_string(now_tm.tm_sec)
    //                             + ".txt";
    //         std::string fnPubAcc = "./src/arms_gen2_control/data/record/txt/pub_acc/pub_acc_"
    //                             + std::to_string(now_tm.tm_year + 1900) + "-"
    //                             + std::to_string(now_tm.tm_mon + 1) + "-"
    //                             + std::to_string(now_tm.tm_mday) + "-"
    //                             + std::to_string(now_tm.tm_hour) + "-"
    //                             + std::to_string(now_tm.tm_min) + "-"
    //                             + std::to_string(now_tm.tm_sec)
    //                             + ".txt";
    //         fwPubPos_=fopen(fnPubPos.c_str(),"w");
    //         fwPubVel_=fopen(fnPubVel.c_str(),"w");
    //         fwPubAcc_=fopen(fnPubAcc.c_str(),"w");

    //         is_colli_det_ = flag_colli_det;
    //         if(is_colli_det_){
    //             ANOM_DETEC::AnomalyDetection anomDetec;

    //             auto start = std::chrono::steady_clock::now();
    //             robotModel_ = anomDetec.loadRobotModel_();
    //             auto end = std::chrono::steady_clock::now();
    //             auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end-start).count();
    //             ROS_INFO("The model is successfully resolved[%.5fs]\n", (float)duration/1000000);
    //         }
    //     }
    
    // }

 Data_Pub::Data_Pub(bool flag_colli_det){

        nh_ = ros::NodeHandle();

        pub_motor_ = nh_.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
        pub_rviz_ = nh_.advertise<sensor_msgs::JointState>("joint_states",1000);

        hand_id1_ = 1;
        hand_id2_ = 2;
        port_name_ = "/dev/ttyUSB0";
        baudrate_ = 115200;
        test_flags_ = 1;
        Serial_flags_ = 1;

        act_position_ = -1;
        hand_state_ = 0xff;

        //Initialize and open serial port
        com_port_ = new serial::Serial(port_name_, (uint32_t)baudrate_, serial::Timeout::simpleTimeout(100));
        checkPortOpen(com_port_);

        default_pos_ = {999,999,999,999,999,999};

        setFORCE(400,400,400,400,400,400,1);
        // setFORCE(400,400,400,400,400,400,2);
        setSPEED(400,400,400,400,400,400,1);
        // setSPEED(400,400,400,400,400,400,2);        

        auto now = std::chrono::system_clock::now();
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        struct tm now_tm = *std::localtime(&now_c);
        std::string fnPubPos = "./src/arms_gen2_control/data/record/txt/pub_pos/pub_pos_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        std::string fnPubVel = "./src/arms_gen2_control/data/record/txt/pub_vel/pub_vel_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        std::string fnPubAcc = "./src/arms_gen2_control/data/record/txt/pub_acc/pub_acc_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        fwPubPos_=fopen(fnPubPos.c_str(),"w");
        fwPubVel_=fopen(fnPubVel.c_str(),"w");
        fwPubAcc_=fopen(fnPubAcc.c_str(),"w");

        is_colli_det_ = flag_colli_det;
        if(is_colli_det_){
            ANOM_DETEC::AnomalyDetection anomDetec;

            auto start = std::chrono::steady_clock::now();
            robotModel_ = anomDetec.loadRobotModel_();
            auto end = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end-start).count();
            ROS_INFO("The model is successfully resolved[%.5fs]\n", (float)duration/1000000);
        }
        
    
    }

    /**
     * @brief 数据发布类的析构函数
     * @details 关闭文件指针，关闭串口
     */
    Data_Pub::~Data_Pub(){
        fclose(fwPubPos_);
        fclose(fwPubVel_);
        fclose(fwPubAcc_);

        com_port_->close(); //Close port
        delete com_port_;   //delete object
    }

    /**
     * @brief 数据订阅类的构造函数
     * @details 话题订阅包括对电机位置和目标点位置的订阅，并对电机位置进行数据保存。
     *          其中，话题订阅使用了回调队列，异步接受订阅数据，接收电机位置的回调从初始化已经开始，目标位置的回调需要getCVPos函数控制开始
     */
    Data_Sub::Data_Sub(){
        nh1_ = ros::NodeHandle();
        nh2_ = ros::NodeHandle();

        queue1_ = new ros::CallbackQueue();
        queue2_ = new ros::CallbackQueue();

        nh1_.setCallbackQueue(queue1_);
        nh2_.setCallbackQueue(queue2_);

        subActPos_ = nh1_.subscribe("motor_state/arm_motor_state_actual", 1000, &Data_Sub::jointStateCallback, this);
        subCVPos_ = nh2_.subscribe("/gripper_det_box", 1, &Data_Sub::camera_arrayCallback_base, this);

        auto now = std::chrono::system_clock::now();
        std::time_t now_c = std::chrono::system_clock::to_time_t(now);
        struct tm now_tm = *std::localtime(&now_c);
        std::string fnSubPos = "./src/arms_gen2_control/data/record/txt/sub_pos/sub_pos_"
                            + std::to_string(now_tm.tm_year + 1900) + "-"
                            + std::to_string(now_tm.tm_mon + 1) + "-"
                            + std::to_string(now_tm.tm_mday) + "-"
                            + std::to_string(now_tm.tm_hour) + "-"
                            + std::to_string(now_tm.tm_min) + "-"
                            + std::to_string(now_tm.tm_sec)
                            + ".txt";
        fwSubPos_=fopen(fnSubPos.c_str(),"w");

        spinner1_ = new ros::AsyncSpinner(1, queue1_);
        spinner2_ = new ros::AsyncSpinner(3, queue2_);

        ActPos_flag = false;
        subCV_flag = false;
        
        spinner1_->start();
    }

    /**
     * @brief 数据订阅类的析构函数
     * @details 关闭回调队列，关闭文件指针
     */
    Data_Sub::~Data_Sub(){
        spinner1_->stop();
        spinner2_->stop();
        fclose(fwSubPos_);
    }
}