/*
基于hpp-fcl库的子碰撞检测相关函数
    - 加载STL文件
    - 加载机器人模型
    - 姿态变换
*/

#include "arms_gen2_control/Def_Class.h"


namespace ANOM_DETEC{
    KINEMATICS::Pose_Trans poseTransHppFcl;
    /**
     * @brief 加载凸网格模型
     * @details 加载指定模型文件，并返回一个生成的凸包（ConvexBase）
     */
    std::shared_ptr<coal::ConvexBase> AnomalyDetection::loadConvexMesh_(const std::string& file_name){
        // 定义了边界体（Bounding Volume）的类型为轴对齐包围盒（Axis-Aligned Bounding Box）
        coal::NODE_TYPE bv_type = coal::BV_AABB;
        // 创建 MeshLoader 对象
        coal::MeshLoader loader(bv_type);
        // 加载文件
        coal::BVHModelPtr_t bvh = loader.load(file_name);
        if (!bvh) {
            std::cerr << "Error: Failed to load file " << file_name << std::endl;
            return nullptr;
        }
        // 生成凸包
        bvh->buildConvexHull(true, "Qt");
        // 返回凸包
        return bvh->convex;
    }

    /**
     * @brief 加载机器人的模型
     * @details 异步加载每个连杆的STL文件，返回凸包的动态数组
     */
    DynamicArray<std::shared_ptr<coal::ConvexBase>> AnomalyDetection::loadRobotModel_() {
        // 定义文件路径数组
        const std::vector<std::string> parts = {
            "base_link.STL",
            "r_arm_Link1.STL", "r_arm_Link2.STL", "r_arm_Link3.STL", "r_arm_Link4.STL", "r_arm_Link5.STL", "r_arm_Link6.STL", "r_arm_Link7.STL",
            "l_arm_Link1.STL", "l_arm_Link2.STL", "l_arm_Link3.STL", "l_arm_Link4.STL", "l_arm_Link5.STL", "l_arm_Link6.STL", "l_arm_Link7.STL"
        };
        // 动态数组存储模型
        DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel;
        robotModel.reserve(parts.size()); // 预分配内存
        // 用于存储异步任务的 future 对象
        std::vector<std::future<std::shared_ptr<coal::ConvexBase>>> futures;
        futures.reserve(parts.size());
        // 启动异步任务加载每个部件的凸包模型
        for (const std::string& part : parts) {
            std::string filePath = "src/arms_gen2_control/model/meshes/" + part;
            futures.push_back(std::async(std::launch::async, [this, filePath]() {
                return loadConvexMesh_(filePath);
            }));
        }
        // 等待所有异步任务完成并收集结果
        for (auto& future : futures) {
            try {
                auto convexMesh = future.get(); // 获取异步任务结果
                if (!convexMesh) {
                    std::cerr << "Error: Failed to load convex mesh for file." << std::endl;
                    return {}; // 返回空数组表示加载失败
                }
                robotModel.push_back(convexMesh);
            } catch (const std::exception& e) {
                std::cerr << "Exception occurred while loading mesh: " << e.what() << std::endl;
                return {}; // 返回空数组表示加载失败
            }
        }
        return robotModel;
    }

    /**
     * @brief 设置geometry_msgs姿态
     * @details 包含位置和四元数信息，用于设置连杆在rviz中的位姿
     */
    geometry_msgs::Pose AnomalyDetection::setPose_mesh_(Eigen::VectorXd q, Eigen::Vector3d position){
        geometry_msgs::Pose mesh_pose;
        mesh_pose.position.x = position(0);  // x位置
        mesh_pose.position.y = position(1);  // y位置
        mesh_pose.position.z = position(2);  // z位置
        mesh_pose.orientation.x = q(0);
        mesh_pose.orientation.y = q(1);
        mesh_pose.orientation.z = q(2);
        mesh_pose.orientation.w = q(3);

        return mesh_pose;
    }

    /**
     * @brief 设置coal::Transform3s姿态
     * @details 设置连杆变换矩阵，用于hpp-fcl库的自碰撞计算
     */
    coal::Transform3s AnomalyDetection::setPose_fcl_(Eigen::VectorXd q, Eigen::Vector3d position){
        Eigen::Quaterniond Q;
        Q.x() = q(0);
        Q.y() = q(1);
        Q.z() = q(2);
        Q.w() = q(3);
        Eigen::Matrix3d R = Q.toRotationMatrix();

        coal::Transform3s T;
        T.setRotation(R);
        coal::Vec3s t(position(0), position(1), position(2));
        T.setTranslation(t);

        return T;
    }

    /**
     * @brief 获取臂各个连杆的位姿
     * @details 根据关节角度计算各个连杆的当前位姿和变换矩阵
     */
    DynamicArray<mesh_pose> AnomalyDetection::get_arm_pose_(std::vector<Eigen::Vector3d> relative_eular, std::vector<Eigen::Vector3d> relative_position, Eigen::VectorXd theta){
        Eigen::Matrix3d T = poseTransHppFcl.rot_x(M_PI/2);
        Eigen::Vector3d pos = Eigen::Vector3d(0, 0, 0.8);
        
        DynamicArray<mesh_pose> arm_pose;
        mesh_pose pose_inter;
        Eigen::Vector3d rel_eular;
        Eigen::VectorXd Qtd(4);

        for(int i=0; i<7; i++){
            pos = pos + T * relative_position[i];

            rel_eular = relative_eular[i];
            T = T*poseTransHppFcl.rot_z(rel_eular(2))*poseTransHppFcl.rot_y(rel_eular(1))*poseTransHppFcl.rot_x(rel_eular(0))*poseTransHppFcl.rot_z(theta(i));
            Eigen::Quaterniond Q(T);
            Q.normalize();
            Qtd << Q.coeffs();

            pose_inter.pose = setPose_mesh_(Qtd,pos);
            pose_inter.T_fcl = setPose_fcl_(Qtd,pos);

            arm_pose.push_back(pose_inter);
        }
        return arm_pose;
    }
}