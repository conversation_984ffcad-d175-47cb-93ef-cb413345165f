#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include <std_msgs/Int32.h>


void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        jnt_position_r[i] = msg->position[i+7];
    }

    for(int i = 0; i < 7; i++){
        // if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_l", jnt_position_l);
        //     ros::shutdown();
        //     exit(0);
        // }
        // if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_r", jnt_position_r);
        //     ros::shutdown();
        //     exit(0);
        // }
        if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
    }

    return;
}

int main(int argc, char *argv[])
{
    
    ros::init(argc, argv, "grasp_assembly");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    // ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);
    // ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //键盘输入
    std::string input;

    CAMERA_READ::camera_read camera;
    std_msgs::Float64MultiArray camera_data;

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    array<double, 7> q0, q1, q2, q3, q4, q5;
    def_msgs::Pose pose_l, pose_r;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    Vector3d vec(3), rpy(3);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;
    
    DATA_PROC::Data_Pub dataPub(false);
    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }
    if(istest == 1){
        std::vector<double> data = {0.3, 0.15, -0.256, 1, 0.3, -0.15, -0.256, 0};  // 你想要的浮动数据
        camera_data.data = data;
    }else{        
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 

    //手张开
    // handCtrl.leftHand({999, 999, 999, 999, 999, 10});
    dataPub.rightHand({999, 999, 999, 999, 999, 10});
    // return 0;
    //运动到桌子上方
    // 0.26605 -0.259195 -0.97216 0.860659 0.488558 0.707906 -0.425653

    pose0_l = arr_give(0.265187, -0.266337, -0.973071, 0.878971, 0.487996, 0.697198, -0.431468);
    pose0_r = arr_give(1.21798, -0.430953, -1.07695, 1.34017, 1.17094, -0.239554, 0.790383);

    qp_grasp.clear();
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0_l);

    t_action.assign({1, 2});
    // t_action.assign({1, 2, 1, 2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear(); 
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0_r);
    qp_grasp.push_back(pose0_r);

    t_action.assign({2, 1});
    // t_action.assign({5, 5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");
   
    dataPub.leftHand({999, 999, 999, 999, 999, 10});

    // return 0;

    pose1_l = arr_give(0.127896, -0.75189, -0.954855, 1.72271, 0.560555, 0.373523, -0.648557);
    qp_grasp.clear();
    qp_grasp.push_back(pose0_l);
    qp_grasp.push_back(pose1_l);

    t_action.assign({1.5});
    // t_action.assign({1, 2, 1, 2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear(); 
    qp_grasp.push_back(pose0_r);
    qp_grasp.push_back(pose0_r);

    // t_action.assign({5, 5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    // return 0;

    ROS_INFO("Launching terminal...");
    
    // 要执行的命令
    std::string cmd_str = "gnome-terminal -- bash -c 'cd ~/robot_arm_v2/; source ./devel/setup.bash; rosrun arms_gen2_control Action001_return2init; exec bash'";
    
    // 执行系统命令
    int result = system(cmd_str.c_str());
    
    if (result == 0) {
        ROS_INFO("Terminal launched successfully.");
    } else {
        ROS_ERROR("Failed to launch terminal. Error code: %d", result);
    }

    return 0;
}


//放箱子上
// #include "robot_control/robot_control.h"
// #include "arms_gen2_control/Def_Class.h"
// #include <std_msgs/Int32.h>


// void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

//     for(int i = 0; i < 7; i++)
//     {
//         jnt_effort_l[i] = msg->effort[i];
//         jnt_effort_r[i] = msg->effort[i+7];
//         jnt_position_l[i] = msg->position[i];
//         jnt_position_r[i] = msg->position[i+7];
//     }

//     for(int i = 0; i < 7; i++){
//         // if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
//         //     ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
//         //     array_cout("jnt_position_l", jnt_position_l);
//         //     ros::shutdown();
//         //     exit(0);
//         // }
//         // if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
//         //     ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
//         //     array_cout("jnt_position_r", jnt_position_r);
//         //     ros::shutdown();
//         //     exit(0);
//         // }
//         if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
//             ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
//             array_cout("jnt_effort_l", jnt_effort);
//             ros::shutdown();
//             exit(0);
//         }
//         if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
//             ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
//             array_cout("jnt_effort_l", jnt_effort);
//             ros::shutdown();
//             exit(0);
//         }
//     }

//     return;
// }

// int main(int argc, char *argv[])
// {
    
//     ros::init(argc, argv, "grasp_assembly");
//     setlocale(LC_ALL,"");
//     const char* urdf_path = getenv("URDF_PATH");
//     ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

//     ros::NodeHandle nh;
//     ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
//     // ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);
//     // ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);

//     theta_limit = robot.theta_limit;
//     effort_limit = robot.effort_limit;

//     //启用多线程
//  	ros::AsyncSpinner spinner(3);
//  	spinner.start(); //开始标志

//     //键盘输入
//     std::string input;

//     CAMERA_READ::camera_read camera;
//     std_msgs::Float64MultiArray camera_data;

//     /*
//     set init position
//     */ 
//     //左臂
//     array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
//     //右臂
//     array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
//     array<double, 7> q0, q1, q2, q3, q4, q5;
//     def_msgs::Pose pose_l, pose_r;
//     array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
//     array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
//     array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
//     VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
//     Vector3d vec(3), rpy(3);
//     // 轨迹点集合
//     Plan_Res_Dual action;
//     vector<double> t_action;
//     vector<array<double, 7>> qp_grasp;

//     //手控制
//     INSPIRE_HAND::Pos_Ctrl handCtrl;
    
//     DATA_PROC::Data_Pub dataPub(false);
//     // dataPub.rightHand();
//     // dataPub.rightHand({400,400,400,400,600,999});
//     // dataPub.rightHand();
//     // dataPub.doubleHand();
//     // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
//     // dataPub.doubleHand();

//     /*
//     获取初始位置
//     */
//     istest = 0;  
//     ctrl_mode = robot_ctrl;

//     // 检查是否有输入参数
//     if(argc > 1) {
//         // 将第一个参数转换为整数
//         int a = atoi(argv[1]);
//         if(a == 1){
//             istest = 0;
//             ctrl_mode = rviz_ctrl;
//             ROS_INFO("运行在仿真模式");
//         } else if(a == 2){
//             istest = 1;
//             ctrl_mode = rviz_ctrl;
//             ROS_INFO("运行在测试模式");
//         }
//     }
//     if(istest == 1){
//         std::vector<double> data = {0.3, 0.15, -0.256, 1, 0.3, -0.15, -0.256, 0};  // 你想要的浮动数据
//         camera_data.data = data;
//     }else{        
//         sensor_msgs::JointState JointState;
//         robot.getCurrentState(JointState);
//         Getpos(JointState, l_jnt_init, r_jnt_init);
//     } 

//     //手张开
//     // handCtrl.leftHand({999, 999, 999, 999, 999, 10});
//     dataPub.rightHand({999, 999, 999, 999, 999, 10});
//     // return 0;
//     //运动到桌子上方
//     // 0.26605 -0.259195 -0.97216 0.860659 0.488558 0.707906 -0.425653

//     // pose0_l = arr_give(0.265187, -0.266337, -0.973071, 0.878971, 0.487996, 0.697198, -0.431468);
//     pose1_l = arr_give(1.50364, -0.513404, -1.2954, 0.0674952, -0.351592, 0.7, -0.114506);
//     pose0_l = arr_give(1.13558, -1.09852, -1.17934, 1.92486, -0.280983, -0.0, 0.0924537);

//     pose0_r = arr_give(1.21798, -0.430953, -1.07695, 1.34017, 1.17094, -0.239554, 0.790383);

//     qp_grasp.clear();
//     qp_grasp.push_back(l_jnt_init);
//     qp_grasp.push_back(l_jnt_init);
//     qp_grasp.push_back(pose0_l);
//     qp_grasp.push_back(pose1_l);

//     t_action.assign({1, 1.5, 1.5});
//     // t_action.assign({1, 2, 1, 2});
//     action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

//     qp_grasp.clear(); 
//     qp_grasp.push_back(r_jnt_init);
//     qp_grasp.push_back(pose0_r);
//     qp_grasp.push_back(pose0_r);

//     t_action.assign({2, 2});
//     // t_action.assign({5, 5});
//     action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
//     ROS_INFO("运动到目标位置.....");
//     robot.move(action, ctrl_mode);
//     ROS_INFO("运动完成!");
   
//     // return 0;

//     dataPub.leftHand({999, 999, 999, 999, 999, 10});


//     // return 0;

//     // pose1_l = arr_give(0.127896, -0.75189, -0.954855, 1.72271, 0.560555, 0.373523, -0.648557);
//     // qp_grasp.clear();
//     // qp_grasp.push_back(pose0_l);
//     // qp_grasp.push_back(pose1_l);

//     // t_action.assign({1.5});
//     // // t_action.assign({1, 2, 1, 2});
//     // action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

//     // qp_grasp.clear(); 
//     // qp_grasp.push_back(pose0_r);
//     // qp_grasp.push_back(pose0_r);

//     // // t_action.assign({5, 5});
//     // action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
//     // ROS_INFO("运动到目标位置.....");
//     // robot.move(action, ctrl_mode);
//     // ROS_INFO("运动完成!");

//     // return 0;

//     ROS_INFO("Launching terminal...");
    
//     // 要执行的命令
//     std::string cmd_str = "gnome-terminal -- bash -c 'cd ~/robot_arm_v2/; source ./devel/setup.bash; rosrun arms_gen2_control Action001_return2init; exec bash'";
    
//     // 执行系统命令
//     int result = system(cmd_str.c_str());
    
//     if (result == 0) {
//         ROS_INFO("Terminal launched successfully.");
//     } else {
//         ROS_ERROR("Failed to launch terminal. Error code: %d", result);
//     }

//     return 0;
// }



