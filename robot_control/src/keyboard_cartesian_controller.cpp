#include "robot_control/robot_control.h"


void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        jnt_position_r[i] = msg->position[i+7];
    }

    for(int i = 0; i < 7; i++){
        if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
            array_cout("jnt_position_l", jnt_position_l);
            ros::shutdown();
            exit(0);
        }
        if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
            array_cout("jnt_position_r", jnt_position_r);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
    }

    return;
}

// 解析输入命令
bool parseCommand(const std::string& command, char& arm, std::vector<double>& increments, char& second_arm, std::vector<double>& second_increments) {
    std::istringstream ss(command);
    ss >> arm;

    // 单独控制左臂或右臂
    if (arm == 'l' || arm == 'r') {
        double value;
        while (ss >> value) {
            increments.push_back(value);
        }
        return true;
    }

    // 同时控制左右臂
    if (arm == 'l' || arm == 'r') {
        double value;
        while (ss >> value) {
            increments.push_back(value);
            if (ss.peek() == ' ') ss.ignore();
        }

        // 第二臂控制
        ss >> second_arm;
        if (second_arm != 'l' && second_arm != 'r') {
            std::cerr << "Invalid second arm identifier. Use 'l' or 'r'." << std::endl;
            return false;
        }

        while (ss >> value) {
            second_increments.push_back(value);
        }
        return true;
    }

    std::cerr << "Invalid arm identifier. Use 'l' or 'r'." << std::endl;
    return false;
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "keyboard_cartesian_controller");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //set init position
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473};

    def_msgs::Pose pose_l, pose_r;
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;
    Vector3d vec_l, vec_r;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 3;
    double t2 = 5;
    double t3 = 7;
    istest = 0;
    ctrl_mode = robot_ctrl;

    std::string input;
    std::cout << "Enter commands to control the arms:" << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  l 0.1  (Move left arm along x by 0.1)" << std::endl;
    std::cout << "  r 0.1 0.2 0.1  (Move right arm along x, y, z by 0.1, 0.2, 0.1)" << std::endl;
    std::cout << "  l 0.1 r 0.2  (Move left arm along x by 0.1, right arm along x by 0.2)" << std::endl;
    while (ros::ok()) {
        std::cout << "Wait input: " << std::endl;
        std::getline(std::cin, input);

        if (input == "q") break;
       
        // 解析输入命令
        char arm;
        char second_arm;
        std::vector<double> increments;
        std::vector<double> second_increments;

        if (!parseCommand(input, arm, increments, second_arm, second_increments)) {
            continue;  // 无效命令，重新输入
        }
        vec_l << 0, 0, 0;
        vec_r << 0, 0, 0;
        // 更新左右臂的位置
        if (arm == 'l') {
            if (increments.size() > 0) vec_l[0] += increments[0];
            if (increments.size() > 1) vec_l[1] += increments[1];
            if (increments.size() > 2) vec_l[2] += increments[2];
        } else if (arm == 'r') {
            if (increments.size() > 0) vec_r[0] += increments[0];
            if (increments.size() > 1) vec_r[1] += increments[1];
            if (increments.size() > 2) vec_r[2] += increments[2];
        }

        // 同时控制
        if (!second_increments.empty()) {
            if (second_arm == 'l') {
                if (increments.size() > 0) vec_l[0] += increments[0];
                if (increments.size() > 1) vec_l[1] += increments[1];
                if (increments.size() > 2) vec_l[2] += increments[2];
            } else if (second_arm == 'r') {
                if (increments.size() > 0) vec_r[0] += increments[0];
                if (increments.size() > 1) vec_r[1] += increments[1];
                if (increments.size() > 2) vec_r[2] += increments[2];
            }
        }

        //获取初始位置
        if(istest == 1){
        }else{
            sensor_msgs::JointState JointState;
            robot.getCurrentState(JointState);
            Getpos(JointState, l_jnt_init, r_jnt_init);
        }  

        // handCtrl.rightHand({999,999,999,999,550,10});

        //运动到抓取位置
        robot.kdl_fk(l_jnt_init, pose_l, left_arm);
        pose_start_l << pose_l.position(0,3),pose_l.position(1,3),pose_l.position(2,3),pose_l.rpy[0],pose_l.rpy[1],pose_l.rpy[2];
        pose_end_l = pose_start_l;
        pose_end_l[0] += vec_l[0];
        pose_end_l[1] += vec_l[1];
        pose_end_l[2] += vec_l[2];
        action.left = robot.line_plan(l_jnt_init, pose_start_l, pose_end_l, 3, left_arm);

        robot.kdl_fk(r_jnt_init, pose_r, right_arm);
        pose_start_r << pose_r.position(0,3),pose_r.position(1,3),pose_r.position(2,3),pose_r.rpy[0],pose_r.rpy[1],pose_r.rpy[2];
        pose_end_r = pose_start_r;
        pose_end_r[0] += vec_r[0];
        pose_end_r[1] += vec_r[1];
        pose_end_r[2] += vec_r[2];
        action.right = robot.line_plan(r_jnt_init, pose_start_r, pose_end_r, 3, right_arm);

        ROS_INFO("运动到抓取位置.....");
        robot.move(action, ctrl_mode);
        ROS_INFO("运动完成!");
    }
    return 0;
}
