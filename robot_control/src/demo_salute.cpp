#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include "std_msgs/Int32.h"

int flag_start = 0;
int flag_start2 = 0;

void jointStateCallback(const std_msgs::Int32::ConstPtr &msg) {
    cout << "ssss" << endl;
    flag_start = msg->data;
    // if (msg->data == 1) {
    //     flag_start = 1;
    //     cout << "start" << endl;
    // }
}

void jointStateCallback2(const std_msgs::Int32::ConstPtr &msg) {
    cout << "ssss2222" << endl;
    flag_start2 = msg->data;
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "demo_salute");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
   
    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("/ankle_detection", 1, jointStateCallback);
    ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);
    ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);
   
    Vector3d vec, rpy;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;


    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    istest = 0;
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    INSPIRE_HAND::Pos_Ctrl handCtrl;
    // DATA_PROC::Data_Pub handCtrl(false);

    qp_grasp.push_back(r_jnt_init);
    while(ros::ok()){
        // cout << "1" <<endl;
        if(flag_start == 1 ){
            /*
            获取初始位置
            */
            if(istest == 1){
            }else{
                sensor_msgs::JointState JointState;
                robot.getCurrentState(JointState);
                Getpos(JointState, l_jnt_init, r_jnt_init);
            }
            //不在初始位置时复位
            if (abs(l_jnt_init[3]) > 0.5 || abs(r_jnt_init[3]) > 0.5)
            {
                pose0 = arr_give(0,-M_PI*8/180,0,0,0,0,0);
                qp_grasp.clear();
                qp_grasp.push_back(r_jnt_init);
                qp_grasp.push_back(pose0);
                
                t_action.assign({5});
                action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

                qp_grasp.clear();
                qp_grasp.push_back(l_jnt_init);
                qp_grasp.push_back(pose0);
           
                t_action.assign({5});
                action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

                ROS_INFO("运动到装配位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
                r_jnt_init = pose0;
                l_jnt_init = pose0;
            }
            

            // array_cout("l_jnt_init", l_jnt_init);
            // array_cout("r_jnt_init", r_jnt_init);
            // return 0;

            // 开始敬礼

            handCtrl.rightHand({999,999,999,999,10,999});

            //左臂保持当前位置
            qp_grasp.clear();
            qp_grasp.push_back(l_jnt_init);
            qp_grasp.push_back(l_jnt_init);
            t_action.assign({100});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

            // vec << 0.35, 0.05, 0.2;
            // rpy << -M_PI_2, M_PI_2, 0; //roll, pitch, yaw
            // robot.kdl_ik(j_init, vec, rpy, pose0, right_arm);
            // vec << 0.36, 0.05, 0.13;
            // robot.kdl_ik(j_init, vec, rpy, pose1, right_arm);
            // r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
            pose0 = arr_give(0.44, -1.04, -1.39, 1.79, 0.36, -0.05, 0.03);
            // pose1 = arr_give(2.19, -1.52, -2.27, 2.11, 1, -0.05, 0.03);
            pose2 = arr_give(2.97, -1.23, -1.61, 2.13, 1.5, -0.05, 0.2);
            pose3 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
            pose4 = arr_give(1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473);

            qp_grasp.clear();
            qp_grasp.push_back(r_jnt_init);
            qp_grasp.push_back(pose0);
            // qp_grasp.push_back(pose1);
            qp_grasp.push_back(pose2);
            // qp_grasp.push_back(pose3);
            // qp_grasp.push_back(pose4);
            t_action.assign({1.2,2});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

            ROS_INFO("运动到装配位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");

            sleep(2);

            while(ros::ok() && flag_start != 2){
                sleep(0.1);
            }
            // 开始端枪

            //右臂复位
            qp_grasp.clear();
            qp_grasp.push_back(l_jnt_init);
            qp_grasp.push_back(l_jnt_init);
            t_action.assign({100});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

            pose0 = arr_give(0,-M_PI*8/180,0,0,0,0,0);
    
            qp_grasp.clear();
            qp_grasp.push_back(pose2);
            qp_grasp.push_back(pose0);
        
            t_action.assign({3});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

            ROS_INFO("运动到初始位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");

            handCtrl.rightHand({999,999,999,999,999,10});

            //双臂抱住枪
            // l_jnt_init = arr_give(0,-M_PI*8/180,0,0,0,0,0);
            r_jnt_init = arr_give(0,-M_PI*8/180,0,0,0,0,0);
            pose0 = arr_give(-0.54 , -0.26, -0.14, 2.02, 1.43, -0.07, 0.56);
            qp_grasp.clear();
            qp_grasp.push_back(r_jnt_init);
            qp_grasp.push_back(pose0);
            t_action.assign({5});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

            // vec << 0.35, 0.05, 0.2;
            // rpy << -M_PI_2, M_PI_2, 0; //roll, pitch, yaw
            // robot.kdl_ik(j_init, vec, rpy, pose0, right_arm);
            // vec << 0.36, 0.05, 0.13;
            // robot.kdl_ik(j_init, vec, rpy, pose1, right_arm);
            // r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
            pose0 = arr_give(0.7, -0.35, -1.8, 1.05, 0.8, 1.23, 0.42);
            pose1 = arr_give(2.19, -1.52, -2.27, 2.11, 1, -0.05, 0.03);
            pose2 = arr_give(2.97, -1.23, -1.61, 2.13, 1.5, -0.05, 0.2);
            pose3 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
            pose4 = arr_give(1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473);

            qp_grasp.clear();
            qp_grasp.push_back(l_jnt_init);
            qp_grasp.push_back(pose0);
            // qp_grasp.push_back(pose0);
            // qp_grasp.push_back(pose1);
            // qp_grasp.push_back(pose2);
            // qp_grasp.push_back(pose3);
            // qp_grasp.push_back(pose4);
            t_action.assign({5});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

            ROS_INFO("运动到装配位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");
            handCtrl.rightHand({799,699,650,620,950,10});
            sleep(10);
            handCtrl.rightHand({999,999,999,999,999,10});

            //双臂复位
            // pose0 = arr_give(-0.42, -0.26, -0.14, 2.01, 1.52, -0.07, 0.52);
            pose0 = arr_give(-0.54 , -0.26, -0.14, 2.02, 1.43, -0.07, 0.56);
            qp_grasp.clear();
            qp_grasp.push_back(pose0);
            qp_grasp.push_back(r_jnt_init);
            // qp_grasp.push_back(r_jnt_init);
            
            t_action.assign({5});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

            // vec << 0.35, 0.05, 0.2;
            // rpy << -M_PI_2, M_PI_2, 0; //roll, pitch, yaw
            // robot.kdl_ik(j_init, vec, rpy, pose0, right_arm);
            // vec << 0.36, 0.05, 0.13;
            // robot.kdl_ik(j_init, vec, rpy, pose1, right_arm);
            // r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
            pose0 = arr_give(0.7, -0.35, -1.8, 1.05, 0.8, 1.23, 0.42);
            pose1 = arr_give(2.19, -1.52, -2.27, 2.11, 1, -0.05, 0.03);
            pose2 = arr_give(2.97, -1.23, -1.61, 2.13, 1.5, -0.05, 0.2);
            pose3 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
            pose4 = arr_give(1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473);

            qp_grasp.clear();
            qp_grasp.push_back(pose0);
            qp_grasp.push_back(l_jnt_init);
            // qp_grasp.push_back(pose1);
            // qp_grasp.push_back(pose2);
            // qp_grasp.push_back(pose3);
            // qp_grasp.push_back(pose4);
            t_action.assign({5});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

            ROS_INFO("运动到装配位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");  

            handCtrl.rightHand({999,999,999,999,999,999}); 

            // flag_start = 0;       
        }
    }
    return 0;
}