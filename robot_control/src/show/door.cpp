#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include <std_msgs/Int32.h>


ros::Publisher car_vir_pos_pub2, car_act_pos_pub2, car_err_pos_pub2;

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        // jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        // jnt_position_r[i] = msg->position[i+7];

        jnt_pos_current_l2[i] = msg->position[i];
        // jnt_pos_current_r2[i] = msg->position[i+7];
    }

    for(int i = 0; i < 7; i++){
        // if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_l", jnt_position_l);
        //     ros::shutdown();
        //     exit(0);
        // }
        // if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_r", jnt_position_r);
        //     ros::shutdown();
        //     exit(0);
        // }
        if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
    }

    return;
}

void jointCallback(const sensor_msgs::JointState::ConstPtr &msg, ROBOT_CONTROL::robot_control* robot) {

    for(int i = 0; i < 7; i++)
    {
        // jnt_pos_vir_r2[i] = msg->position[i];
        jnt_pos_vir_l2[i] = msg->position[i+4];
    }
    robot->kdl_fk(jnt_pos_vir_l2, car_pos_vir_l2, left_arm);
    // robot->kdl_fk(jnt_pos_vir_r2, car_pos_vir_r2, right_arm);


    std_msgs::Float64MultiArray car_pos_msg;
    car_pos_msg.data.resize(12);
    for (size_t i = 0; i < 3; i++)
    {
        car_pos_msg.data[i] = car_pos_vir_l2.position[i];
        car_pos_msg.data[i+3] = car_pos_vir_l2.rpy[i];
        car_pos_msg.data[i+6] = car_pos_vir_r2.position[i];
        car_pos_msg.data[i+9] = car_pos_vir_r2.rpy[i];
    }

    car_vir_pos_pub2.publish(car_pos_msg);

    robot->kdl_fk(jnt_pos_current_l2, car_pos_current_l2, left_arm);
    // robot->kdl_fk(jnt_pos_current_r2, car_pos_current_r2, right_arm);

    std_msgs::Float64MultiArray car_pos_act_msg;
    car_pos_act_msg.data.resize(12);

    for (size_t i = 0; i < 3; i++)
    {
        car_pos_act_msg.data[i] = car_pos_current_l2.position[i];
        car_pos_act_msg.data[i+3] = car_pos_current_l2.rpy[i];
        car_pos_act_msg.data[i+6] = car_pos_current_r2.position[i];
        car_pos_act_msg.data[i+9] = car_pos_current_r2.rpy[i];
    }
    car_act_pos_pub2.publish(car_pos_act_msg);

    std_msgs::Float64MultiArray car_pos_err_msg;
    car_pos_err_msg.data.resize(12);
    for (size_t i = 0; i < 12; i++)
    {
        car_pos_err_msg.data[i] = (car_pos_msg.data[i] - car_pos_act_msg.data[i])*1000;
    }
    car_err_pos_pub2.publish(car_pos_err_msg);
    // cout << "car_pos_act_msg: " << car_pos_err_msg.data[7] << endl;

    return;
}

void CurrentStateCallback(const std_msgs::Float64MultiArray::ConstPtr& msg) {
    if (msg->data.size() == 4 && msg->data[3] == 0 && msg->data[0] > 0.4){
        flag_cam = 1;
    }

    return;
}

void doorStateCallback(const std_msgs::Float64MultiArray::ConstPtr& msg) {
    flag_door = 1;
    return;
}

int main(int argc, char *argv[])
{
    
    ros::init(argc, argv, "door");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
    ROBOT_CONTROL::robot_control robot2(urdf_path,"base_link","AL7","AR7");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber joint_sub = nh.subscribe<sensor_msgs::JointState>("joint_states",10, boost::bind(jointCallback, _1, &robot));
    ros::Subscriber sub2 = nh.subscribe("/gripper_det_box", 1, CurrentStateCallback);

    ros::Subscriber sub3 = nh.subscribe("/flag_door", 1, doorStateCallback);

    car_vir_pos_pub2 = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/vir",1);
    car_act_pos_pub2 = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/act",1);
    car_err_pos_pub2 = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/error",1);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;


    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    

    //键盘输入
    std::string input;

    CAMERA_READ::camera_read camera;
    std_msgs::Float64MultiArray camera_data, door_data;
    Vector4d vec_camera;
    Vector3d vec_door;

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    // array<double, 7> jnt_init = {1.41,-0.64,-1.48,0.64,1.45,0.04,0};
    array<double, 7> jnt_init = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -0*M_PI/180, 10*M_PI/180, 0*M_PI/180);
    array<double, 7> q0, q1, q2, q3, q4, q5;
    def_msgs::Pose pose_l, pose_r;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    Vector3d vec(3), rpy(3);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;
    INSPIRE_HAND::Pos_Ctrl dataPub;
    // DATA_PROC::Data_Pub dataPub(false);

    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    //读取当前位置
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init, 11);
    }

    // //运动到识别位置
    // pose1_l = arr_give(-10*M_PI/180, -20*M_PI/180, 0*M_PI/180, 50*M_PI/180, -30*M_PI/180, 5*M_PI/180, 0*M_PI/180);
    // pose0_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180, 0*M_PI/180);
    // // pose3_l = arr_give(-0.5799541257628321, -0.7744448812675493, 0.35345827270728186, 1.8989673113128778, -2.5638147779492237e-05, 1.2912156434552411e-05, -6.939520124725945e-06);
    // qp_grasp.clear();
    // qp_grasp.push_back(l_jnt_init);
    // qp_grasp.push_back(pose1_l);
    // qp_grasp.push_back(pose0_l);
    // t_action.assign({3,3});
    // action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    // qp_grasp.clear(); 
    // qp_grasp.push_back(r_jnt_init);
    // qp_grasp.push_back(r_jnt_init);
    // t_action.assign({100});
    // action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    // ROS_INFO("运动到识别位置.....");
    // robot.move_ele(action, ctrl_mode);
    // ROS_INFO("运动完成!");
    //运动到识别位置
    pose1_l = arr_give(-10*M_PI/180, -20*M_PI/180, 0*M_PI/180, 50*M_PI/180, -30*M_PI/180, 5*M_PI/180, 0*M_PI/180);
    pose0_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180, 0*M_PI/180);
    double delea = 5*M_PI/180;
    pose2_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180 + delea, 0*M_PI/180);
    pose3_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180, 0*M_PI/180 + delea);
    pose4_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180 - delea, 0*M_PI/180);
    pose5_l = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -90*M_PI/180, 10*M_PI/180, 0*M_PI/180 - delea);
    // pose3_l = arr_give(-0.5799541257628321, -0.7744448812675493, 0.35345827270728186, 1.8989673113128778, -2.5638147779492237e-05, 1.2912156434552411e-05, -6.939520124725945e-06);
    qp_grasp.clear();
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose1_l);
    qp_grasp.push_back(pose0_l);
    qp_grasp.push_back(pose2_l);
    qp_grasp.push_back(pose3_l);
    qp_grasp.push_back(pose4_l);
    qp_grasp.push_back(pose5_l);
    t_action.assign({3,3,2,2,2,2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear(); 
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({100});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到识别位置.....");
    robot.move_ele(action, ctrl_mode, 2);
    ROS_INFO("运动完成!");

    std::cout << "keyboard input to continue " << std::endl;
    std::getline(std::cin, input);
    if (input == "q") return 0;

    //开始识别
    if(istest == 1){
        std::vector<double> data = {0.397442101287527, -0.09522129132013918, 0.037664834289618455 + 0.15, 0.0};  
        camera_data.data = data;
    }else{
        while(ros::ok())
        {
            bool condition = camera.getCurrentState(camera_data);
            if (!condition)
            {
            }else if (camera_data.data.size() == 4 && camera_data.data[3] == 0 && camera_data.data[0] > 0.4)
            {
                cout << "camera_data: " <<  camera_data <<endl;
                break;
            }
        }
    } 
    pose0_l = jnt_pos_vir_l2;
    robot2.kdl_fk(pose0_l, pose_l, left_arm);
    Vector4d pe;
    pe << camera_data.data[0] , camera_data.data[1] , camera_data.data[2], 1;
    vec_camera = pose_l.T * pe;
    cout << "vec_camera: " <<  vec_camera <<endl;
    // return 0;

    //运动到识别角度位置
    vec << vec_camera[0] - 0.3, vec_camera[1] + 0.1, vec_camera[2] + 0.1; //0.11 0.15
    rpy << M_PI, 0, 0;
    array<double, 7> joint_f2;
    array<double, 7> joint_init = {0.861636, -0.379348, -0.350379, 1.19425, 0.336415, 0.00524743, -0.504232};
    
    int error_flag = robot.kdl_ik(joint_init, vec, rpy, joint_f2, left_arm);
    if (error_flag == -1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }

    qp_grasp.clear();
    qp_grasp.push_back(pose0_l);
    qp_grasp.push_back(jnt_init);
    qp_grasp.push_back(joint_f2);
    t_action.assign({2.5, 2.5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到识别角度位置.....");
    robot.move_ele(action, ctrl_mode);
    ROS_INFO("运动完成!");

    std::cout << "keyboard input to continue " << std::endl;
    std::getline(std::cin, input);
    if (input == "q") return 0;

    // while(ros::ok()){

    //开始识别
    if(istest == 1){
        std::vector<double> data = {0.99, 0.01, 0.01};  
        door_data.data = data;
    }else{
        while(ros::ok())
        {
            bool condition = camera.get_plane_normal(door_data);
            if (!condition)
            {
            }else if (door_data.data.size() == 3)
            {
                // cout << "door_data: " <<  door_data <<endl;
                break;
            }
        }
    } 
    robot2.kdl_fk(joint_f2, pose_l, left_arm);
    Vector3d pe2;
    pe2 << door_data.data[0] , door_data.data[1] , door_data.data[2];
    Eigen::Matrix3d TT = pose_l.T.block<3,3>(0,0);
    vec_door = TT * pe2;
    // cout << "vec_door: " <<  vec_door <<endl;
    double theta_door = atan2(vec_door[1], vec_door[0]);
    // std::cout << "theta_door: " << theta_door << std::endl;
    std::cout << "theta_door in degrees: " << theta_door * (180.0 / M_PI) << std::endl;

    rpy << 3.13615, -0.431056, 0.372257;
    Matrix3d r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
    Matrix3d r_dd = rpy2rotationMatrix(theta_door, 0, 0) * r_c ;
    Vector3d ypr = r_dd.eulerAngles(2, 1, 0);
    // }
    // vec_camera[0] = vec_camera[0]  - (theta_door * (180.0 / M_PI)-2)*0.06/13;
    if(theta_door * (180.0 / M_PI)-2 > 0){
        vec_camera[0] = vec_camera[0]  - (theta_door * (180.0 / M_PI)-2)*0.06/13;
    }
    vec_camera[1] = vec_camera[1]  + (theta_door * (180.0 / M_PI)+2)*0.03/13 - 0.05;
    vec_camera[2] = vec_camera[2]  - (theta_door * (180.0 / M_PI)-2)*0.06/13 + 0.05;
    vec << vec_camera[0] - 0.18 , vec_camera[1] + 0.17, vec_camera[2] + 0.25; //0.11 0.15
    // rpy << 3.13615, -0.431056, 0.372257;
    rpy << ypr[2], ypr[1], ypr[0];
    array<double, 7> joint_f3;
    error_flag = robot.kdl_ik(joint_init, vec, rpy, joint_f3, left_arm);
    if (error_flag == -1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }

    qp_grasp.clear();
    qp_grasp.push_back(joint_f2);
    qp_grasp.push_back(joint_f3);
    t_action.assign({2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到放置位置.....");
    robot.move_ele(action, ctrl_mode);
    ROS_INFO("运动完成!");

    // std::cout << "keyboard input to continue " << std::endl;
    // std::getline(std::cin, input);
    // if (input == "q") return 0;
    // return 0;

    //直线规划
    VectorXd pose_start(6), pose_end(6);
    pose_start << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];

    // vec << vec_camera[0] - 0.115, vec_camera[1] + 0.17, vec_camera[2] + 0.25; //0.11 0.15
    vec << vec_camera[0] - 0.08, vec_camera[1] + 0.17, vec_camera[2] + 0.25; //0.11 0.15
    rpy << ypr[2], ypr[1], ypr[0];
    pose_end << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    // pose_end << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
    
    action.left = robot.line_plan(joint_init, pose_start, pose_end, 10, left_arm);

    if (action.left.error_flag == -1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }

    ROS_INFO("直线运动.....");
    robot.move_ele(action, ctrl_mode, 1);
    ROS_INFO("运动完成!");

    array<double, 7> joint_f4;
    robot.kdl_ik(joint_init, vec, rpy, joint_f4, left_arm);
    // // vec << vec_camera[0] - 0.115, vec_camera[1] + 0.17, vec_camera[2] + 0.25; //0.11 0.15
    // vec << vec_camera[0] - 0.115, vec_camera[1] + 0.17, vec_camera[2] + 0.25; //0.11 0.15
    // // rpy << 3.13615, -0.431056, 0.372257;
    // rpy << ypr[2], ypr[1], ypr[0];
    // array<double, 7> joint_f4;
    // error_flag = robot.kdl_ik(joint_init, vec, rpy, joint_f4, left_arm);
    // if (error_flag == -1)
    // {
    //     ROS_ERROR("规划失败!");
    //     return 0;
    // }
    // qp_grasp.clear();
    // qp_grasp.push_back(joint_f2);
    // qp_grasp.push_back(joint_f3);
    // qp_grasp.push_back(joint_f4);
    // // qp_grasp.push_back(joint_f3);
    // t_action.assign({2, 7});
    // action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);
    // ROS_INFO("运动到放置位置.....");
    // robot.move(action, ctrl_mode);
    // ROS_INFO("运动完成!");


    std::cout << "keyboard input to continue " << std::endl;
    std::getline(std::cin, input);
    if (input == "q") return 0;

    array<double, 7> joint_end = arr_give(0, -10*M_PI/180, 0, 0, 0, 0, 0);
    dt = 0.001;
    qp_grasp.clear();
    qp_grasp.push_back(jnt_pos_vir_l2);
    qp_grasp.push_back(joint_f3);
    qp_grasp.push_back(joint_f2);
    qp_grasp.push_back(joint_end);
    // qp_grasp.push_back(pose0_l);
    t_action.assign({2, 3, 3});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到识别位置.....");
    robot.move_ele(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}