#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include <std_msgs/Int32.h>

// 利用Eigen库，采用SVD分解的方法求解矩阵伪逆，默认误差er为0
Eigen::MatrixXd pinv_eigen_based(Eigen::MatrixXd & origin, const float er = 0) {
    // 进行svd分解
    Eigen::JacobiSVD<Eigen::MatrixXd> svd_holder(origin,
                                                 Eigen::ComputeThinU |
                                                 Eigen::ComputeThinV);
    // 构建SVD分解结果
    Eigen::MatrixXd U = svd_holder.matrixU();
    Eigen::MatrixXd V = svd_holder.matrixV();
    Eigen::MatrixXd D = svd_holder.singularValues();

    // 构建S矩阵
    Eigen::MatrixXd S(V.cols(), U.cols());
    S.setZero();

    for (unsigned int i = 0; i < D.size(); ++i) {

        if (D(i, 0) > er) {
            S(i, i) = 1 / D(i, 0);
        } else {
            S(i, i) = 0;
        }
    }

    // pinv_matrix = V * S * U^T
    return V * S * U.transpose();
}

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        jnt_position_r[i] = msg->position[i+7];
    }

    for(int i = 0; i < 7; i++){
        // if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_l", jnt_position_l);
        //     ros::shutdown();
        //     exit(0);
        // }
        // if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_r", jnt_position_r);
        //     ros::shutdown();
        //     exit(0);
        // }
        if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
    }

    return;
}



int main(int argc, char *argv[])
{
    ros::init(argc, argv, "jy_joint");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
    ROBOT_CONTROL::robot_control robot2(urdf_path,"base_link","AL7","AR7");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    // ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);
    // ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //键盘输入
    std::string input;

    CAMERA_READ::camera_read camera;
    std_msgs::Float64MultiArray camera_data, door_data;
    Vector4d vec_camera;
    Vector3d vec_door;

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    // array<double, 7> jnt_init = {1.41,-0.64,-1.48,0.64,1.45,0.04,0};
    array<double, 7> jnt_init = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -0*M_PI/180, 10*M_PI/180, 0*M_PI/180);
    
    array<double, 7> q0, q1, q2, q3, q4, q5;
    def_msgs::Pose pose_l, pose_r;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    Vector3d vec(3), rpy(3);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;
    INSPIRE_HAND::Pos_Ctrl dataPub;
    // DATA_PROC::Data_Pub dataPub(false);

    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }


    //读取当前位置
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init, 11);
    }
    // return 0;
    //运动到识别位置
    pose0_r = arr_give(0.460, -0.502, -0.145, 1.256, 0, 0, 0);
    pose1_r = arr_give(0.60, -0.502, -0.145, 0.8, 0, 0, 0);
    pose2_r = arr_give(0, -M_PI*20/180, 0, 0, 0, 0, 0);
    // pose3_l = arr_give(-0.5799541257628321, -0.7744448812675493, 0.35345827270728186, 1.8989673113128778, -2.5638147779492237e-05, 1.2912156434552411e-05, -6.939520124725945e-06);
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(l_jnt_init);
    t_action.assign({100});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear(); 
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose2_r);
    qp_grasp.push_back(pose0_r);
    qp_grasp.push_back(pose1_r);
    t_action.assign({2, 3, 3});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到观瞄位置.....");
    robot.move_ele(action, ctrl_mode);
    ROS_INFO("运动完成!");

    std::cout << "keyboard input to continue " << std::endl;
    std::getline(std::cin, input);
    if (input == "q") return 0;

    qp_grasp.clear(); 
    qp_grasp.push_back(pose1_r);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({3});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("复位.....");
    robot.move_ele(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}