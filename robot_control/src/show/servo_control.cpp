#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <serial/serial.h>
#include <vector>
#include <unistd.h>
#include <iomanip>
#include <iostream>

serial::Serial ser;

// CRC-16-MODBUS 校验码计算
uint16_t calcCRC16(const uint8_t *data, size_t length) {
    uint16_t crc = 0xFFFF;
    for (size_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

// 发送舵机控制指令
void sendServoCommand(uint8_t id, uint16_t position, uint16_t speed, uint16_t acceleration) {
    // 构造数据包（不包括 CRC）
    std::vector<uint8_t> packet = {
        id, 0x10, 0x00, 0x80, 0x00, 0x04, 0x08,
        static_cast<uint8_t>(position >> 8), static_cast<uint8_t>(position & 0xFF), // 位置
        0x00, 0x01, // 固定值
        static_cast<uint8_t>(acceleration >> 8), static_cast<uint8_t>(acceleration & 0xFF), // 加速度
        static_cast<uint8_t>(speed >> 8), static_cast<uint8_t>(speed & 0xFF), // 速度
    };

    // 计算 CRC-16-MODBUS 校验码
    uint16_t crc = calcCRC16(packet.data(), packet.size());
    packet.push_back(static_cast<uint8_t>(crc & 0xFF)); // CRC 低字节
    packet.push_back(static_cast<uint8_t>(crc >> 8));   // CRC 高字节

    // 打印数据包
    std::cout << "Sending packet: ";
    for (size_t i = 0; i < packet.size(); i++) {
        std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(packet[i]) << " ";
    }
    std::cout << std::endl;

    // 发送数据
    int bytesSent = ser.write(packet.data(), packet.size());

    ROS_INFO("Bytes sent: %d", bytesSent);
}


// 订阅回调函数
void servoCallback(const std_msgs::Bool::ConstPtr &msg) {
    if (msg->data) {
        int goal = -97+360; // 目标角度
        int init = 0+360;
        
        uint16_t position = static_cast<uint16_t>((goal * 4095) / 360); 
        uint16_t position_init = static_cast<uint16_t>((init * 4095) / 360); 
        // uint16_t position = static_cast<uint16_t>(3413); // 
        uint16_t speed = static_cast<uint16_t>(300); // 速度
        uint16_t acceleration = static_cast<uint16_t>(200); // 加速度

        sendServoCommand(1, position, speed, acceleration);
        ROS_INFO("Received command: Moving servo to %d degrees", goal);

        sleep(3);

        sendServoCommand(1, position_init, speed, acceleration);
        ROS_INFO("Received command: Moving servo to init degrees");

    }
}

int main(int argc, char **argv) {
    ros::init(argc, argv, "servo_control_node");
    ros::NodeHandle nh;

    // 初始化串口
    try {
        ser.setPort("/dev/ttyXRUSB0");  // 这里换成你的485设备名
        ser.setBaudrate(115200);
        serial::Timeout to = serial::Timeout::simpleTimeout(1000);
        ser.setTimeout(to);
        ser.open();
    } catch (serial::IOException &e) {
        ROS_ERROR("Unable to open port");
        return -1;
    }

    if (ser.isOpen()) {
        ROS_INFO("Serial Port initialized");
    } else {
        return -1;
    }

    ros::Subscriber sub = nh.subscribe("/servo_command", 1, servoCallback);
    ros::spin();
}