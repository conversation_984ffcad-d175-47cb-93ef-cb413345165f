#include "arms_gen2_control/Def_Class.h"

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Demo01");
    ros::NodeHandle nh;

    DATA_PROC::Data_Pub dataPub(false);
    dataPub.leftHand();
    // dataPub.leftHand({10,10,10,10,999,500});
    sleep(1);
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();
    // while (ros::ok())
    // {
    //     /* code */
    // }
    
    return 0;
}
