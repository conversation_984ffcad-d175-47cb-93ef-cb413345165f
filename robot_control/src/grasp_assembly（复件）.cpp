#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include <std_msgs/Int32.h>
#include "assembly_control/Def_Class.h"


void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        jnt_position_r[i] = msg->position[i+7];
    }

    for(int i = 0; i < 7; i++){
        // if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_l", jnt_position_l);
        //     ros::shutdown();
        //     exit(0);
        // }
        // if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
        //     ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //     array_cout("jnt_position_r", jnt_position_r);
        //     ros::shutdown();
        //     exit(0);
        // }
        if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
            ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
        if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
            ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }
    }

    return;
}

int main(int argc, char *argv[])
{
    
    ros::init(argc, argv, "grasp_assembly");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    // ros::Subscriber sub2 = nh.subscribe("/conflict", 1, jointStateCallback2);
    // ros::Publisher  pub_conflict = nh.advertise<std_msgs::Int32>("/conflict", 1);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //键盘输入
    std::string input;

    CAMERA_READ::camera_read camera;
    std_msgs::Float64MultiArray camera_data;

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    array<double, 7> q0, q1, q2, q3, q4, q5;
    def_msgs::Pose pose_l, pose_r;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    Vector3d vec(3), rpy(3);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;
    INSPIRE_HAND::Pos_Ctrl dataPub;
    
    // DATA_PROC::Data_Pub dataPub(false);
    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    //DMP
    // ACT_LIB::DMP dmpLib;
    // double dt2 = 0.0014;
    // DATA_PROC_FUNC::DATA_SAVE dataSave;
    // PLANNING_FUNC::KINEMATICS kineFunc;
    // TRAJ_PLAN::INTERPOLATION interPlan;
    // TRAJ_PLAN::ADJUST adjustFunc;
    // ACT_LIB::DMP dmpLib;
    // ACT_LIB::INTER interLib;
    // ACT_LIB::MOVE moveLib;
    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }
    if(istest == 1){
        std::vector<double> data = {0.3, 0.15, -0.256, 1, 0.3, -0.15, -0.256, 0};  // 你想要的浮动数据
        camera_data.data = data;
    }else{
        while(ros::ok())
        {
            bool condition = camera.getCurrentState(camera_data);
            if (!condition)
            {
            // }else if (camera_data.data.size() == 8  && camera_data.data[3] == 1 && camera_data.data[7] == 0 && camera_data.data[1] > 0.1 && camera_data.data[5] < -0.09)
            }else if (camera_data.data.size() == 8  && camera_data.data[3] == 1 && camera_data.data[7] == 0 && camera_data.data[1] > 0.08 && camera_data.data[5] < -0.07)
            {
                cout << "camera_data: " <<  camera_data <<endl;
                break;
            }
        }
        
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 

    //手张开
    // handCtrl.leftHand({999, 999, 999, 999, 999, 10});
    // handCtrl.rightHand({999, 999, 999, 999, 999, 10});
    // handCtrl.doubleHand({999, 999, 999, 999, 999, 999}, {999, 999, 999, 999, 999, 999});
    dataPub.doubleHand({999, 999, 999, 999, 999, 999}, {999, 999, 999, 999, 999, 999});
    // return 0;
    //运动到桌子上方
    pose0_l = arr_give(-0.1603581552388118, -0.30437298667607204, -0.23593074957574117, 0.3851795288282412, 0.0, 0.0, 0.0);
    pose1_l = arr_give(-0.74836202917045, -0.6416922594344975, 0.005361566348440058, 1.2151920669714795, 0.0, 0.0, 0.0);
    pose2_l = arr_give(-1.1592952649848072, -0.6830057304052235, 0.29395041383401543, 1.6709042345793572, 0.0, 0.0, 0.0);
    pose3_l = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
    // pose3_l = arr_give(-0.5799541257628321, -0.7744448812675493, 0.35345827270728186, 1.8989673113128778, -2.5638147779492237e-05, 1.2912156434552411e-05, -6.939520124725945e-06);
    qp_grasp.clear();
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0_l);
    qp_grasp.push_back(pose1_l);
    qp_grasp.push_back(pose2_l);
    qp_grasp.push_back(pose3_l);
    t_action.assign({0.5, 1, 0.5, 1.5});
    // t_action.assign({1, 2, 1, 2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    pose0_r = arr_give(-0.1603581552388118, -0.30437298667607204, -0.23593074957574117, 0.3851795288282412, 0.0, 0.0, 0.0);
    pose1_r = arr_give(-0.74836202917045, -0.6416922594344975, 0.005361566348440058, 1.2151920669714795, 0.0, 0.0, 0.0);
    pose2_r = arr_give(-1.1592952649848072, -0.6830057304052235, 0.29395041383401543, 1.6709042345793572, 0.0, 0.0, 0.0);
    pose3_r = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
    // pose3_r = arr_give(-0.5799541257628321, -0.7744448812675493, 0.35345827270728186, 1.8989673113128778, -2.5638147779492237e-05, 1.2912156434552411e-05, -6.939520124725945e-06);
    qp_grasp.clear(); 
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0_r);
    qp_grasp.push_back(pose1_r);
    qp_grasp.push_back(pose2_r);
    qp_grasp.push_back(pose3_r);
    // t_action.assign({5, 5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");
    // return 0;
    //运动到抓取位置
    double tt2 = 2.5;
    robot.kdl_fk(pose3_l, pose_l, left_arm);
    
    pose_start_l << pose_l.position[0],pose_l.position[1],pose_l.position[2],pose_l.rpy[0],pose_l.rpy[1],pose_l.rpy[2];
    // vec << camera_data.data[0] + 0.085, camera_data.data[1] - 0.01, camera_data.data[2] + 0.01;
    vec << camera_data.data[0] + 0.092, camera_data.data[1] - 0.01 + 0.003, -0.3438 + 0.025;
    // rpy << 0, M_PI, M_PI;
    rpy << pose_start_l[3], pose_start_l[4], pose_start_l[5];
    pose_end_l << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    action.left = robot.line_plan(pose3_l, pose_start_l, pose_end_l, tt2, left_arm);

    array<double, 7> joint_f;
    robot.kdl_ik(pose3_l, vec, rpy, joint_f, left_arm);

    // action.left.pos = movingAverageFilter(action.left, 100);

    robot.kdl_fk(pose3_r, pose_r, right_arm);
    pose_start_r << pose_r.position[0],pose_r.position[1],pose_r.position[2],pose_r.rpy[0],pose_r.rpy[1],pose_r.rpy[2];
    // vec << camera_data.data[4] + 0.078, camera_data.data[5] + 0.015, camera_data.data[2] + 0.0622;
    vec << camera_data.data[4] + 0.086, camera_data.data[5] + 0.016, -0.306 + 0.0465;
    rpy << 0, 0, 0;
    pose_end_r << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    action.right = robot.line_plan(pose3_r, pose_start_r, pose_end_r, tt2, right_arm);


    if (action.left.error_flag == 1 || action.right.error_flag == 1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }

    ROS_INFO("运动到抓取位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
    // 大拇指闭合
    // handCtrl.leftHand({999, 999, 999, 999, 999, 100});
    // handCtrl.rightHand({999, 999, 999, 999, 999, 100});
    // handCtrl.doubleHand({999, 999, 999, 999, 999, 100}, {999, 999, 999, 999, 999, 100});
    dataPub.doubleHand({999, 999, 999, 999, 999, 100}, {999, 999, 999, 999, 999, 100});

    // std::cout << "keyboard input to continue " << std::endl;
    // std::getline(std::cin, input);
    // if (input == "q") return 0;
    
    

    //手抓
    // handCtrl.leftHand({500, 500, 500, 500, 500, 100});
    // handCtrl.rightHand({500, 500, 400, 400, 600, 100});
    // handCtrl.doubleHand({500, 500, 400, 400, 600, 100}, {500, 500, 500, 500, 500, 100});
    dataPub.doubleHand({500, 500, 350, 350, 600, 100}, {500, 500, 450, 450, 500, 100});

    // pose_start_l = pose_end_l;
    // pose_end_l[1] = 0.2;
    // pose_end_l[2] += 0.05;
    // action.left = robot.line_plan(pose1_l, pose_start_l, pose_end_l, 3, left_arm);

    // pose_start_r = pose_end_r;
    // pose_end_r[1] = -0.2;
    // pose_end_r[2] += 0.05;
    // action.right = robot.line_plan(pose1_r, pose_start_r, pose_end_r, 3, right_arm);

    // ROS_INFO("抬起来.....");
    // robot.move(action, ctrl_mode);
    // ROS_INFO("运动完成!");    
    double tt3 = 2.5;
    pose_start_l = pose_end_l;
    vec << 0.295302, 0.0200022, -0.152483;
    rpy << 0, M_PI, M_PI_2;
    pose_end_l << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    action.left = robot.line_plan(pose3_l, pose_start_l, pose_end_l, tt3, left_arm);

    robot.kdl_ik(pose3_l, vec, rpy, pose1_l, left_arm);

    pose_start_r = pose_end_r;
    vec << 0.294979, -0.0200005, -0.0176007;
    rpy << 0, 0, M_PI_2;
    pose_end_r << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    action.right = robot.line_plan(pose3_r, pose_start_r, pose_end_r, tt3, right_arm);

    robot.kdl_ik(pose3_r, vec, rpy, pose1_r, right_arm);

    if (action.left.error_flag == 1 || action.right.error_flag == 1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }
    

    ROS_INFO("抬起来.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!"); 

    ROS_INFO("Launching terminal...");
    
    // 要执行的命令
    std::string cmd_str = "gnome-terminal -- bash -c 'cd ~/robot_arm_v2/; source ./devel/setup.bash; rosrun assembly_control Action10_assembly;exec bash'";
    
    // 执行系统命令
    int result = system(cmd_str.c_str());
    
    if (result == 0) {
        ROS_INFO("Terminal launched successfully.");
    } else {
        ROS_ERROR("Failed to launch terminal. Error code: %d", result);
    }

    // flag_start2 = 1;
    // std_msgs::Int32 msg_conflict;
    // msg_conflict.data = 1;  // 设置要发布的整数值
    // pub_conflict.publish(msg_conflict);

    // while(flag_start2 == 1 && ros::ok()){
    //     sleep(0.1);
    // }

    // handCtrl.rightHand({999, 999, 999, 999, 999, 100});

    // sensor_msgs::JointState JointState2;
    // robot.getCurrentState(JointState2);
    // Getpos(JointState2, l_jnt_init, r_jnt_init);

    // qp_grasp.clear();
    // qp_grasp.push_back(l_jnt_init);
    // qp_grasp.push_back(joint_f);

    // t_action.assign({2});
    // action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    // qp_grasp.clear(); 
    // qp_grasp.push_back(r_jnt_init);
    // qp_grasp.push_back(r_jnt_init);

    // action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    // ROS_INFO("运动到目标位置.....");
    // robot.move(action, ctrl_mode);
    // ROS_INFO("运动完成!");


    return 0;
}