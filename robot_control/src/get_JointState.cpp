#include "robot_control/robot_control.h"

int main(int argc, char *argv[])
{
   ros::init(argc, argv, "get_JointPos");
   
   const char* urdf_path = getenv("URDF_PATH");
   ROBOT_CONTROL::robot_control robot(urdf_path,"base_link","l_arm_Link7","r_arm_Link7");

   sensor_msgs::JointState JointState;
   robot.getCurrentState(JointState);

   // 左臂（前7个）
   std::cout << "=== Left Arm (Degrees) ===" << std::endl;
   for (int i = 0; i < 7; ++i) {
      double angle_deg = JointState.position[i] * (180.0 / M_PI);
      std::cout << "Left Arm Joint[" << i << "]: " << angle_deg << "°" << std::endl;
   }

   // 右臂（后7个）
   std::cout << "\n=== Right Arm (Degrees) ===" << std::endl;
   for (int i = 7; i < 14; ++i) {
      double angle_deg = JointState.position[i] * (180.0 / M_PI);
      std::cout << "Right Arm Joint[" << i-7 << "]: " << angle_deg << "°" << std::endl;
   }

   cout << JointState << endl;

   // std::array<double, 7> l_q, r_q;
   // for (size_t i = 0; i < 7; i++)
   // {
   //    l_q[i] = JointState.position[i];
   //    r_q[i] = JointState.position[i+7];
   // }
   // def_msgs::Pose pose;
   // robot.kdl_fk(l_q, pose, left_arm);
   // array_cout("l_q", l_q);
   // pose_cout("l_pose", pose);

   // robot.kdl_fk(r_q, pose, right_arm);
   // array_cout("r_q", r_q);
   // pose_cout("r_pose", pose);

   return 0;
}