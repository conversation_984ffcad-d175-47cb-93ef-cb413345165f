#include "robot_control/robot_control.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "multi_joint_plan");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
    Vector3d vec, rpy;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{0.28, -0.56, -0.35, 0.81, -1.13, 0.35, -0.53 };
    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;
    std::array<double, 7> result;
    /*
    获取初始位置
    */
    // 默认值为0
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    }

    pose0 = arr_give(0.28, -0.56, -0.35, 0.81, -1.13, 0.35, -0.53);
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    t_action.assign({2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0);
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到初始位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    //画圆
    double tt = 100;
    vec << 0.428839, 0.331958, -0.394865;
    rpy << 1.6276, 3.08446, 2.88193; //roll, pitch, yaw
    action.left = robot.circle_plan(j_init, vec, rpy, tt, left_arm);

    vec << 0.428839, -0.331956, -0.394867;
    rpy << -1.62759, 0.0571292, 0.259658; //roll, pitch, yaw
    action.right = robot.circle_plan(j_init, vec, rpy, tt, right_arm);
    if (action.left.error_flag == 1 || action.right.error_flag == 1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }
    ROS_INFO("画圆.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}