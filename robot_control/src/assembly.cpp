#include "robot_control/robot_control.h" 
#include "robot_control/assembly.h" 

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_pos_act_l[i] = msg->position[i];
        jnt_pos_act_r[i] = msg->position[i+7];
    }

    return;
}

void jointCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_pos_vir_r[i] = msg->position[i];
        jnt_vel_vir_r[i] = msg->velocity[i];
        jnt_pos_vir_l[i] = msg->position[i+7];
        jnt_vel_vir_l[i] = msg->velocity[i+7];
    }
    // car_pos_vir_l = kdl_fk(l_chain, jnt_pos_vir_l);
    // car_pos_vir_r = kdl_fk(r_chain, jnt_pos_vir_r);

    std_msgs::Float64MultiArray car_pos_msg, car_vel_msg;
    car_pos_msg.data.resize(12);
    car_vel_msg.data.resize(12);
    for (size_t i = 0; i < 6; i++)
    {
        car_pos_msg.data[i] = car_pos_vir_l[i];
        car_pos_msg.data[i+6] = car_pos_vir_r[i];
        car_vel_msg.data[i] = car_vel_vir_l[i];
        car_vel_msg.data[i+6] = car_vel_vir_r[i];
    }
    //修正跳变
    car_pos_msg.data[11] = car_pos_msg.data[11] - car_pos_msg.data[9] + M_PI_2;

    car_vir_pos_pub.publish(car_pos_msg);
    car_vir_vel_pub.publish(car_vel_msg);



    std_msgs::Float64MultiArray car_pos_act_msg, car_vel_act_msg;
    car_pos_act_msg.data.resize(12);
    car_vel_act_msg.data.resize(12);

    for (size_t i = 0; i < 6; i++)
    {
        car_pos_act_msg.data[i] = car_pos_act_l[i];
        car_pos_act_msg.data[i+6] = car_pos_act_r[i];

    }
    //修正跳变
    car_pos_act_msg.data[11] = car_pos_act_msg.data[11] - car_pos_act_msg.data[9] + M_PI_2;

    car_act_pos_pub.publish(car_pos_act_msg);
    car_act_vel_pub.publish(car_vel_act_msg);

    std_msgs::Float64MultiArray car_pos_err_msg;
    car_pos_err_msg.data.resize(12);
    for (size_t i = 0; i < 6; i++)
    {
        car_pos_err_msg.data[i] = (car_pos_vir_l[i] - car_pos_act_l[i])*1000;
        car_pos_err_msg.data[i+6] = (car_pos_vir_r[i] - car_pos_act_r[i])*1000;
    }
    car_err_pos_pub.publish(car_pos_err_msg);
    // cout << "car_pos_act_msg: " << car_pos_err_msg.data[7] << endl;

    return;
}


int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"assembly");

    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber joint_sub = nh.subscribe("joint_states",10,jointCallback);
    car_vir_vel_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_vel/vir",1);
    car_act_vel_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_vel/act",1);
    car_vir_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/vir",1);
    car_act_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/act",1);
    car_err_pos_pub = nh.advertise<std_msgs::Float64MultiArray>("/car_pos/error",1);

    //限制
    // theta_limit = robot.theta_limit;
    // effort_limit = robot.effort_limit;
    cout<<theta_limit<<endl;
	//启用多线程
 	ros::AsyncSpinner spinner(3); 
 	spinner.start(); //开始标志
    sleep(2);

    //set init position
    array<double, 7> jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    //初始化为0
    jnt_pos_act_r = VectorXd::Zero(7);
    jnt_pos_act_l = VectorXd::Zero(7);
    // jnt_effort_r = VectorXd::Zero(7);
    // jnt_effort_l = VectorXd::Zero(7);    
    jnt_pos_vir_r = VectorXd::Zero(7);
    jnt_pos_vir_l = VectorXd::Zero(7);


    car_pos_vir_r = VectorXd::Zero(6);
    car_pos_vir_l = VectorXd::Zero(6);
    car_vel_vir_r = VectorXd::Zero(6);
    car_vel_vir_l = VectorXd::Zero(6);

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Arm action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;
    VectorXd pose_start(6), pose_end(6);

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //切换
    istest = 1;
    ctrl_mode = rviz_ctrl;

    /*
    运动到装配位置
    */


    return 0;
}
