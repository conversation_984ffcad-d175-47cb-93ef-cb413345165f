#include "arms_gen2_control/Def_Class.h"

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"get_r_force");
    ros::NodeHandle nh;

    DATA_PROC::Data_Pub dataPub(false);
    
    // 创建两个发布者
    ros::Publisher pub_force2 = nh.advertise<std_msgs::Float64MultiArray>("hand_r_force", 1);

    // 定义两个力传感器数据存储数组
    std::array<float, 6> curforce;
    std_msgs::Float64MultiArray msg_force;
    msg_force.data.resize(6); // 确保消息的 data 字段有足够的空间
    
    while (ros::ok()) {
        dataPub.getFORCE_ACT(curforce, 2); // 读取力传感器数据

        for (size_t i = 0; i < 6; i++) {
            msg_force.data[i] = curforce[i];
        }

        pub_force2.publish(msg_force); // 发布消息
    }

    return 0;
}
