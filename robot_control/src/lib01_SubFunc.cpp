#include <iostream>
#include <fstream>
#include "vector"
#include "string"
#include "ros/ros.h"
#include "sensor_msgs/JointState.h"
#include "std_msgs/Float64MultiArray.h"
#include "std_msgs/Float64.h"

#include "assembly_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

#define l0 0.2536
#define l1 0.2486
#define l2 0.1745
#define l3 0.07
#define l4 0.12


// FILE *fpWrite=fopen("theta7_can.txt","w");

namespace PLANNING_FUNC{
    Demon_Traj DMP_FUNC::dataExt(string pathName1, string pathName2){
        ifstream infile;
        infile.open(pathName1.data());   //将文件流对象与文件连接起来 
        assert(infile.is_open());   //若失败,则输出错误消息,并终止程序运行 
        vector<vector<double>> data1;
        vector<vector<double>> data2;
        vector<double> line;
        string s;
        while (getline(infile, s)) {
            istringstream is(s); //将读出的一行转成数据流进行操作
            double d;
            while (!is.eof()) {
                is >> d;
                line.push_back(d);
            }
            data1.push_back(line);
            line.clear();
            s.clear();
        }
        infile.close();             //关闭文件输入流 

        infile.open(pathName2.data());   //将文件流对象与文件连接起来 
        assert(infile.is_open());   //若失败,则输出错误消息,并终止程序运行 
        while (getline(infile, s)) {
            istringstream is(s); //将读出的一行转成数据流进行操作
            double d;
            while (!is.eof()) {
                is >> d;
                line.push_back(d);
            }
            data2.push_back(line);
            line.clear();
            s.clear();
        }
        infile.close();             //关闭文件输入流 

        int len = data1.size();
        Demon_Traj origin_data;
        origin_data.time.resize(len);
        origin_data.pos_ta1.resize(len);
        origin_data.pos_ta2.resize(len);
        origin_data.pos_ta3.resize(len);
        origin_data.pos_ta4.resize(len);
        origin_data.vel_ta1.resize(len);
        origin_data.vel_ta2.resize(len);
        origin_data.vel_ta3.resize(len);
        origin_data.vel_ta4.resize(len);
        origin_data.acc_ta1.resize(len);
        origin_data.acc_ta2.resize(len);
        origin_data.acc_ta3.resize(len);
        origin_data.acc_ta4.resize(len);
        origin_data.ArmJnt.resize(len);
        double dt;
        for (int i = 0; i < len; i++) {
            origin_data.time(i) = data1[i][0];
            origin_data.pos_ta1(i) = data1[i][1];
            origin_data.pos_ta2(i) = data1[i][2];
            origin_data.pos_ta3(i) = data1[i][3];
            origin_data.pos_ta4(i) = data1[i][4];
            origin_data.ArmJnt(i) = data2[i][1];
            if (i == 0) {
                origin_data.vel_ta1(i) = 0;
                origin_data.vel_ta2(i) = 0;
                origin_data.vel_ta3(i) = 0;
                origin_data.vel_ta4(i) = 0;
                origin_data.acc_ta1(i) = 0;
                origin_data.acc_ta2(i) = 0;
                origin_data.acc_ta3(i) = 0;
                origin_data.acc_ta4(i) = 0;
            }
            else {
                int j = i - 1;
                dt = origin_data.time(i) - origin_data.time(j);
                origin_data.vel_ta1(i) = (origin_data.pos_ta1(i) - origin_data.pos_ta1(j)) / dt;
                origin_data.vel_ta2(i) = (origin_data.pos_ta2(i) - origin_data.pos_ta2(j)) / dt;
                origin_data.vel_ta3(i) = (origin_data.pos_ta3(i) - origin_data.pos_ta3(j)) / dt;
                origin_data.vel_ta4(i) = (origin_data.pos_ta4(i) - origin_data.pos_ta4(j)) / dt;
                origin_data.acc_ta1(i) = (origin_data.vel_ta1(i) - origin_data.vel_ta1(j)) / dt;
                origin_data.acc_ta2(i) = (origin_data.vel_ta2(i) - origin_data.vel_ta2(j)) / dt;
                origin_data.acc_ta3(i) = (origin_data.vel_ta3(i) - origin_data.vel_ta3(j)) / dt;
                origin_data.acc_ta4(i) = (origin_data.vel_ta4(i) - origin_data.vel_ta4(j)) / dt;
                
            }
        }
        origin_data.ArmJnt = origin_data.ArmJnt.array() - origin_data.ArmJnt(0);
        return origin_data;
    }

    Train_Res DMP_FUNC::dmpTrain(Demon_Traj data, Train_Par par) {
    #pragma region INPUT&INIT
        VectorXd time = data.time;
        int n = time.size();
        double t_end = time(n-1);
        // cout << t_end << endl;

        MatrixXd x_demon(n, 4);
        x_demon.col(0) = data.pos_ta1;
        x_demon.col(1) = data.pos_ta2;
        x_demon.col(2) = data.pos_ta3;
        x_demon.col(3) = data.pos_ta4;
        MatrixXd dx_demon(n, 4);
        dx_demon.col(0) = data.vel_ta1;
        dx_demon.col(1) = data.vel_ta2;
        dx_demon.col(2) = data.vel_ta3;
        dx_demon.col(3) = data.vel_ta4;
        MatrixXd ddx_demon(n, 4);
        ddx_demon.col(0) = data.acc_ta1;
        ddx_demon.col(1) = data.acc_ta2;
        ddx_demon.col(2) = data.acc_ta3;
        ddx_demon.col(3) = data.acc_ta4;
        Vector4d x_init = x_demon.row(0);
        Vector4d x_goal = x_demon.row(n-1);

        double k = par.k;
        double alpha_y = par.alpha_y;
        int k_f = par.k_f;
        int N = par.nbfs;

        double tau = 1.0;
        double alpha_x = k * tau / t_end;
        double beta_y = alpha_y / 4;

        VectorXd tmp = VectorXd::Zero(N);
        VectorXd c = VectorXd::Zero(N);
        VectorXd h = VectorXd::Zero(N);
        MatrixXd w = MatrixXd::Ones(N,4);

        VectorXd x = VectorXd::Ones(n);
        MatrixXd s = MatrixXd::Zero(n,4);
        MatrixXd ft = MatrixXd::Zero(n,4);

        MatrixXd psi = MatrixXd::Ones(N,n);
    #pragma endregion
    
    #pragma region UPDATE
        for(int i = 0; i < N; i++)
        {
            tmp(i) = i * t_end / (N + 1);
            c(i) = exp(-alpha_x / tau * tmp(i));
            h(i) = (double)N / pow(c(i),2);
        }

        for (int i = 0; i < n; i++)
        {
            x(i) = exp(-alpha_x / tau * time(i));
            ft.row(i) = tau * tau * ddx_demon.row(i) - alpha_y * (beta_y * (x_goal.transpose() - x_demon.row(i)) - tau * dx_demon.row(i)); 
            s.row(i) = x(i) * (x_goal - x_init).array().abs();
        }

        for (int i = 0; i < N; i++)
        {
            for (int j = 0; j < n; j++)
            {
                psi(i,j) = exp(-h(i) * pow(x(j)-c(i),2));
            }
        }

        MatrixXd psi_x;
        double a,b;
        for (int i = 0; i < N; i++)
        {
            psi_x = psi.row(i).asDiagonal();
            for(int j = 0; j < 4; j++){
                a = s.col(j).transpose() * psi_x * ft.col(j);
                b = s.col(j).transpose() * psi_x * s.col(j);
                w(i,j) = a / (b + 1.0e-15);
            }
            
        }
    #pragma endregion

    #pragma region OUTPUT
    Train_Res res;
	res.w = w;
	res.c = c;
	res.h = h;
	res.N = N;
	res.alpha_x = alpha_x;
	res.alpha_y = alpha_y;
	res.beta_y = beta_y;
	res.t_end = t_end;
	res.k_f = k_f;

	return res;
    #pragma endregion
    }

    Plan_Res_MulDOF DMP_FUNC::lnrCmp(Plan_Res_MulDOF traj, Vector4d g) {
        VectorXd t = traj.t;
        double dt = t(1) - t(0);
        int n = traj.t.size();

        Vector4d traj_end = traj.pos.row(n-1).transpose();
        Vector4d err = g - traj_end;

        MatrixXd e = MatrixXd::Zero(n,4);
        MatrixXd x = MatrixXd::Zero(n,4);
        MatrixXd dx = MatrixXd::Zero(n,4);
        MatrixXd ddx = MatrixXd::Zero(n,4);

        for (int i = 0; i < n; i++)
        {
            e.row(i) = err * exp(-30 * t(n - i - 1));
            x.row(i) = traj.pos.row(i) + e.row(i);
        }
        for (int i = 1; i < n; i++)
        {
            dx.row(i) = (x.row(i) - x.row(i-1)) / dt;
            ddx.row(i) = (dx.row(i) - dx.row(i-1)) / dt;
        }
        
        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        return res;
    }

    Plan_Res_MulDOF DMP_FUNC::dmpRegress(Train_Res trainRes, Regress_Par par){
        Vector4d y0 = par.x_init;
        Vector4d g = par.x_goal;
        double tau = par.tau;
        double dt = par.dt;
        MatrixXd w = trainRes.w;
        VectorXd c = trainRes.c;
        VectorXd h = trainRes.h;
        int N = trainRes.N;
        int k_f = trainRes.k_f;
        double alpha_x = trainRes.alpha_x;
        double alpha_y = trainRes.alpha_y;
        double beta_y = trainRes.beta_y;
        double t_end = trainRes.t_end;

        t_end = tau * t_end;
        int n = floor(t_end / dt) + 1;
        // cout << n << endl;

        VectorXd t = VectorXd::Zero(n);
        VectorXd x = VectorXd::Zero(n);

        MatrixXd y = MatrixXd::Zero(n,4);
        MatrixXd dy = MatrixXd::Zero(n,4);
        MatrixXd ddy = MatrixXd::Zero(n,4);

        MatrixXd f_num = MatrixXd::Zero(n,4);
        MatrixXd f_denom = MatrixXd::Zero(n,4);
        MatrixXd f = MatrixXd::Zero(n,4);

        MatrixXd psi = MatrixXd::Ones(N,n);

        for (int i = 0; i < n; i++)
        {
            t(i) = i * dt;
            x(i) = exp(-alpha_x / tau * t(i));
        }

        y.row(0) = y0.transpose();

        for (int i = 1; i < n; i++)
        {
            for (int j = 0; j < N; j++)
            {
                psi(j,i) = exp(-h(j) * pow(x(i)-c(j),2));
                f_num.row(i) = f_num.row(i) + psi(j,i) * w.row(j);
                f_denom.row(i) = f_denom.row(i).array() + psi(j,i) + 0.00000000001;
            }
            f(i,0) = (f_num(i,0) * x(i) * abs(g(0) - y0(0))) / f_denom(i,0);
            f(i,1) = (f_num(i,1) * x(i) * abs(g(1) - y0(1))) / f_denom(i,1);
            f(i,2) = (f_num(i,2) * x(i) * abs(g(2) - y0(2))) / f_denom(i,2);
            f(i,3) = (f_num(i,3) * x(i) * abs(g(3) - y0(3))) / f_denom(i,3);
            ddy.row(i) = (alpha_y * (beta_y * (g.transpose() - y.row(i-1)) - tau * dy.row(i-1)) + f.row(i)).array() / pow(tau,2);
            dy.row(i) = dy.row(i-1) + ddy.row(i) * dt;
            y.row(i) = y.row(i-1) + dy.row(i) * dt;
        }
        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = y;
        res.vel = dy;
        res.acc = ddy;

        return res;
    }

    Plan_Res_MulDOF DMP_FUNC::limitAmplitude(Plan_Res_MulDOF traj, Vector4d y0, Vector4d g, Vector4d jntLimit){
        VectorXd t = traj.t;
        double dt = t(1) - t(0);
        int n = t.size();

        traj.pos = traj.pos.array()*180/PI;
        y0 = y0.array()*180/PI;
        g = g.array()*180/PI;
        MatrixXd y(n,4);

        VectorXd::Index maxRow, minRow;
        Vector4d m;
        Vector4d p;
        double k_err;

        for(int i = 0; i < 4; i++){
            if(jntLimit(i) <= 0){
                m(i) = traj.pos.col(i).minCoeff(&minRow);
                p(i) = minRow;
                if(m(i) < jntLimit(i) && g(i) > jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = (jntLimit(i) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = (jntLimit(i) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else if(m(i) < jntLimit(i) && g(i) < jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = ((g(i) - 20) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = ((g(i) - 20) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else{
                    for(int j = 0; j < n; j++){
                        y(j,i) = traj.pos(j,i);
                    }
                }
            }else{
                m(i) = traj.pos.col(i).maxCoeff(&maxRow);
                p(i) = maxRow;
                if(m(i) > jntLimit(i) && g(i) < jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = (jntLimit(i) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = (jntLimit(i) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else if(m(i) > jntLimit(i) && g(i) > jntLimit(i)){
                    for(int j = 0; j < n; j++){
                        if(j <= p(i)){
                            k_err = ((g(i) - 20) - y0(i))/(m(i) - y0(i));
                            y(j,i) = k_err * (traj.pos(j,i) - y0(i)) + y0(i);
                        }else{
                            k_err = ((g(i) - 20) - g(i))/(m(i) - g(i));
                            y(j,i) = k_err * (traj.pos(j,i) - g(i)) + g(i);
                        }
                    }
                }else{
                    for(int j = 0; j < n; j++){
                        y(j,i) = traj.pos(j,i);
                    }
                }
            }
        }

        m(0) = y.col(0).minCoeff(&minRow);
        p(0) = minRow;
        for(int i = 0; i < n; i++){
            if(i <= p(0)){
                k_err = (-45 - y0(0))/(m(0) - y0(0));
                y(i,0) = k_err * (y(i,0) - y0(0)) + y0(0);
            }else{
                k_err = (-45 - g(0))/(m(0) - g(0));
                y(i,0) = k_err * (y(i,0) - g(0)) + g(0);
            }
        }

        y = y.array()/180*PI;
        MatrixXd dy = MatrixXd::Zero(n,4);
        MatrixXd ddy = MatrixXd::Zero(n,4);

        for (int i = 1; i < n; i++)
        {
            dy.row(i) = (y.row(i) - y.row(i-1)) / dt;
            ddy.row(i) = (dy.row(i) - dy.row(i-1)) / dt;
        }

        Plan_Res_MulDOF res;
        res.t = t;
        res.pos = y;
        res.vel = dy;
        res.acc = ddy;
        return res;
    }

    Vector3d KINEMATICS::biasHand(Vector3d pose, Vector3d baisVec){
        Vector3d x_ide = Vector3d(1,0,0);
        Vector3d y_ide = Vector3d(0,1,0);
        Vector3d z_ide = Vector3d(0,0,1);
        AngleAxisd z_vec(PI/2-pose(0), z_ide);
        AngleAxisd y_vec(pose(1), y_ide);
        AngleAxisd x_vec(pose(2), x_ide);
        Matrix3d z_mtx = z_vec.matrix();
        Matrix3d y_mtx = y_vec.matrix();
        Matrix3d x_mtx = x_vec.matrix();

        Matrix3d mtx_rot = z_mtx * y_mtx * x_mtx;
        Vector3d res;
        res = mtx_rot * baisVec;
        // cout << res << endl;
        return res;
    }

    JntPos KINEMATICS::invKine_JntPos(Vector3d g, double phi, Vector3d pose){
        
        VectorXd d(7);
        VectorXd a(7);
        VectorXd alpha(7);
        VectorXd offset(7);
        d << l0, 0, l1, 0, l2, 0, 0;
        a << 0, 0, 0, 0, 0, l3, l4;
        alpha << -PI/2, PI/2, -PI/2, PI/2, -PI/2, -PI/2, 0;
        offset << -PI/2, PI/2, PI/2, 0, 0, -PI/2, 0;
        VectorXd theta_res = VectorXd::Zero(7);

        Vector3d x_ide = Vector3d(1,0,0);
        Vector3d y_ide = Vector3d(0,1,0);
        Vector3d z_ide = Vector3d(0,0,1);
        AngleAxisd z_vec(PI/2-pose(0), z_ide);
        AngleAxisd y_vec(pose(1), y_ide);
        AngleAxisd x_vec(pose(2), x_ide);
        Matrix3d z_mtx = z_vec.matrix();
        Matrix3d y_mtx = y_vec.matrix();
        Matrix3d x_mtx = x_vec.matrix();

        AngleAxisd trotx_vec(PI/2, x_ide);
        Matrix3d trotx = trotx_vec.matrix();
        // cout << trotx << endl;

        Matrix3d Rot07 = z_mtx * y_mtx * x_mtx;
        // cout << Rot07 << endl;
        Matrix3d Rot04;
        Matrix3d Rot47;
        Matrix4d Tran04;
        // cout << Tran04 << endl;
        Matrix4d Tran_Jnti;

        JntPos res;

        Vector3d shoulderPos;
        shoulderPos << 0, -l0, 0;
        Vector3d wristMidPos = g - Rot07 * x_ide * l4;
        // cout << wristMidPos << endl;
        Vector3d wristPos;
        Vector3d elbowPos_ref;
        Vector3d elbowPos;
        Vector3d vec_SE_ref;
        Vector3d vec_SE;
        Vector3d vec_SW;
        Vector3d vec_k;
        Matrix3d R_phi;
        double d_SW;
        double ka,kb;
        double s1,s2,s3,s4,s1_ref,s2_ref;
        double theta1_ref;
        double theta2_ref;
        double theta51;
        double theta52;

        double start_iterate = -PI/2;
        double end_iterate = PI/2;
        double step_iterate = 0.1 / 180 * PI;
        int num_iterate = round((end_iterate - start_iterate) / step_iterate);
        // VectorXd theta7_candidate = VectorXd::Zero(num_iterate+1);

        VectorXd theta7_candidate = VectorXd::LinSpaced(num_iterate+1,-PI/2,PI/2);
        Matrix3d jnt7_mtx;
        MatrixXd theta(num_iterate,7);
        // cout << num_iterate << endl;
        // cout << int(num_iterate/2) << endl;
        // cout << theta7_candidate(0) << endl;
        for(int i; i < num_iterate; i++){
            // cout << i << endl;
            jnt7_mtx(0,0) = cos(-theta7_candidate[i]);
            jnt7_mtx(0,1) = -sin(-theta7_candidate[i]);
            jnt7_mtx(0,2) = 0;
            jnt7_mtx(1,0) = sin(-theta7_candidate[i]);
            jnt7_mtx(1,1) = cos(-theta7_candidate[i]);
            jnt7_mtx(1,2) = 0;
            jnt7_mtx(2,0) = 0;
            jnt7_mtx(2,1) = 0;
            jnt7_mtx(2,2) = 1;

            wristPos = wristMidPos - Rot07 * jnt7_mtx * x_ide * l3;
            wristPos(0) = round(wristPos(0)*10000)/10000;
            wristPos(1) = round(wristPos(1)*10000)/10000;
            wristPos(2) = round(wristPos(2)*10000)/10000;
            // cout << "pos_w: " << wristPos(0) << ", " << wristPos(1) << ", " << wristPos(2) << endl;

            d_SW = sqrt(pow(wristPos(0)-shoulderPos(0),2) + pow(wristPos(1)-shoulderPos(1),2) + pow(wristPos(2)-shoulderPos(2),2));
            d_SW = round(d_SW*10000)/10000;

            if(d_SW > l1 + l2){
                res.error_flag = 1;
                continue;
            }else{
                // cout <<  "d_SW:" << d_SW << " < l1+l2:" << l1+l2 << endl;
                res.error_flag = 0;

                if(d_SW == l1 + l2){
                    theta(i,3) = 0;
                }else{
                    s4 = (pow(d_SW,2)-pow(l1,2)-pow(l2,2))/(2*l1*l2);
                    theta(i,3) = acos(s4);
                }
                s2_ref = (wristPos(1)+l0)/(l1+l2*cos(theta(i,3)));
                theta2_ref = asin(s2_ref);

                ka = l1*cos(theta2_ref)+l2*cos(theta2_ref)*cos(theta(i,3));
                kb = l2*sin(theta(i,3));
                s1_ref = (ka*wristPos(0)+kb*wristPos(2))/(ka*ka+kb*kb);
                theta1_ref = asin(s1_ref);

                elbowPos_ref(0) = l1*sin(theta1_ref)*cos(theta2_ref);
                elbowPos_ref(1) = -l0 + l1*sin(theta2_ref);
                elbowPos_ref(2) = -l1*cos(theta1_ref)*cos(theta2_ref);

                vec_SE_ref = elbowPos_ref - shoulderPos;
                vec_SW = wristPos - shoulderPos;

                vec_k = vec_SW.array() / vec_SW.norm();

                R_phi(0,0) = cos(phi) + pow(vec_k(0),2) * (1-cos(phi));
                R_phi(0,1) = vec_k(0) * vec_k(1) * (1-cos(phi)) - vec_k(2) * sin(phi);
                R_phi(0,2) = vec_k(0) * vec_k(2) * (1-cos(phi)) + vec_k(1) * sin(phi);
                R_phi(1,0) = vec_k(0) * vec_k(1) * (1-cos(phi)) + vec_k(2) * sin(phi);
                R_phi(1,1) = cos(phi) + pow(vec_k(1),2) * (1-cos(phi));
                R_phi(1,2) = vec_k(1) * vec_k(2) * (1-cos(phi)) - vec_k(0) * sin(phi);
                R_phi(2,0) = vec_k(0) * vec_k(2) * (1-cos(phi)) - vec_k(1) * sin(phi);
                R_phi(2,1) = vec_k(1) * vec_k(2) * (1-cos(phi)) + vec_k(0) * sin(phi);
                R_phi(2,2) = cos(phi) + pow(vec_k(2),2) * (1-cos(phi));

                vec_SE = R_phi * vec_SE_ref;

                elbowPos = vec_SE + shoulderPos;

                s2 = (elbowPos(1) + l0) / l1;
                theta(i,1) = asin(s2);
                s1 = elbowPos(0) / (l1 * cos(theta(i,1)));
                theta(i,0) = asin(s1);
                s3 = (wristPos(1) + l0 - l1 * sin(theta(i,1)) - l2 * cos(theta(i,3)) * sin(theta(i,1))) / (l2 * cos(theta(i,1)) * sin(theta(i,3)) + 1.0e-15);
                theta(i,2) = -asin(s3);
                // cout << theta(i,0) << ' ' << theta(i,1) << ' ' << theta(i,2) << ' ' << theta(i,3) << endl;

                Tran04 = Matrix4d::Identity();
                for(int j = 0; j < 4; j++){
                    Tran_Jnti(0,0) = cos(theta(i,j)+offset(j));
                    Tran_Jnti(0,1) = -cos(alpha(j))*sin(theta(i,j)+offset(j));
                    Tran_Jnti(0,2) = sin(alpha(j))*sin(theta(i,j)+offset(j));
                    Tran_Jnti(0,3) = a(j)*cos(theta(i,j)+offset(j));
                    Tran_Jnti(1,0) = sin(theta(i,j)+offset(j));
                    Tran_Jnti(1,1) = cos(alpha(j))*cos(theta(i,j)+offset(j));
                    Tran_Jnti(1,2) = -sin(alpha(j))*cos(theta(i,j)+offset(j));
                    Tran_Jnti(1,3) = a(j)*sin(theta(i,j)+offset(j));
                    Tran_Jnti(2,0) = 0;
                    Tran_Jnti(2,1) = sin(alpha(j));
                    Tran_Jnti(2,2) = cos(alpha(j));
                    Tran_Jnti(2,3) = d(j);
                    Tran_Jnti(3,0) = 0;
                    Tran_Jnti(3,1) = 0;
                    Tran_Jnti(3,2) = 0;
                    Tran_Jnti(3,3) = 1;

                    Tran04 = Tran04 * Tran_Jnti;
                }
                Rot04 = trotx * Tran04.block(0,0,3,3);
                Rot47 = Rot04.inverse() * Rot07;
                
                theta(i,5) = asin(-Rot47(2,2));
                theta(i,4) = atan2(Rot47(1,2)/cos(theta(i,5)),Rot47(0,2)/cos(theta(i,5)));
                theta(i,6) = atan2(-Rot47(2,1)/cos(theta(i,5)),Rot47(2,0)/cos(theta(i,5)));
                // fprintf(fpWrite,"%.8f %.8f\n",theta7_candidate[i]*180/PI,theta(i,6)*180/PI);
                
                if (isnan(theta(i,0)) || isnan(theta(i,1)) || isnan(theta(i,2)) || isnan(theta(i,3)) || isnan(theta(i,4)) || isnan(theta(i,5)) || isnan(theta(i,6))){
                    res.error_flag = 3;
                    continue;
                }
                bool iter_info_show = false;
                if(abs(theta(i,6) - theta7_candidate(i)) < 0.1*PI/180){
                    if(iter_info_show){
                        cout << "i: " << i << endl;
                        cout << "wristPos: " << wristPos(0) << ", " << wristPos(1) << ", " << wristPos(2) << endl; 
                        cout << "theta7_candidate: " << theta7_candidate(i) << endl;
                        cout << "theta7: " << theta(i,6) << endl;
                    }

                    theta_res(0) = theta(i,0);
                    theta_res(1) = theta(i,1);
                    theta_res(2) = theta(i,2);
                    theta_res(3) = theta(i,3);
                    theta_res(4) = theta(i,4);
                    theta_res(5) = theta(i,5);
                    theta_res(6) = theta(i,6);
                    res.solved_flag = 1;
                    // cout << "jnt_pos: " << theta_res(0) << ',' << theta_res(1) << ',' << theta_res(2) << ',' << theta_res(3) << ',' << theta_res(4) << ',' << theta_res(5) << ',' << theta_res(6) << endl;
                    break;
                }
            }
        }
        // fclose(fpWrite);

        res.theta = theta_res;
        MatrixXd theta_limit(7,2);
        theta_limit << -PI, PI,
                       -PI*135/180, PI*5/180,
                       -PI/2, PI/2,
                       0, PI*110/180,
                       -PI, PI,
                       -PI/2, PI/2,
                       -PI/2, PI/2;
        
        // for(int i = 0; i < 7; i++){
        //     cout << "theta" << i << "_limit: [" << theta_limit(i,0)*180/PI << "," << theta_limit(i,1)*180/PI << "]" << endl;
        // }
        // cout << "d_SW: " << d_SW << ", l1+l2: " << l1+l2 << endl;
        if(res.error_flag == 1){
            cout << "error: The point is unreachable! " << "d_SW:" << d_SW << " > l1+l2:" << l1+l2 << endl;
        }
        if(res.error_flag == 3){
            cout << "error: Reverse trigonometric function solving failed!" << endl;

        }
        for(int i = 0; i < 7; i++){
            if (res.theta(i) < theta_limit(i,0) || res.theta(i) > theta_limit(i,1)){
                res.error_flag = 2;
                cout << "error: Joint" << i+1 << " " << res.theta(i)*180/PI << " exceeds the limit position[" << theta_limit(i,0)*180/PI << ", " << theta_limit(i,1)*180/PI << ']' << endl;
            }
        }
        
        return res;
    }

    KineRes KINEMATICS::fkine(VectorXd theta){
        int n = theta.size();

        VectorXd d(7);
        VectorXd a(7);
        VectorXd alpha(7);
        VectorXd offset(7);
        d << l0, 0, l1, 0, l2, 0, 0;
        a << 0, 0, 0, 0, 0, l3, l4;
        alpha << -PI/2, PI/2, -PI/2, PI/2, -PI/2, -PI/2, 0;
        offset << -PI/2, PI/2, PI/2, 0, 0, -PI/2, 0;

        MatrixXd Mat = MatrixXd::Identity(4,4);
        Matrix4d Ti;
        for(int i = 0; i < n; i++){
            Ti << cos(theta(i)+offset(i)), -cos(alpha(i))*sin(theta(i)+offset(i)),  sin(alpha(i))*sin(theta(i)+offset(i)), a(i)*cos(theta(i)+offset(i)),
                  sin(theta(i)+offset(i)),  cos(alpha(i))*cos(theta(i)+offset(i)), -sin(alpha(i))*cos(theta(i)+offset(i)), a(i)*sin(theta(i)+offset(i)),
                  0, sin(alpha(i)), cos(alpha(i)), d(i),
                  0, 0, 0, 1;
            Mat = Mat*Ti;
        }

        Vector3d x_ide = Vector3d(1,0,0);
        AngleAxisd trotx_vec(PI/2, x_ide);
        Matrix3d trotx = trotx_vec.matrix();
        Matrix4d tranx = Matrix4d::Identity();
        tranx.block(0,0,3,3) = trotx;

        Mat = tranx*Mat;

        Matrix3d transMat = Mat.block(0,0,3,3);
        Vector3d cart_end_position;
        cart_end_position << Mat(0,3),Mat(1,3),Mat(2,3);
        // cart_end_position << round(Mat(0,3)*10000)/10000,round(Mat(1,3)*10000)/10000,round(Mat(2,3)*10000)/10000;

        double r,p,y,r1,r2,p1,p2,y1,y2;
        if(abs(Mat(2,0)) == 1){
            r = 0;
            if(Mat(2,0) == 1){
                p = -PI/2;
                y = -r + atan2(-Mat(0,1),-Mat(0,2));
            }else{
                p = PI/2;
                y = r + atan2(Mat(0,1),Mat(0,2));
            }
        }else{
            p1 = -asin(Mat(2,0));
            p2 = PI - p1;
            y1 = atan2(Mat(2,1)/cos(p1),Mat(2,2)/cos(p1));
            y2 = atan2(Mat(2,1)/cos(p2),Mat(2,2)/cos(p2));
            r1 = atan2(Mat(1,0)/cos(p1),Mat(0,0)/cos(p1));
            r2 = atan2(Mat(1,0)/cos(p2),Mat(0,0)/cos(p2));

            r = r1;
            p = p1;
            y = y1;
        }

        KineRes res;
        res.eul_y = y;
        res.eul_p = p;
        res.eul_r = r;
        res.transMatrix = transMat;
        res.cart_end_position = cart_end_position;
        return res;
    }

    Plan_Res INTERPOLATION::quinitic_poly_inter(VectorXd t_seg, VectorXd pos_seg, double dt){
        int num_node = pos_seg.size();
        double a0,a1,a2,a3,a4,a5;
        double tmp,ti;
        int count = 1;
        VectorXi n(num_node);
        VectorXd t(count),x(count),dx(count),ddx(count);
        t(0) =   t_seg(0);
        x(0) =   pos_seg(0);
        dx(0) =  0;
        ddx(0) = 0;

        for(int i = 0; i < num_node-1; i++){
            tmp = t_seg(i+1) - t_seg(i);
            n(i) = int(tmp/dt);
            t.conservativeResize(count+n(i));
            x.conservativeResize(count+n(i));
            dx.conservativeResize(count+n(i));
            ddx.conservativeResize(count+n(i));

            a0 = pos_seg(i);
            a1 = 0;
            a2 = 0 / 2;
            a3 =  20*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,3));
            a4 = -30*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,4));
            a5 =  12*(pos_seg(i+1)-pos_seg(i))/(2*pow(tmp,5));

            for(int j = count; j < count+n(i); j++){
                t(j) = t_seg(i) + j*dt;
                ti = (j-count)*dt;
                x(j) =     a0 +   a1*ti +    a2*pow(ti,2) +    a3*pow(ti,3) +   a4*pow(ti,4) + a5*pow(ti,5);
                dx(j) =    a1 + 2*a2*ti +  3*a3*pow(ti,2) +  4*a4*pow(ti,3) + 5*a5*pow(ti,4);
                ddx(j) = 2*a2 + 6*a3*ti + 12*a4*pow(ti,2) + 20*a5*pow(ti,3);
            }
            count = count + n(i);
        }

        Plan_Res res;
        res.t = t;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        return res;
    }

    Plan_Res INTERPOLATION::cub_spline_inter(VectorXd t_seg, VectorXd pos_seg, double dt, double v0, double vn){
        int num_node = pos_seg.size();
        VectorXd h = VectorXd::Zero(num_node-1);
        VectorXd delta_y = VectorXd::Zero(num_node-1);
        for(int i = 0; i < num_node-1; i++){
            h(i) = t_seg(i+1) - t_seg(i);
            delta_y(i) = pos_seg(i+1) - pos_seg(i);
        }

        VectorXd a = pos_seg;
        VectorXd b = VectorXd::Zero(num_node);
        VectorXd c = VectorXd::Zero(num_node);
        VectorXd d = VectorXd::Zero(num_node);

        MatrixXd Mtx_L = MatrixXd::Zero(num_node,num_node);
        VectorXd Mtx_R = VectorXd::Zero(num_node);

        VectorXi n_seg = VectorXi::Zero(num_node);

        for(int i = 0; i < num_node; i++){
            if(i == 0){
                Mtx_L(i,0) = 2 * h(0);
                Mtx_L(i,1) = h(0);
                Mtx_R(i) = 3 * (delta_y(0)/h(0) - v0);
                continue;
            }
            if(i == num_node - 1){
                Mtx_L(i,i-1) = h(num_node - 2);
                Mtx_L(i,i) = 2 * h(num_node - 2);
                Mtx_R(i) = 3 * (vn - delta_y(i-1)/h(i-1));
                continue;
            }
            Mtx_L(i,i-1) = h(i-1);
            Mtx_L(i,i) = 2 * (h(i-1) + h(i));
            Mtx_L(i,i+1) = h(i);
            Mtx_R(i) = 3 * (delta_y(i) / h(i) - delta_y(i-1) / h(i-1));
        }
        c = Mtx_L.lu().solve(Mtx_R);

        // cout << c << endl;

        for(int i = 0; i < num_node-1; i++){
            b(i) = delta_y(i) / h(i) - (c(i+1) + 2*c(i)) * h(i) / 3;
            d(i) = (c(i+1) - c(i)) / (3 * h(i));
            n_seg(i+1) = round((t_seg(i+1) - t_seg(i))/dt) + n_seg(i);
        }

        // cout << n_seg << endl;
        // cout << round((t_seg(num_node-1)-t_seg(0))/dt) << endl;

        VectorXd res_t = VectorXd::LinSpaced(n_seg(num_node-1)+1,t_seg(0),t_seg(num_node-1));
        VectorXd res_pos = VectorXd::Zero(n_seg(num_node-1)+1);
        VectorXd res_vel = VectorXd::Zero(n_seg(num_node-1)+1);
        VectorXd res_acc = VectorXd::Zero(n_seg(num_node-1)+1);

        for(int i = 0; i < num_node-1; i++){
            for(int j = n_seg(i); j < n_seg(i+1)+1; j++){
                res_pos(j) = a(i) + b(i)*(res_t(j)-t_seg(i)) + c(i)*pow(res_t(j)-t_seg(i),2) + d(i)*pow(res_t(j)-t_seg(i),3);
                res_vel(j) = b(i) + 2*c(i)*(res_t(j)-t_seg(i)) + 3*d(i)*pow(res_t(j)-t_seg(i),2);
                res_acc(j) = 2*c(i) + 6*d(i)*(res_t(j)-t_seg(i));
            }
        }

        // cout << res_pos(0) << endl;
        Plan_Res res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        return res;
    }
}

namespace DATA_PROC_FUNC{
    void DATA_SAVE::save2txt(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, FILE *fpWrite){
        int n = motion_r.t.rows();
        for(int i = 0; i < n; i++){
            fprintf(fpWrite,"%.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f %.4f\n",
                    motion_r.pos(i,0)*180/PI,
                    motion_r.pos(i,1)*180/PI,
                    motion_r.pos(i,2)*180/PI,
                    motion_r.pos(i,3)*180/PI,
                    motion_r.pos(i,4)*180/PI,
                    motion_r.pos(i,5)*180/PI,
                    motion_r.pos(i,6)*180/PI,
                    motion_l.pos(i,0)*180/PI,
                    motion_l.pos(i,1)*180/PI,
                    motion_l.pos(i,2)*180/PI,
                    motion_l.pos(i,3)*180/PI,
                    motion_l.pos(i,4)*180/PI,
                    motion_l.pos(i,5)*180/PI,
                    motion_l.pos(i,6)*180/PI);
        }
        // fclose(fpWrite);
    }

    void DATA_PUB::pub2rviz(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l){
        ros::NodeHandle nh;
        ros::Publisher pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);
        sensor_msgs::JointState jnt_state_msgs;

        int n = motion_r.t.rows();

        ros::Rate rate(1000);
        int num_jnt = 14;
        int num = int(num_jnt/2);
        while(ros::ok()){
            for(int i = 0; i < n; i++){
                jnt_state_msgs.header.stamp = ros::Time::now();
                jnt_state_msgs.header.frame_id = "pub2rviz";

                jnt_state_msgs.name.resize(num_jnt);
                jnt_state_msgs.position.resize(num_jnt);
                jnt_state_msgs.velocity.resize(num_jnt);
                
                for(int j = 0; j < num; j++){
                    jnt_state_msgs.name[j] = "r_arm_Joint" + to_string(j+1);
                    jnt_state_msgs.position[j] = motion_r.pos(i,j);
                    jnt_state_msgs.velocity[j] = motion_r.vel(i,j);
                    jnt_state_msgs.name[j+num] = "l_arm_Joint" + to_string(j+1);
                    jnt_state_msgs.position[j+num] = motion_l.pos(i,j);
                    jnt_state_msgs.velocity[j+num] = motion_l.vel(i,j);
                    // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

                }
                // jnt_state_msgs.position[0] = 0.2;
                pub_rviz.publish(jnt_state_msgs);
                rate.sleep();
            }
            // ros::spinOnce();
            break;
        }
    }

    void DATA_PUB::pub2gazebo(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l){
        ros::NodeHandle nh;
        ros::Publisher pub_r_jnt1_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt1_r_controller/command",10);
        ros::Publisher pub_r_jnt2_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt2_r_controller/command",10);
        ros::Publisher pub_r_jnt3_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt3_r_controller/command",10);
        ros::Publisher pub_r_jnt4_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt4_r_controller/command",10);
        ros::Publisher pub_r_jnt5_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt5_r_controller/command",10);
        ros::Publisher pub_r_jnt6_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt6_r_controller/command",10);
        ros::Publisher pub_r_jnt7_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt7_r_controller/command",10);
        ros::Publisher pub_l_jnt1_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt1_l_controller/command",10);
        ros::Publisher pub_l_jnt2_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt2_l_controller/command",10);
        ros::Publisher pub_l_jnt3_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt3_l_controller/command",10);
        ros::Publisher pub_l_jnt4_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt4_l_controller/command",10);
        ros::Publisher pub_l_jnt5_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt5_l_controller/command",10);
        ros::Publisher pub_l_jnt6_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt6_l_controller/command",10);
        ros::Publisher pub_l_jnt7_gazebo = nh.advertise<std_msgs::Float64>("/Arms_Gen2/jnt7_l_controller/command",10);
        std_msgs::Float64 jnt_state_msgs;

        int n = motion_r.t.rows();

        ros::Rate rate(1000);
        
        while(ros::ok()){
            for(int i = 0; i < n; i++){
                jnt_state_msgs.data = motion_l.pos(i,0);
                pub_l_jnt1_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,1);
                pub_l_jnt2_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,2);
                pub_l_jnt3_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,3);
                pub_l_jnt4_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,4);
                pub_l_jnt5_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,5);
                pub_l_jnt6_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_l.pos(i,6);
                pub_l_jnt7_gazebo.publish(jnt_state_msgs);

                jnt_state_msgs.data = motion_r.pos(i,0);
                pub_r_jnt1_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,1);
                pub_r_jnt2_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,2);
                pub_r_jnt3_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,3);
                pub_r_jnt4_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,4);
                pub_r_jnt5_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,5);
                pub_r_jnt6_gazebo.publish(jnt_state_msgs);
                jnt_state_msgs.data = motion_r.pos(i,6);
                pub_r_jnt7_gazebo.publish(jnt_state_msgs);

                rate.sleep();
            }
            // ros::spinOnce();
            break;
        }
    }

    void DATA_PUB::pub2motors(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l){
        ros::NodeHandle nh;
        ros::Publisher pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);

        ros::Publisher pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);
        sensor_msgs::JointState jnt_state_msgs_rviz;

        int num_jnt = 14;

        std_msgs::Float64MultiArray jnt_state_msgs;
        jnt_state_msgs.data.resize(28);

        int n = motion_r.t.rows();
        int num = 7;
        ros::Rate rate(1000);
        while(ros::ok()){
            for(int i = 0; i < n; i++){
                for(int j = 0; j < num; j++){
                    jnt_state_msgs.data[j] = motion_l.pos(i,j);
                    jnt_state_msgs.data[j+num] = motion_r.pos(i,j);
                    jnt_state_msgs.data[j+2*num] = 0;
                    jnt_state_msgs.data[j+3*num] = 0;
                }
                pub_motor.publish(jnt_state_msgs);

                jnt_state_msgs_rviz.header.stamp = ros::Time::now();
                jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

                jnt_state_msgs_rviz.name.resize(num_jnt);
                jnt_state_msgs_rviz.position.resize(num_jnt);
                jnt_state_msgs_rviz.velocity.resize(num_jnt);
                
                for(int j = 0; j < num; j++){
                    jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
                    jnt_state_msgs_rviz.position[j] = motion_r.pos(i,j);
                    jnt_state_msgs_rviz.velocity[j] = motion_r.vel(i,j);
                    jnt_state_msgs_rviz.name[j+num] = "l_arm_Joint" + to_string(j+1);
                    jnt_state_msgs_rviz.position[j+num] = motion_l.pos(i,j);
                    jnt_state_msgs_rviz.velocity[j+num] = motion_l.vel(i,j);
                    // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

                }
                // jnt_state_msgs.position[0] = 0.2;
                pub_rviz.publish(jnt_state_msgs_rviz);

                rate.sleep();
            }
            break;
        }
    }


}
