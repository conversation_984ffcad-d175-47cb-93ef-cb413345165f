#include "robot_control/robot_control.h"

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
    // detect_flag = true;
    
    // for (double value : msg->data) {
    //     container.push_back(value);
    //     // cout << "apple: " << value << endl;
    // }
}

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort[i] = msg->effort[i];
        jnt_position[i] = msg->position[i];
    }
    //关节位置校验
    MatrixXd theta_limit(7,2);
    theta_limit <<  -M_PI, M_PI,
                    -M_PI, M_PI*16/180,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI;
    //电流校验
    MatrixXd effort_limit(7,2);
    effort_limit <<  -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15;
    for(int i = 0; i < 7; i++){
        if (jnt_position[i] < theta_limit(i,0) || jnt_position[i] > theta_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit position : ");
            array_cout("jnt_position", jnt_position);
            ros::shutdown();
            exit(0);
        }

        if (jnt_effort[i] < effort_limit(i,0) || jnt_effort[i] > effort_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit effort : ");
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }

    }

    return;
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "line_plan");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //set init position
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    array<double, 7> pose0;

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    def_msgs::Pose pose, pose_l;
    Vector3d vec, rpy;
    Matrix4d T;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 3;
    double t2 = 5;
    double t3 = 7;
    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 
    // handCtrl.rightHand({999,999,999,999,550,10});

    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({100});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, 0.001);

    qp_grasp.clear();
    pose0 = arr_give(0.460, -0.502, -0.145, 1.256, -0.714, 0.531, -0.009);
    //左臂保持当前位置
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    t_action.assign({5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    //直线规划
    robot.kdl_fk(pose0, pose, left_arm);
    VectorXd pose_start(6), pose_end(6);
    pose_start << pose.position[0],pose.position[1],pose.position[2],pose.rpy[0],pose.rpy[1],pose.rpy[2];
    // vec << 0.330001, -0.3804, -0.158852;
    // rpy << 0, 0, M_PI/6;
    // pose_start << vec[0],vec[1]+0.1,vec[2],rpy[0],rpy[1],rpy[2];
    pose_end << pose.position[0] ,pose.position[1],pose.position[2]+0.15,pose.rpy[0],pose.rpy[1],pose.rpy[2];
    // pose_end << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
    
    action.left = robot.line_plan(pose0, pose_start, pose_end, 0.1, left_arm);

    if (action.left.error_flag == -1)
    {
        ROS_ERROR("规划失败!");
        return 0;
    }

    ROS_INFO("运动到抓取位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");
    return 0;
}
