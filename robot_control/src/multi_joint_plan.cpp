#include "robot_control/robot_control.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "multi_joint_plan");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_link","l_arm_Link7","r_arm_Link7");
    Vector3d vec, rpy;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    istest = 0;
    ctrl_mode = robot_ctrl;

    /*
    获取初始位置
    */
   istest = 1;
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    }
    // array_cout("l_jnt_init", l_jnt_init);
    // array_cout("r_jnt_init", r_jnt_init);

    // vec << 0.35, -0.15, -0.2;
    // rpy << -M_PI_2, -M_PI_2, -M_PI; //roll, pitch, yaw
    // robot.kdl_ik(j_init, vec, rpy, pose0, left_arm);
    // vec << 0.34, -0.15, -0.13;
    // robot.kdl_ik(j_init, vec, rpy, pose1, left_arm);
    pose0 = arr_give(-0.1603581552388118, -0.30437298667607204, -0.23593074957574117, 0.3851795288282412, 0.0, 0.0, 0.0);
    pose1 = arr_give(-0.74836202917045, -0.6416922594344975, 0.005361566348440058, 1.2151920669714795, 0.0, 0.0, 0.0);
    pose2 = arr_give(-1.1592952649848072, -0.6830057304052235, 0.29395041383401543, 1.6709042345793572, 0.0, 0.0, 0.0);
    pose3 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
    pose4 = arr_give(0.7850279760118953, -0.41392808296576633, -1.0509720167472572, 1.2498451869282914, 0.8618114725147112, 0.10562896424217197, 0.8883516196539216);

    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose2);
    qp_grasp.push_back(pose3);
    qp_grasp.push_back(pose4);
    t_action.assign({1, 2, 1, 2, 2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    // vec << 0.35, 0.05, 0.2;
    // rpy << -M_PI_2, M_PI_2, 0; //roll, pitch, yaw
    // robot.kdl_ik(j_init, vec, rpy, pose0, right_arm);
    // vec << 0.36, 0.05, 0.13;
    // robot.kdl_ik(j_init, vec, rpy, pose1, right_arm);
    pose0 = arr_give(-0.1603581552388118, -0.30437298667607204, -0.23593074957574117, 0.3851795288282412, 0.0, 0.0, 0.0);
    pose1 = arr_give(-0.74836202917045, -0.6416922594344975, 0.005361566348440058, 1.2151920669714795, 0.0, 0.0, 0.0);
    pose2 = arr_give(-1.1592952649848072, -0.6830057304052235, 0.29395041383401543, 1.6709042345793572, 0.0, 0.0, 0.0);
    pose3 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
    pose4 = arr_give(1.293585947742837, -0.511828860023065, -0.9362194697331507, 1.551231470776691, 1.2887340017571667, -0.4752453735093168, 0.677800311175473);

    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose2);
    qp_grasp.push_back(pose3);
    qp_grasp.push_back(pose4);
    // t_action.assign({10, 5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}