#include "robot_control/robot_control.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "fk_kdl");

    const char* urdf_path = getenv("URDF_PATH");
    // ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_link","AL7","AR7");

    // std::array<double, 7> joint{0.324392, 0.492657, 0.545566, -0.415706, -0.179565, 3.51719, 0.459035 };
    // array<double, 7> joint = {0,0*M_PI/180,0,0,0,0,0};
    array<double, 7> joint = {0.861636, -0.379348, -0.350379, 1.19425, 0.336415, 0.00524743, -0.504232};
    array<double, 7> joint2 = {1.59969, -0.607114, 1.63731, -1.53491, -1.55001, -0.0837471, 0.641356};
    def_msgs::Pose pose;
    int kinematics_status;

    robot.kdl_fk(joint, pose, left_arm);
    pose_cout("l_pose", pose);

    robot.kdl_fk(joint, pose, right_arm);
    pose_cout("r_pose", pose);


    return 0;
}