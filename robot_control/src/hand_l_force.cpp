#include "arms_gen2_control/Def_Class.h"


void publishForceData(ros::Publisher& pub, DATA_PROC::Data_Pub& dataPub, int force_id) {

    std::array<float, 6> curforce;    
    std_msgs::Float64MultiArray msg_force;
    msg_force.data.resize(6); // 确保消息的 data 字段有足够的空间
    while (ros::ok()) {
        dataPub.getFORCE_ACT(curforce, force_id); // 读取力传感器数据

        for (size_t i = 0; i < 6; i++) {
            msg_force.data[i] = curforce[i];
        }

        pub.publish(msg_force); // 发布消息
    }
    return;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"get_l_force");
    ros::NodeHandle nh;

    DATA_PROC::Data_Pub dataPub(false);
    
    // 创建两个发布者
    ros::Publisher pub_force1 = nh.advertise<std_msgs::Float64MultiArray>("hand_l_force", 1);
    // ros::Publisher pub_force2 = nh.advertise<std_msgs::Float64MultiArray>("hand_r_force", 1);

    // 定义两个力传感器数据存储数组
    std::array<float, 6> curforce;
    std_msgs::Float64MultiArray msg_force;
    msg_force.data.resize(6); // 确保消息的 data 字段有足够的空间
    while (ros::ok()) {
        dataPub.getFORCE_ACT(curforce, 1); // 读取力传感器数据

        for (size_t i = 0; i < 6; i++) {
            msg_force.data[i] = curforce[i];
        }

        pub_force1.publish(msg_force); // 发布消息
    }

    // // 创建两个线程分别处理发布任务
    // std::thread thread1(publishForceData, std::ref(pub_force1), std::ref(dataPub), 2);
    // std::thread thread2(publishForceData, std::ref(pub_force2), std::ref(dataPub), 1);

    // // 主线程继续运行（如果需要）
    // // ros::spin();

    // // 等待线程结束
    // thread1.join();
    // thread2.join();

    // while (ros::ok())
    // {
    //     auto start = std::chrono::steady_clock::now();
    //     dataPub.getFORCE_ACT(curforce_, 1); //读一次0.114s
    //     auto end = std::chrono::steady_clock::now();
    //     auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end-start).count();
    //     ROS_INFO("time: [%.5fs]\n", (float)duration/1000000);
    //     for (float f : curforce_) {
    //         std::cout << f << " ";
    //     }
    //     std::cout << std::endl;
    // }


    return 0;
}
