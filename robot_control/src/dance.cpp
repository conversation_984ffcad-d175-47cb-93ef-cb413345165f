#include "robot_control/robot_control.h"
#define BOOST_BIND_GLOBAL_PLACEHOLDERS
#include <rosbag/bag.h>
#include <rosbag/view.h>

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "dance");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_link","l_arm_Link7","r_arm_Link7");
    Vector3d vec, rpy;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5, pose6, pose7, pose8, pose9, pose10, pose11,pose12, pose13, pose14, pose15, pose16, pose17,
                     pose18, pose19, pose20, pose21, pose22, pose23, pose24, pose25, pose26, pose27, pose28,

                     pose50, pose51, pose52, pose53, pose54, pose55, pose56, pose57, pose58;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    istest = 0;
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    /*
    获取初始位置
    */
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    }
    // array_cout("l_jnt_init", l_jnt_init);
    // array_cout("r_jnt_init", r_jnt_init);

    // vec << 0.35, -0.15, -0.2;
    // rpy << -M_PI_2, -M_PI_2, -M_PI; //roll, pitch, yaw
    // robot.kdl_ik(j_init, vec, rpy, pose0, left_arm);
    // vec << 0.34, -0.15, -0.13;
    // robot.kdl_ik(j_init, vec, rpy, pose1, left_arm);

    //1 上下挥手
    pose0 = arr_give(1.55, -0.55, -1.6, 1.24, 1.48, 0.0, 0.0);
    pose1 = arr_give(1.35, -0.55, -1.6, 1.24, 1.48, 0.0, 0.0);
    //复位
    pose2 = arr_give(0,-M_PI*15/180,0,0,0,0,0);
    //2 抬手
    pose3 = arr_give(M_PI*90/180,-M_PI*8/180,-M_PI*90/180,0,0,0,0);
    //3 左手抬手到胸前
    pose4 = arr_give(M_PI*0/180,-0.8,-M_PI*90/180,0.4,M_PI*90/180,0,0);//1
    pose5 = arr_give(M_PI*90/180,-M_PI*5/180,-M_PI*90/180,M_PI*90/180,0,0,0);
    //4 双臂张开摇晃
    pose6 = arr_give(0,-0.75,M_PI*90/180,M_PI*90/180,0,0,0);
    pose7 = arr_give(0,-0.3,M_PI*90/180,1.41,0,0,0);
    pose8 = arr_give(0,-0.9,M_PI*90/180,M_PI*90/180,0,0,0);
    //5 左手胸前，右手抬起来
    pose9 = arr_give(0,-2.5,M_PI*90/180,0,0,0,0);//r
    pose10 = arr_give(0,-2.7,M_PI*90/180,0,0,0,0);//r
    pose11 = arr_give(M_PI*70/180,-M_PI*5/180,-M_PI*90/180,M_PI*90/180,0,0,0);//l
    //16 双手抬到胸前
    pose12 = arr_give(-M_PI*90/180,-1.95,M_PI*90/180,1.84,0,0.88,0);//
    pose13 =arr_give(-M_PI*90/180,-1.95,M_PI*90/180,0.91,0,0.08,0);//
    //18 双手分别抬到胸前
    pose14 = arr_give(M_PI*70/180,-M_PI*5/180,-M_PI*90/180,M_PI*90/180,0,0,0);//
    // pose14 = arr_give(M_PI*90/180,-M_PI*5/180,-M_PI*90/180,M_PI*90/180,0,0,0);
    //21
    pose15 = arr_give(M_PI*90/180,-0.46,-M_PI*90/180,1.61,0,0,0);//r
    pose16 = arr_give(0,-1.22,-M_PI*90/180,-0.95,-M_PI*60/180,0,0);//l
    pose17 = arr_give(M_PI*90/180,-0.46,-M_PI*90/180,1.35,0,0,0);//r
    pose18 = arr_give(0,-1,-M_PI*90/180,-0.85,-M_PI*90/180,0,0);//l
    pose19 = arr_give(0,-0.9,-M_PI*0/180,0,0,0,0);//r
    pose20 = arr_give(0,-2.24,-M_PI*90/180,0,0,0,0);//l
    pose21 = arr_give(0,-2.6,-M_PI*90/180,0,0,0,0);//l

    //27
    pose22 = arr_give(0,-0.63,0.93,1.53,0,0,0);//r
    pose23 = arr_give(0.8,-0.48,1.03,-0.2,-M_PI*90/180,0,0);//l
    pose24 = arr_give(0,-M_PI*90/180,M_PI*90/180,0,M_PI*90/180,0,0);//r
    pose25 = arr_give(0,-1.86,M_PI*90/180,0.9,0,0,0);//l
    pose26 = arr_give(0,-2.2,M_PI*90/180,1.25,0,0,0);

    //30
    pose27 = arr_give(M_PI*90/180,-M_PI*5/180,-M_PI*90/180,1.27,0,0,0);//r
    pose28 = arr_give(0,-1.05,M_PI*90/180,0.65,0,0,0);//l

    //激光
    pose50 = arr_give(1.35, -0.16, -0.52, 1.37, 0, 0.0, 0.0);//r
    pose51 = arr_give(0.99, -0.16, -1.37, 1.41, -0.41, 0.0, 0.0);//l
    pose52 = arr_give(1.35, -0.16, -0.15, 1.37, 0, 0.0, 0.0);//r
    pose53 = arr_give(1.35, -0.16, -0.15, 1.37, 1.5, 0.0, 0.0);//r

    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose50);
    qp_grasp.push_back(pose52);
    qp_grasp.push_back(pose52);
    qp_grasp.push_back(pose51);
    qp_grasp.push_back(pose51);

    t_action.assign({2, 2, 1, 3, 2});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose51);
    qp_grasp.push_back(pose51);
    qp_grasp.push_back(pose50);
    qp_grasp.push_back(pose52);
    qp_grasp.push_back(pose53);
    t_action.assign({2, 2, 2, 2, 2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    // 控手
    // DATA_PROC::Data_Pub dataPub(false);
    // dataPub.leftHand();
    // dataPub.leftHand({10,10,10,999,999,10});
    // dataPub.leftHand({10,10,999,999,999,10});
    // dataPub.leftHand({10,999,999,999,999,10});
    // return 0;

    // bag 文件路径
    std::string bag_file = "/home/<USER>/src/robot_control/data/hand_dance.bag";

    rosbag::Bag bag;
    bag.open(bag_file, rosbag::bagmode::Read);

    // 只读取 /joint_states
    std::vector<std::string> topics;
    topics.push_back(std::string("/motor_command/arm_position_control"));

    rosbag::View view(bag, rosbag::TopicQuery(topics));

    // 先统计消息数量
    size_t msg_count = view.size();
    std::cout << "Found " << msg_count << " messages in /joint_states" << std::endl;

    // -------- 设置起止索引 --------
    const size_t start_idx = 2000;   // 从第 2000 条（含）
    const size_t end_idx   = 14500;  // 到第 17000 条（含）
    msg_count = end_idx - start_idx + 1;

    // 分配 Eigen 矩阵
    Eigen::MatrixXd pos_bl(msg_count, 7);   // 每行 14 个关节
    Eigen::MatrixXd pos_br(msg_count, 7);   // 每行 14 个关节
    Eigen::VectorXd t_b(msg_count);         // 时间戳

    size_t ii = 0;
    for (const rosbag::MessageInstance& m : view)
    {
        std_msgs::Float64MultiArray::ConstPtr js = m.instantiate<std_msgs::Float64MultiArray>();
        if (js != nullptr)
        {
            if (js->data.size() != 28)
            {
                ROS_WARN("Message %zu has %zu positions, expected 14", ii, js->data.size());
                continue;
            }
            if (ii >= start_idx && ii <= end_idx) {
                // 赋值 position
                for (size_t j = 0; j < 7; j++)
                {
                    pos_bl(ii - start_idx, j) = js->data[j];
                    pos_br(ii - start_idx, j) = js->data[j+7];
                }
                t_b[ii - start_idx] = 1;
            }
            if (ii == start_idx) {
                // 赋值 position
                for (size_t j = 0; j < 7; j++)
                {
                    pose54[j] = js->data[j];
                }
            }
            if (ii == end_idx) {
                // 赋值 position
                for (size_t j = 0; j < 7; j++)
                {
                    pose55[j] = js->data[j];
                }
            }
            ii++;
        }
    }

    bag.close();

    qp_grasp.clear();
    qp_grasp.push_back(pose51);
    qp_grasp.push_back(pose54);
    t_action.assign({2});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(pose53);
    qp_grasp.push_back(pose54);
    t_action.assign({2});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    action.left.pos = pos_bl;
    action.left.vel = pos_bl;
    action.left.acc = pos_bl;
    action.left.error_flag = 0;
    action.left.t = t_b;
    action.right = action.left;

    int n1 = action.left.t.rows();
    std::cout<<"n1 "<<n1;

    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    // return 0;


    qp_grasp.clear();
    qp_grasp.push_back(pose55);
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);

    qp_grasp.push_back(pose2);
    qp_grasp.push_back(pose3);
    qp_grasp.push_back(pose3);
    // qp_grasp.push_back(pose2);

    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose5);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose6);
    qp_grasp.push_back(pose7);
    qp_grasp.push_back(pose6);

    qp_grasp.push_back(pose5);
    qp_grasp.push_back(pose11);
    qp_grasp.push_back(pose5);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose12);
    qp_grasp.push_back(pose13);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose14);
    qp_grasp.push_back(pose4);

    t_action.assign({2, 1, 1, 1,   1.5, 1.5, 2,   1, 1, 1,   1.5, 1, 1,   2, 1, 1, 1.5,   2, 1, 2,   1, 1, 1, 1});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(pose55);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose0);

    qp_grasp.push_back(pose2);
    qp_grasp.push_back(pose2);
    qp_grasp.push_back(pose3);
    // qp_grasp.push_back(pose2);

    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose6);
    qp_grasp.push_back(pose8);
    qp_grasp.push_back(pose6);

    qp_grasp.push_back(pose9);
    qp_grasp.push_back(pose10);
    qp_grasp.push_back(pose9);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose12);
    qp_grasp.push_back(pose13);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose14);
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose4);

    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    // return 0;

    qp_grasp.clear();
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose16);
    qp_grasp.push_back(pose18);
    qp_grasp.push_back(pose20);
    qp_grasp.push_back(pose21);
    qp_grasp.push_back(pose20);
    qp_grasp.push_back(pose21);
    qp_grasp.push_back(pose19);

    qp_grasp.push_back(pose23);
    qp_grasp.push_back(pose22);
    qp_grasp.push_back(pose23);
    qp_grasp.push_back(pose25);
    qp_grasp.push_back(pose26);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose28);
    qp_grasp.push_back(pose2);

    t_action.assign({1, 1, 1, 2, 1, 1, 1,   1, 1, 1, 1, 1, 2   ,1, 1.5});
    // t_action.assign({2, 2, 2, 2, 2, 2, 2,   2, 2, 2, 2, 2, 2   ,5, 5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(pose4);
    qp_grasp.push_back(pose15);
    qp_grasp.push_back(pose17);
    qp_grasp.push_back(pose19);
    qp_grasp.push_back(pose19);
    qp_grasp.push_back(pose19);
    qp_grasp.push_back(pose19);
    qp_grasp.push_back(pose19);

    qp_grasp.push_back(pose22);
    qp_grasp.push_back(pose23);
    qp_grasp.push_back(pose22);
    qp_grasp.push_back(pose24);
    qp_grasp.push_back(pose26);
    qp_grasp.push_back(pose4);

    qp_grasp.push_back(pose27);
    qp_grasp.push_back(pose2);

    // t_action.assign({10, 5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到装配位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}