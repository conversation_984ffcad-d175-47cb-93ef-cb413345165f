#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"

// 定义颜色转义序列
#define RESET   "\033[0m"
#define GRE<PERSON>   "\033[32m"

int flag_hand = 0;
std::vector<double> target_vel_(14, 0.0);   // 初始化为4个0

// 工作函数
void leftHandThread(DATA_PROC::Data_Pub* dataPub) {

    while(flag_hand == 0){
        usleep(100);
    }
    // usleep(5000);
    dataPub->leftHand({999,999,999,999,999,100},{999,999,999,999,999,999});
    ROS_INFO("%s手张开%s", GREEN, RESET);
}   

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "throw");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Publisher pub_motor, pub_rviz;
    pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //键盘输入
    std::string input;

    DATA_PROC::Data_Pub dataPub(false);
    
    sleep(5);
    dataPub.leftHand({100,100,100,100,999,100});
    
    // return 0;

    // 启动线程
    std::thread t(leftHandThread, &dataPub);
    t.detach();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 
   
    pose0 = arr_give(M_PI*360/180,-M_PI*2/180,0,0,0,0,0);
    //左臂保持当前位置
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    t_action.assign({5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({20});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    pose0_l = pose0;
    pose0_r = r_jnt_init;

    double target_a = -M_PI*500/180;
    int  tt = 0;
    while (ros::ok())
    {
        target_vel_[0] += 0.001*target_a;

        if(target_vel_[0] < -M_PI*250/180){

        }
        if(tt == 700){
            flag_hand = 1;
        }

        if(target_vel_[0] < -M_PI*250/180){
            target_vel_[0] = -M_PI*250/180;
            
        }
        if(target_vel_[0] > M_PI*0/180){
            target_vel_[0] = M_PI*0/180;
            return 0;
        }
        pose0_l[0] = pose0_l[0] + target_vel_[0]*0.001;

        if(pose0_l[0] < M_PI*150/180){
            target_a = M_PI*600/180;
        }

        sensor_msgs::JointState jnt_state_msgs_rviz;
        int num_jnt = 14;
        std_msgs::Float64MultiArray jnt_state_msgs;
        jnt_state_msgs.data.resize(28);
        int num = 7;
        int num2 = 7;
        ros::Rate rate(1000);

        if(ctrl_mode == robot_ctrl){
            for(int j = 0; j < num; j++){
                jnt_state_msgs.data[j] = pose0_l[j];
            }
            for(int j = 0; j < num2; j++){
                jnt_state_msgs.data[j + num] = pose0_r[j];
            }
            for(int j = num + num2; j < 28; j++){
                jnt_state_msgs.data[j] = 0;
            }
            pub_motor.publish(jnt_state_msgs);

        }

        jnt_state_msgs_rviz.header.stamp = ros::Time::now();
        jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

        jnt_state_msgs_rviz.name.resize(num_jnt);
        jnt_state_msgs_rviz.position.resize(num_jnt);
        jnt_state_msgs_rviz.velocity.resize(num_jnt);

         for(int j = 0; j < num; j++){
               jnt_state_msgs_rviz.name[j+num2] = "l_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j+num2] = pose0_l[j];
               jnt_state_msgs_rviz.velocity[j+num2] = 0;
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         for(int j = 0; j < num2; j++){
               jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j] = pose0_r[j];
               jnt_state_msgs_rviz.velocity[j] = 0;
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         // jnt_state_msgs.position[0] = 0.2;
         pub_rviz.publish(jnt_state_msgs_rviz);

        rate.sleep();
        tt += 1;
    
    }

    sleep(1);

    return 0;
}