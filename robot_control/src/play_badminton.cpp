#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "joint_position_controller");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;
    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 
    DATA_PROC::Data_Pub dataPub(false);

    //左臂保持当前位置
    pose0 = arr_give(0.712055, -0.190262, -0.510384, 0.536654, 0.0872926, 0.524996, 0.210462);
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    t_action.assign({2.5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    pose1 = arr_give(0.261304, -0.0777537, -0.327505, 0.352001, -1.30391, -0.098619, -0.141385);


    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose1);
    t_action.assign({1, 1.5});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    //左手松开
    // dataPub.leftHand({999, 999, 999, 999, 999, 10});

    //左臂保持当前位置
    qp_grasp.clear();
    qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose0);
    t_action.assign({4});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);


    pose2 = arr_give(0.729791, -0.0745419, -0.339585, 0.345146, -1.34562, -0.0952359, -0.0188852);
    // pose2 = arr_give(0.488381, -0.408806, 1.08965, 0.907589, -0.752775, 0.674284, -0.13957);

    //从下面打
    // pose1 = arr_give(0.28719, -0.283211, -0.426015, 0.617955, -1.15553, 0.0466659, -0.233323);
    // pose2 = arr_give(0.581379, -0.164615, -0.432678, 0.52304, -1.05863, 0.0884317, 0.242851);

    //握拍姿势
    // 0.52232, -0.175833, 0.0738708, 0.512829, -0.147634, 0.452472, 0.0368583

    qp_grasp.clear();
    // qp_grasp.push_back(r_jnt_init);
    // qp_grasp.push_back(pose0);
    qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose2);
    t_action.assign({1});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}
