#include "robot_control/robot_control.h"
#include <std_msgs/Bool.h>

int flag_start = 0;

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
    // detect_flag = true;
    
    // for (double value : msg->data) {
    //     container.push_back(value);
    //     // cout << "apple: " << value << endl;
    // }
}

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_position_l[i] = msg->position[i+7];
        jnt_position_r[i] = msg->position[i];
    }

    return;
}

// 订阅回调函数
void servoCallback(const std_msgs::Bool::ConstPtr &msg) {
    if (msg->data) {
        flag_start = 1;
    }
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "four_initial");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link4","r_arm_Link4");

    ros::NodeHandle nh;
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    // ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber sub = nh.subscribe("joint_states", 1000, jointStateCallback);
    ros::Subscriber sub_servo = nh.subscribe("/servo_command", 1, servoCallback);

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //set init position
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Arm action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 2;
    double t2 = 4;
    double t3 = 6;
    istest = 0;
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    //获取初始位置
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, r_jnt_init, 4);
    } 

    pose0 = arr_give(0,-M_PI*8/180,0,0,0,0,0);
    // pose0 = arr_give(0.63, -0.42, -0.18, 1.02, 0.0, 0.0, 0.0);
    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0);

    t_action.assign({3});
    action = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到初始位置.....");
    robot.move(action, ctrl_mode, 4);
    ROS_INFO("运动完成!");

    return 0;
}