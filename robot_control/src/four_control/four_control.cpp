#include "robot_control/robot_control.h"
#include <std_msgs/Bool.h>

int flag_start = 1;

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
    // detect_flag = true;
    
    // for (double value : msg->data) {
    //     container.push_back(value);
    //     // cout << "apple: " << value << endl;
    // }
}

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_position_l[i] = msg->position[i+7];
        jnt_position_r[i] = msg->position[i];
    }

    // for(int i = 0; i < 7; i++){
    //     if (jnt_position_l[i] < theta_limit(i,0) || jnt_position_l[i] > theta_limit(i,1)){
    //         ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
    //         array_cout("jnt_position_l", jnt_position_l);
    //         ros::shutdown();
    //         exit(0);
    //     }
    //     if (jnt_position_r[i] < theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
    //         ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
    //         array_cout("jnt_position_r", jnt_position_r);
    //         ros::shutdown();
    //         exit(0);
    //     }
    // }

    return;
}

Eigen::Vector3d admittance_ctrl_REI_classical(Eigen::Vector3d fd, Eigen::Vector3d fe, double dt)
{
    static Eigen::Vector3d x = Eigen::MatrixXd::Zero(3,1);
    static Eigen::Vector3d dx = Eigen::MatrixXd::Zero(3,1);
    static Eigen::Vector3d sum = Eigen::MatrixXd::Zero(3,1);
    Eigen::Vector3d Me = {10, 10, 10};
    Eigen::Vector3d Be = {20, 20, 20};//650
    Eigen::Vector3d ddx;

    double ki = 0.00000000000000001;  // 0.005727;//0.4275 
    sum = sum + (fe - fd) * dt;
    ddx = (fe - fd + ki * sum) - Be.cwiseProduct(dx).cwiseQuotient(Me);
    // std::cout<<"ddxe"<<std::endl<<ddx(2)<<std::endl;
    dx = dx + ddx * dt;
    // std::cout<<"dxe"<<std::endl<<dx(2)<<std::endl;
    x = x + dx * dt;
    // std::cout<<"xe"<<std::endl<<x(2)<<std::endl;
    return x;
}

// // 函数：计算雅可比矩阵
// MatrixXd computejacobian(KDL::Chain chain, VectorXd jnt_pos_current) {
//     int n = chain.getNrOfJoints();
//     KDL::JntArray joint_angles(n);
//     for (size_t i = 0; i < n; i++) {
//         joint_angles(i) = jnt_pos_current[i];
//     }
//     // 创建用于存储雅可比矩阵的 KDL::Jacobian 对象
//     KDL::Jacobian jacobian(n);

//     // 创建正向运动学求解器
//     KDL::ChainJntToJacSolver jnt_to_jac_solver(chain);

//     // 计算雅可比矩阵
//     jnt_to_jac_solver.JntToJac(joint_angles, jacobian);

//     MatrixXd jacobian_matrix = jacobian.data;

//     return jacobian_matrix;
    
// }

// 利用Eigen库，采用SVD分解的方法求解矩阵伪逆，默认误差er为0
Eigen::MatrixXd pinv_eigen_based(Eigen::MatrixXd & origin, const float er = 0) {
    // 进行svd分解
    Eigen::JacobiSVD<Eigen::MatrixXd> svd_holder(origin,
                                                 Eigen::ComputeThinU |
                                                 Eigen::ComputeThinV);
    // 构建SVD分解结果
    Eigen::MatrixXd U = svd_holder.matrixU();
    Eigen::MatrixXd V = svd_holder.matrixV();
    Eigen::MatrixXd D = svd_holder.singularValues();

    // 构建S矩阵
    Eigen::MatrixXd S(V.cols(), U.cols());
    S.setZero();

    for (unsigned int i = 0; i < D.size(); ++i) {

        if (D(i, 0) > er) {
            S(i, i) = 1 / D(i, 0);
        } else {
            S(i, i) = 0;
        }
    }

    // pinv_matrix = V * S * U^T
    return V * S * U.transpose();
}

// 订阅回调函数
void servoCallback(const std_msgs::Bool::ConstPtr &msg) {
    if (msg->data) {
        flag_start = 1;
    }
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "four");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link4","r_arm_Link4");

    ros::NodeHandle nh;
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    // ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber sub = nh.subscribe("joint_states", 1000, jointStateCallback);
    ros::Subscriber sub_servo = nh.subscribe("/servo_command", 1, servoCallback);

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //set init position
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*8/180,0,0,0,0,0};

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 2;
    double t2 = 4;
    double t3 = 6;
    istest = 0;
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    //获取初始位置
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, r_jnt_init, 4);
    } 

    //左臂保持当前位置
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(l_jnt_init);
    t_action.assign({100});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    pose0 = arr_give(0.63, -0.42, -0.18, 1.02, 0.0, 0.0, 0.0);
    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(pose0);

    t_action.assign({3});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到初始位置.....");
    robot.move(action.right, ctrl_mode, 4);
    ROS_INFO("运动完成!");
    // return 0;
    ros::Publisher pub_motor, pub_rviz;
    pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);

    Eigen::Vector3d pp;
    Eigen::Vector3d pp_old;
    Eigen::Vector3d Fe;
    Eigen::Vector3d Fd;
    Eigen::Matrix<double, 4, 1> qd_dn;
    Eigen::MatrixXd dxx(6,1) ;
    pp = {0, 0, 0};
    pp_old = {0, 0, 0};
    Fe = {0, 0, 0};
    Fd = {0, 0, 0};
    double ss = 0.08;
    int mm = 0;
    int flag = 0;
    double ppp = ss;
    Fe = {0, 0, 0};
    Fd = {-1000, 0, 0};
    double allt = 0.0;
    while(ros::ok()){
        if(flag_start == 1){
            while (ros::ok())
            {
                MatrixXd jacobian = robot.computejacobian(jnt_position_r, right_arm);
                
                pp = admittance_ctrl_REI_classical(Fe, Fd, 0.001)/30;   //30
                // cout << pp <<endl;
                pp = {pp[0], 0, 0};

                if (pp[0] > ss && flag == 0)
                { 
                    Fd = -Fd ;
                    flag = 1;
                    // pose1 = jnt_position_r;
                    // pp = {0.2, 0, 0};
                    // break;
                }
                if (pp[0] < -1 * ss && flag == 0)
                { 
                    Fd = -Fd ;
                    flag = 2;
                    // pose1 = jnt_position_r;
                    // pp = {-0.2, 0, 0};
                    // break;
                }

                if (flag == 1 )
                {
                    if (pp[0] < 0){
                        pp[0] = 0;
                    }
                    if (abs(qd_dn[0])<0.01)
                    {
                        break;
                    }
                    
                }
                if (flag == 2 )
                {
                    if (pp[0] > 0){
                        pp[0] = 0;
                    }
                    if (abs(qd_dn[0])<0.01)
                    {
                        break;
                    }
                }

                dxx(0,0) = (pp[0]-pp_old[0])/0.001;
                pp_old[0] = pp[0];
                dxx(1,0) = 0;
                dxx(2,0) = 0;
                dxx(3,0) = 0;
                dxx(4,0) = 0;
                dxx(5,0) = 0;
                qd_dn = pinv_eigen_based(jacobian)*dxx;

                // cout << qd_dn <<endl;

                for (size_t j = 0; j < 4; j++)
                {
                    pose0[j] = pose0[j]+qd_dn[j]*0.001;
                }

                sensor_msgs::JointState jnt_state_msgs_rviz;
                int num_jnt = 4;
                std_msgs::Float64MultiArray jnt_state_msgs;
                jnt_state_msgs.data.resize(8);
                int num = 4;
                ros::Rate rate(1000);

                if(ctrl_mode == robot_ctrl){
                    for(int j = 0; j < num; j++){
                        jnt_state_msgs.data[j] = pose0[j];
                        jnt_state_msgs.data[j+num] = 0;
                    }
                    pub_motor.publish(jnt_state_msgs);
                }

                jnt_state_msgs_rviz.header.stamp = ros::Time::now();
                jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

                jnt_state_msgs_rviz.name.resize(num_jnt);
                jnt_state_msgs_rviz.position.resize(num_jnt);
                jnt_state_msgs_rviz.velocity.resize(num_jnt);
                
                for(int j = 0; j < num; j++){
                    jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
                    jnt_state_msgs_rviz.position[j] = pose0[j];
                    jnt_state_msgs_rviz.velocity[j] = 0;
                    // jnt_state_msgs_rviz.name[j+num] = "l_arm_Joint" + to_string(j+1);
                    // jnt_state_msgs_rviz.position[j+num] = l_jnt_init[j];
                    // jnt_state_msgs_rviz.velocity[j+num] = 0;
                    // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

                }
                // jnt_state_msgs.position[0] = 0.2;
                pub_rviz.publish(jnt_state_msgs_rviz);

                rate.sleep();
                allt += 0.001;
            
            }

            //左臂保持当前位置
            qp_grasp.clear();
            qp_grasp.push_back(l_jnt_init);
            qp_grasp.push_back(l_jnt_init);
            t_action.assign({100});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

            pose2 = arr_give(0.63, -0.42, -0.18, 1.02, 0.0, 0.0, 0.0);
            qp_grasp.clear();
            qp_grasp.push_back(pose0);
            qp_grasp.push_back(pose2);

            t_action.assign({allt*2});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

            ROS_INFO("运动到初始位置.....");
            robot.move(action.right, ctrl_mode, 4);
            ROS_INFO("运动完成!");

            break;
        }

    }
    //直线规划
    // robot.kdl_fk(jnt_init, pose, right_arm);
    // VectorXd pose_start(6), pose_end(6);
    // pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
    // vec << 0.330001, -0.3804, -0.158852;
    // rpy << 0, 0, M_PI/6;
    // pose_end << vec[0],vec[1],vec[2],rpy[0],rpy[1],rpy[2];
    // // pose_end << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
    
    // action = robot.line_plan(jnt_init, pose_start, pose_end, 10, right_arm);

    // ROS_INFO("运动到抓取位置.....");
    // robot.move(action, ctrl_mode);
    // ROS_INFO("运动完成!");

    return 0;
}