#include "robot_control/robot_control.h"

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort[i] = msg->effort[i];
        jnt_position[i] = msg->position[i];
    }
    //关节位置校验
    MatrixXd theta_limit(7,2);
    theta_limit <<  -M_PI, M_PI,
                    -M_PI, M_PI*5/180,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI;
    //电流校验
    MatrixXd effort_limit(7,2);
    effort_limit <<  -10, 10,
                    -10, 10,
                    -10, 10,
                    -10, 10,
                    -10, 10,
                    -10, 10,
                    -10, 10;
    for(int i = 0; i < 7; i++){
        if (jnt_position[i] < theta_limit(i,0) || jnt_position[i] > theta_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit position : ");
            array_cout("jnt_position", jnt_position);
            ros::shutdown();
            exit(0);
        }

        if (jnt_effort[i] < effort_limit(i,0) || jnt_effort[i] > effort_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit effort : ");
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }

    }

    return;
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "game_hand");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7","r_arm_Link7_tool");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);

    //启用多线程
 	ros::AsyncSpinner spinner(2);
 	spinner.start(); //开始标志

    //set init position
    array<double, 7> jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Arm action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 3;
    double t2 = 5;
    double t3 = 7;
    istest = 0;
    ctrl_mode = robot_ctrl;
    //获取初始位置
    if(istest == 1){
    }else{
        robot.getCurrentState(JointState);
        cout<<JointState<<endl;
        Getpos(JointState, jnt_init);
    } 
    // q0 = arr_give(0.229291, -1.35041, 1.2741, -1.53506, -1.51848, 0.0710284, 0.610088 );
    q1 = arr_give(0,-5*M_PI/180,0,0,0,0,0);
    qp_grasp.clear();
    qp_grasp.push_back(jnt_init);
    // qp_grasp.push_back(q0);
    qp_grasp.push_back(q1);
    // t_action.assign({t2, t1});
    t_action.assign({7});
    action = robot.multi_joint_plan(qp_grasp, t_action, dt);
    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    return 0;
}
