#include "robot_control/robot_control.h"

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    // if (container.size() > 0) container.clear();
    detect_flag = true;
    if (msg->data.size() >= 5)
    {
        int n = msg->data.size();
        // cout << "n: " << n << endl;
        for (size_t i = 0; i < n; i++)
        {
            container[i] = msg->data[i];
        }
    }
    // cout << container.size() <<endl;
}

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort[i] = msg->effort[i];
        jnt_position[i] = msg->position[i];
    }
    //关节位置校验
    MatrixXd theta_limit(7,2);
    theta_limit <<  -M_PI, M_PI,
                    -M_PI, M_PI*5/180,
                    -M_PI, M_PI,
                    -1, 130*M_PI/180,
                    -M_PI, M_PI,
                    -M_PI, M_PI,
                    -M_PI, M_PI;
    //电流校验
    MatrixXd effort_limit(7,2);
    effort_limit <<  -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15,
                    -15, 15;
    for(int i = 0; i < 7; i++){
        if (jnt_position[i] < theta_limit(i,0) || jnt_position[i] > theta_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit position : ");
            array_cout("jnt_position", jnt_position);
            ros::shutdown();
            exit(0);
        }

        if (jnt_effort[i] < effort_limit(i,0) || jnt_effort[i] > effort_limit(i,1)){
            ROS_ERROR("Error:  arm exceeds the limit effort : ");
            array_cout("jnt_effort_l", jnt_effort);
            ros::shutdown();
            exit(0);
        }

    }

    return;
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "game_hand");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7","r_arm_Link7_tool");
    ros::NodeHandle nh;
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    sleep(2);

    //set init position
    array<double, 7> jnt_init = {0,-M_PI*5/180,0,0,0,0,0};

    //机械臂状态
    sensor_msgs::JointState JointState;

    // 轨迹点集合
    Plan_Res_Arm action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;
    VectorXd pose_start(6), pose_end(6);
    int kr;
    Matrix3d r_c;
    Matrix3d r_d;
    Vector3d ypr;
    double thetax = 0;
    double thetax2 = 0;
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    array<double, 80> container2;
    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    //相机
    VectorXd camera_data;
    camera_data.resize(5);

    //规划时间
    double t1 = 2;
    double t2 = 3.9;
    double t3 = 3.9;
    istest = 0;
    vector<int> finis;
    ctrl_mode = robot_ctrl;
    //获取初始位置
    // if(istest == 1){
    // }else{
    //     robot.getCurrentState(JointState);
    //     Getpos(JointState, jnt_init);
    // } 

    int mm = 0;
#if 1
    //循环抓取
    for (size_t ii = mm; ii < 1; ii++)
    {
        handCtrl.rightHand({999,999,999,999,550,10});
        camera_data[4] = -1;
        // 获取相机数据 先抓水杯，再从左往右抓
        for (size_t i = 0; i < 80; i++)
        {
            container[i] = -100;
            container2[i] = -100;
        }
        int b_flag = 0;
        while (ros::ok() && ii == 0 && !b_flag)
        {
            detect_flag = false;
            while (!detect_flag){
                ROS_INFO("等待电池数据.....");
                sleep(0.1);
            }
            for (size_t j = 0; j < 80; j++)
            {
                container2[j] = container[j];
                if(container2[j] == 1){
                    b_flag = 1;
                    for (size_t i = 0; i < 5; i++)
                    {
                        camera_data[i] = container2[j-4+i];
                    }
                    break;
                }  
                           
            }
        }

        if (camera_data[4] == -1)
        {
            continue;
            // ROS_INFO("分拣完成.....");
            // ros::shutdown();
            // exit(0);
        }

        cout << "camera_data: " << camera_data << endl;

        if (ii == mm){
            //获取当前位置
            if(istest == 1){
            }else{
                robot.getCurrentState(JointState);
                Getpos(JointState, jnt_init);
            }
            pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
            if(qp_grasp.size() > 0) qp_grasp.clear();
            qp_grasp.push_back(jnt_init);
            qp_grasp.push_back(pose1);
            t_action.assign({t2});
            action = robot.multi_joint_plan(qp_grasp, t_action, dt);
            ROS_INFO("运动到放置位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");

        }


        if (camera_data[3] < -0.17 && camera_data[3] > -M_PI/4)
        {
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
               

                thetax = -90*M_PI/180;
                thetax2 = 45*M_PI/180;
                // thetax3 = 45*M_PI/180;
                rpy << -0.0472663, 0.0783188, 0.00431417;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax2, 0, 0) * rpy2rotationMatrix(0, 0, thetax) * r_c * rpy2rotationMatrix(45*M_PI/180, 0, 0);

                // // rpy << -1.62239, 0.466443, 0;
                // r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                // r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];

                //固定高度
                camera_data[2] = -0.53 ;
                vec << camera_data[0]-0.023-0.02, camera_data[1]-0.022-0.063, camera_data[2] + 0.2 -0.112;
                // rpy << -1.62239, 0.466443, 0;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({999,999,999,999,550,10});

                pose_start = pose_end;
                pose_end[0] -= 0.08;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                pose_start = pose_end;
                pose_end[2] += 0.08;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);                
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
        }
        

        // sleep(5);
        // continue;
        int mm = round(camera_data[4]);
        switch(mm){
            case 0:
                ROS_INFO("球");
                finis.push_back(0);
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                // thetax = 0*M_PI/180;
                // rpy << -1.70669, 0.469318, 0.535212;
                // r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                // r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c;
                // ypr = r_d.eulerAngles(2, 1, 0);
                // rpy << ypr[2], ypr[1], ypr[0];

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                //固定高度
                camera_data[2] =  -0.53;  
                vec << camera_data[0] - 0.019, camera_data[1] - 0.026, camera_data[2] + 0.162;
                rpy << -1.70669, 0.469318, 0.535212;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

               if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("gg.....");
                }

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({200,200,800,800,700,300});

                pose_start = pose_end;
                pose_end[2] -= 0.063;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
// #if 1
                //手抓
                handCtrl.rightHand({200,200,500,500,600,300});
#if 1
                //放
                pose_start = pose_end;
                pose_end[2] += 0.063;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

            //    if (pose_end[1] > -0.16)
            //    {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.18;
                    pose_end[2] += 0.03;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
            //    }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                vec << pose_end[0] - 0.08, pose_end[1] -0.2 , pose_end[2] + 0.05;
                robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);

                pose2 = arr_give(-0.77,-0.12,0.83,1.10,-0.83,0.27,-0.30);
                vec << -0.0169151, -0.536316 - 0.05, -0.473568 + 0.1;
                rpy << 1.64864, 2.67622, 2.15223;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3, t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif

                break;
            case 1: 
                ROS_INFO("电池");
                finis.push_back(1);
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
               
                //旋转

                if (camera_data[3] >= 0){
                    camera_data[3] =  (  M_PI/2 - camera_data[3]);
                }else{
                    camera_data[3] = (  -M_PI/2 -camera_data[3]);
                }
                if (camera_data[3] >= 60*M_PI/180){
                    camera_data[3] = 60*M_PI/180;
                }else if (camera_data[3] <= -60*M_PI/180)
                {
                    camera_data[3] = -60*M_PI/180;
                }else if (camera_data[3] <= 0&&camera_data[3] > -70*M_PI/180)
                {
                    camera_data[3] = 0;
                }



                
                thetax = abs(camera_data[3]);
                cout<<"thetax: "<<thetax<<endl;
                // thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];

                //固定高度
                camera_data[2] = -0.53 - 0.001* (60*M_PI/180 -thetax)/(60*M_PI/180);
                camera_data[0] -= 0.015* (60*M_PI/180 -thetax)/(60*M_PI/180);
                vec << camera_data[0]-0.023, camera_data[1]-0.022, camera_data[2] + 0.116;
                // rpy << -1.62239, 0.466443, 0;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({750,850,850,999,550,10});



                pose_start = pose_end;
                pose_end[2] -= 0.025;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("gg.....");
                }
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手抓
                handCtrl.rightHand({999,850,850,600,550,10}); //600

                //放
                pose_start = pose_end;
                pose_end[2] += 0.06;
                thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
                pose_end[3] = rpy[0];
                pose_end[4] = rpy[1];
                pose_end[5] = rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

            //    if (pose_end[1] > -0.186)
            //    {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.15;
                    pose_end[2] += 0.03;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
            //    }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                pose1 = pose0;
                pose1[6] -= 39*M_PI/180;
                vec << 0.04487, -0.0977 , pose_end[2] - 0.03 ;
                thetax = 60*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(0, -30*M_PI/180, 0) * rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
                robot.kdl_ik(jnt_init, vec, rpy, pose2, right_arm);

                // pose2 = arr_give(-0.77,-0.12,0.83,1.10,-0.83,0.27,-0.30);
                // vec << -0.0169151, -0.536316, -0.473568 + 0.1;
                // rpy << 1.64864, 2.67622, 2.15223;
                // robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t1,t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                robot.kdl_fk(pose2, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                pose_end = pose_start;
                pose_end[2] -= 0.033;
                pose_end[0] += 0.005; //0.005
                pose_end[1] -= 0.02;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({750,850,850,999,550,10});  

                // pose_start = pose_end;
                // pose_end[0] = 0.04487;
                // pose_end[1] = -0.0977;

                // thetax = 0*M_PI/180;
                // rpy << -1.62239, 0.466443, 0;
                // r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                // r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                // ypr = r_d.eulerAngles(2, 1, 0);
                // rpy << ypr[2], ypr[1], ypr[0];
                // pose_end[3] = rpy[0];
                // pose_end[4] = rpy[1];
                // pose_end[5] = rpy[2];
                // // pose_end[2] += 0.03;
                // action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                // ROS_INFO("抬起来.....");
                // robot.move(action, ctrl_mode);
                // ROS_INFO("运动完成!");

#if 0
                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                vec << pose_end[0] - 0.08, pose_end[1] -0.2 , pose_end[2] + 0.05;
                vec << pose_end[0] - 0.08, pose_end[1] -0.1 , pose_end[2] + 0.05;
                robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);
                // vec << 0.330001, -0.3804, -0.158852;
                // // rpy << 0, 0, M_PI/6;
                // robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);

                pose2 = arr_give(-0.77,-0.12,0.83,1.10,-0.83,0.27,-0.30);
                vec << -0.0169151, -0.536316, -0.473568 + 0.1;
                rpy << 1.64864, 2.67622, 2.15223;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3, t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif
                break;
            case 2:
                ROS_INFO("杯子");
                finis.push_back(2);
                //手预备抓
                handCtrl.rightHand({999,999,999,999,999,10});
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }
                
                thetax = -90*M_PI/180;
                thetax2 = 45*M_PI/180;
                // thetax3 = 45*M_PI/180;

                rpy << -0.0472663, 0.0783188, 0.00431417;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax2, 0, 0) * rpy2rotationMatrix(0, 0, thetax) * r_c * rpy2rotationMatrix(45*M_PI/180, 0, 0);
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];

                if (camera_data[3] < -15*M_PI/180)
                {
                    rpy << -1.63836,  0.788562, 0;
                }
                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                //固定高度
                camera_data[2] = -0.53;
                vec << camera_data[0] - 0.04, camera_data[1] - 0.02 , camera_data[2] + 0.173;
                // rpy << -1.5961, 0.266632, 0.119273;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

                // cout << "pose_end: " << pose_end << endl;
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");


#if 1
                //手预备抓
                // handCtrl.rightHand({999,999,999,999,999,10});
                pose_start = pose_end;
                pose_end[0] += 0;
                pose_end[1] += 0;
                pose_end[2] -= 0.07;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
// #if 1
                //手抓
                handCtrl.rightHand({700, 750, 700, 700, 600, 10});

                //放
                pose_start = pose_end;
                pose_end[2] += 0.06;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

               if (pose_end[1] < -0.15)
               {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.15;
                    pose_end[2] += 0.1;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
               }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                // vec << pose_end[0] - 0.08, pose_end[1] + 0.05 , pose_end[2] + 0.05;
                // robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);
                pose1 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981, 0.0933509 + 0.0, -0.445982 + 0.13;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose1, vec, rpy, pose1, right_arm);

                pose2 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981 - 0.02, 0.0933509 + 0.05, -0.445982 + 0.08;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3,t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose2 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t2,t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif
                break;
            case 3:
                ROS_INFO("鸡蛋");
                finis.push_back(3);                
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                thetax = 45*M_PI/180;

                // if (camera_data[1] < -0.28 && camera_data[0] > 0.3)
                // {
                //     thetax = 0*M_PI/180;
                // }
                

                thetax2 = 10*M_PI/180;
                rpy << -1.56805, 0.27223, 0.0625683;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c * rpy2rotationMatrix(thetax2, 0, 0);
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                //固定高度
                camera_data[2] = -0.53;
                vec << camera_data[0] - 0.015, camera_data[1] + 0.005 , camera_data[2] + 0.165;
                // rpy << -1.5961, 0.266632, 0.119273;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

                if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("鸡蛋gg.....");
                }
                

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({900,900,900,900,999,10});

                pose_start = pose_end;
                pose_end[0] += 0;
                pose_end[1] += 0;
                pose_end[2] -= 0.05;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手抓
                handCtrl.rightHand({900, 550, 650, 650, 700, 10});
#if 1
                //放
                pose_start = pose_end;
                pose_end[0] -= 0.05;
                pose_end[2] += 0.06;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

               if (pose_end[1] < -0.15)
               {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.15;
                    pose_end[2] += 0.03;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
               }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                kr = robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);
                
                // vec << pose_end[0] - 0.08, pose_end[1] + 0.05 , pose_end[2] + 0.05;
                // robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);
                pose1 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981, 0.0933509 -0.03, -0.445982 + 0.13;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose1, vec, rpy, pose1, right_arm);

                pose2 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981, 0.0933509 -0.03, -0.445982 + 0.05;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3,t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose2 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t2,t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif

                break;
            case 4:
                break;
            case 5:
                break;
            case 6:
                ROS_INFO("丝巾");
                finis.push_back(6);
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                thetax = 45*M_PI/180;
                thetax2 = 10*M_PI/180;
                rpy << -1.62472, 0.257253, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c *rpy2rotationMatrix(thetax2, 0, 0);
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
                cout<<"rpy: "<<rpy[0]<<" "<<rpy[1]<<" "<<rpy[2]<<endl;
                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                //固定高度
                camera_data[2] = -0.53;
                camera_data[2] += -(camera_data[1] + 0.09)/100;
                vec << camera_data[0] -0.06, camera_data[1] - 0.055 , camera_data[2] + 0.1616; 
                // rpy << -1.62472, 0.257253, 0;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);
               if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("gg.....");
                }
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({200,200,200,999,550,100});
#if 1
                pose_start = pose_end;
                pose_end[0] -= 0.01;
                pose_end[1] -= 0.01;
                pose_end[2] -= 0.072;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                pose_start = pose_end;
                pose_end[0] += 0.03;
                pose_end[1] += 0.03;
                // pose_end[2] -= 0.072;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手抓
                handCtrl.rightHand({200,200,200,350,550,100});
                //放
                pose_start = pose_end;
                pose_end[0] -= 0.05;
                pose_end[2] += 0.1;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);
                pose1 = pose0;
                pose1[6]-=60*M_PI/180;
                pose1[5]+=30*M_PI/180;
                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                // qp_grasp.push_back(pose2);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                // vec << pose_end[0], pose_end[1], pose_end[2];
                // rpy << pose_end[3], pose_end[4], pose_end[5];
                // robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);
                pose0 = pose1;
                pose1 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981 - 0.02, 0.0933509 + 0.01, -0.445982 + 0.13;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose1, vec, rpy, pose1, right_arm);

                // pose2 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                // vec << -0.00918981, 0.0933509 -0.03, -0.445982 + 0.05;
                // rpy << -1.5977, 0.14277, 1.60321;
                // robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                // qp_grasp.push_back(pose2);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose1;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                // qp_grasp.push_back(pose2);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif

                break;
            case 7:
                ROS_INFO("钉子");
                finis.push_back(7);
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
               
                //旋转

                if (camera_data[3] >= 0){
                    camera_data[3] =  (  M_PI/2 - camera_data[3]);
                }else{
                    camera_data[3] = (  -M_PI/2 -camera_data[3]);
                }
                if (camera_data[3] >= 60*M_PI/180){
                    camera_data[3] = 60*M_PI/180;
                }else if (camera_data[3] <= -60*M_PI/180)
                {
                    camera_data[3] = -60*M_PI/180;
                }else if (camera_data[3] <= 0&&camera_data[3] > -76*M_PI/180)
                {
                    camera_data[3] = 0;
                }

                // thetax = -0.23;
                // if (camera_data[3] >= 0){
                //     camera_data[3] =  (  M_PI/2 - camera_data[3]);
                // }else{
                //     camera_data[3] = (  -M_PI/2 -camera_data[3]);
                // }
                // if (camera_data[3] >= 60*M_PI/180) camera_data[3] = 60*M_PI/180;
                // if (camera_data[3] <= -60*M_PI/180) camera_data[3] = -60*M_PI/180;
                
                thetax = abs(camera_data[3]);
                thetax2 = -10*M_PI/180;
                cout<<"thetax: "<<thetax<<endl;
                // thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c * rpy2rotationMatrix(thetax2, 0, 0);
                ypr = r_d.eulerAngles(2, 1, 0); 
                rpy << ypr[2], ypr[1], ypr[0];

                //固定高度
                camera_data[2] = -0.53 - 0.002* (60*M_PI/180 -thetax)/(60*M_PI/180);
                camera_data[1] -= 0.005* (thetax)/(60*M_PI/180);
                camera_data[0] +=  0.007* (60*M_PI/180 -thetax)/(60*M_PI/180);
                vec << camera_data[0]-0.032 -0.000, camera_data[1] - 0.01, camera_data[2] + 0.1375;
                // rpy << -1.62239, 0.466443, 0;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

               if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("gg.....");
                }

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({999,999,999,999,550,10});

                pose_start = pose_end;
                pose_end[2] -= 0.039;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#if 1
                //手抓
                handCtrl.rightHand({999,500,500,500,550,10}); //600

                //放
                pose_start = pose_end;
                pose_end[2] += 0.039;

                thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
                pose_end[3] = rpy[0];
                pose_end[4] = rpy[1];
                pose_end[5] = rpy[2];

                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

            //    if (pose_end[1] > -0.18)
            //    {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.22;
                    pose_end[2] += 0.03;

                thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c ;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
                pose_end[3] = rpy[0];
                pose_end[4] = rpy[1];
                pose_end[5] = rpy[2];

                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
            //    }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                vec << pose_end[0] - 0.08, pose_end[1] -0.2 , pose_end[2] + 0.05;
                vec << pose_end[0] - 0.08, pose_end[1] -0.1 , pose_end[2] + 0.05;
                robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);
                // vec << 0.330001, -0.3804, -0.158852;
                // // rpy << 0, 0, M_PI/6;
                // robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);

                pose2 = arr_give(-0.77,-0.12,0.83,1.10,-0.83,0.27,-0.30);
                vec << -0.0169151, -0.536316, -0.473568 + 0.1;
                rpy << 1.64864, 2.67622, 2.15223;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3, t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif

                break;
            case 8:
                ROS_INFO("螺丝刀");
                finis.push_back(8);
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                thetax = 45*M_PI/180;
                rpy << -1.5961, 0.266632, 0.119273;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c;
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];
            
                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                //固定高度
                camera_data[2] = -0.53;
                camera_data[2] += -(camera_data[1] + 0.09)/100;

                if (camera_data[3] < -0.3 || camera_data[3] > M_PI/4)
                {
                    rpy << -1.5961, 0.266632, 0.119273;
                    camera_data[0] += 0.02;
                    camera_data[1] += 0.01;
                    camera_data[2] += 0.001;
                }

                vec << camera_data[0] - 0.05, camera_data[1] + 0.00, camera_data[2] + 0.1315;

                // rpy << -1.5961, 0.266632, 0.119273;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                handCtrl.rightHand({999,999,900,900,550,10});
                pose_start = pose_end;
                pose_end[2] -= 0.035;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#if 1
                //手抓
                handCtrl.rightHand({500,500,450,500,550,10});

                //放
                pose_start = pose_end;
                pose_end[2] += 0.039;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手抓
                // handCtrl.rightHand({100,100,450,500,550,10});

            //    if (pose_end[1] > -0.18)
            //    {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.22;
                    pose_end[2] += 0.05;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
            //    }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                vec << pose_end[0] - 0.08, pose_end[1] -0.2 , pose_end[2] + 0.05;
                robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);

                pose2 = arr_give(-0.77,-0.12,0.83,1.10,-0.83,0.27,-0.30);
                vec << -0.0169151 - 0.08, -0.536316, -0.473568 + 0.1;
                rpy << 1.64864, 2.67622, 2.15223;
                robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                qp_grasp.push_back(pose2);
                t_action.assign({t3, t2});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose2;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

#endif
                break; 

            case 9:
                ROS_INFO("💊");
                finis.push_back(9);
                handCtrl.rightHand({200,200,200,800,550,100});
                //获取当前位置
                if(istest == 1){
                }else{
                    robot.getCurrentState(JointState);
                    Getpos(JointState, jnt_init);
                }

                robot.kdl_fk(jnt_init, pose, right_arm);
                pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
               
                //旋转
                // thetax = -0.23;
                if (camera_data[3] >= 0){
                    camera_data[3] =  (  M_PI/2 - camera_data[3]);
                }else{
                    camera_data[3] = (  -M_PI/2 -camera_data[3]);
                }
                if (camera_data[3] >= 60*M_PI/180){
                    camera_data[3] = 60*M_PI/180;
                }else if (camera_data[3] <= -60*M_PI/180)
                {
                    camera_data[3] = -60*M_PI/180;
                }else if (camera_data[3] <= 0&&camera_data[3] > -70*M_PI/180)
                {
                    camera_data[3] = 0;
                }
                                
                thetax = abs(camera_data[3]);
                thetax2 = -10*M_PI/180;
                cout<<"thetax: "<<thetax<<endl;
                // thetax = 45*M_PI/180;
                rpy << -1.62239, 0.466443, 0;
                r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c * rpy2rotationMatrix(thetax2, 0, 0);
                ypr = r_d.eulerAngles(2, 1, 0);
                rpy << ypr[2], ypr[1], ypr[0];

                //固定高度
                camera_data[2] = -0.53 + 0.00* (60*M_PI/180 -thetax)/(60*M_PI/180);
                camera_data[0] += 0.005* (60*M_PI/180 -thetax)/(60*M_PI/180); 
                camera_data[1] -= 0.003* (60*M_PI/180 -thetax)/(60*M_PI/180);
                vec << camera_data[0]-0.018, camera_data[1]-0.013 - 0.005, camera_data[2] + 0.115; //106 
                // rpy << -1.62239, 0.466443, 0;
                pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);
               if (action.error_flag == 1)
                {
                    continue;
                    ROS_INFO("gg.....");
                }
                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                //手预备抓
                // handCtrl.rightHand({200,200,200,800,550,100});

                pose_start = pose_end;
                pose_end[2] -= 0.025;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("运动到抓取位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#if 1
                //手抓
                handCtrl.rightHand({200,200,200,500,550,100});

                //放
                pose_start = pose_end;
                pose_end[2] += 0.039;
                action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                ROS_INFO("抬起来.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

               if (pose_end[1] < -0.15)
               {
                    pose_start = pose_end;
                    pose_end[0] = 0.3;
                    pose_end[1] = -0.15;
                    pose_end[2] += 0.03;
                    action = robot.line_plan(jnt_init, pose_start, pose_end, t2, right_arm);

                    ROS_INFO("抬起来.....");
                    robot.move(action, ctrl_mode);
                    ROS_INFO("运动完成!");
               }

                vec << pose_end[0], pose_end[1], pose_end[2];
                rpy << pose_end[3], pose_end[4], pose_end[5];
                robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);

                // vec << pose_end[0] - 0.08, pose_end[1] + 0.05 , pose_end[2] + 0.05;
                // robot.kdl_ik(jnt_init, vec, rpy, pose1, right_arm);
                pose1 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                vec << -0.00918981 , 0.0933509 + 0.01, -0.445982 + 0.13;
                rpy << -1.5977, 0.14277, 1.60321;
                robot.kdl_ik(pose1, vec, rpy, pose1, right_arm);

                // pose2 = arr_give(0.08,-0.11,-1.6,1.19,-1.53,0.03,-0.35);
                // vec << -0.00918981, 0.0933509 -0.03, -0.445982 + 0.05;
                // rpy << -1.5977, 0.14277, 1.60321;
                // robot.kdl_ik(pose2, vec, rpy, pose2, right_arm);

                if(qp_grasp.size() > 0) qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                // qp_grasp.push_back(pose2);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到放置位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");

                handCtrl.rightHand({999,999,999,999,999,10});

                pose0 = pose1;
                pose1 = arr_give(-0.00929976, -1.21347, -0.627446, 1.50004, 1.18059, 0.619602, 0.0186);
                qp_grasp.clear();
                qp_grasp.push_back(pose0);
                qp_grasp.push_back(pose1);
                // qp_grasp.push_back(pose2);
                t_action.assign({t3});
                action = robot.multi_joint_plan(qp_grasp, t_action, dt);
                ROS_INFO("运动到初始位置.....");
                robot.move(action, ctrl_mode);
                ROS_INFO("运动完成!");
#endif
                break;                  
        }

    }
#endif


    return 0;
}


// #if 0
//     /*
//     抓杯子
//     */
//     //拿
//     q0 = arr_give(-0.38,-0.61,-0.82,0.99,0.56,1.17,-0.37);
//     q1 = arr_give(-0.08,-0.03,-0.14,0.63,-0.03,0.94,-0.16);
//     qp_grasp.clear();
//     qp_grasp.push_back(jnt_init);
//     qp_grasp.push_back(q0);
//     qp_grasp.push_back(q1);
//     t_action.assign({t2, t1});
//     action = robot.multi_joint_plan(qp_grasp, t_action, dt);
//     ROS_INFO("运动到抓取位置.....");
//     // robot.move(action, ctrl_mode);
//     ROS_INFO("运动完成!");

//     //手抓
//     handCtrl.rightHand({100,999,999,999,999,999});
//     handCtrl.rightHand();

//     //放
//     q0 = q1;
//     robot.kdl_fk(q0, pose, right_arm);
//     pose.position[0] -= 0.1;
//     pose.position[2] += 0.18;
//     robot.kdl_ik(q0, pose.position, pose.rpy, q1, right_arm);
//     q2 = arr_give(0.12,-0.15,-1.5,1.54,-1.59,0.25,0.03);
//     robot.kdl_fk(q2, pose, right_arm);
//     pose.position[0] -= 0.01;
//     pose.position[2] -= 0.08;
//     robot.kdl_ik(q0, pose.position, pose.rpy, q2, right_arm);
//     qp_grasp.clear();
//     qp_grasp.push_back(q0);
//     qp_grasp.push_back(q1);
//     qp_grasp.push_back(q2);
//     t_action.assign({t1, t2});
//     action = robot.multi_joint_plan(qp_grasp, t_action, dt);
//     ROS_INFO("运动到放置位置.....");
//     // robot.move(action, ctrl_mode);
//     ROS_INFO("运动完成!");

// #endif
    // //旋转
    // q0 = q1;
    // robot.kdl_fk(q1, pose, right_arm);
    // T = pose.T; 
    // T.block<3, 3>(0, 0) = rpy2rotationMatrix(PI_4, 0, 0) * pose.T.block<3, 3>(0, 0);
    // EigenMatrixToPose(T, pose);
    // robot.kdl_ik(q0, pose.position, pose.rpy, q1, right_arm);
    // qp_grasp.clear();
    // qp_grasp.push_back(q0);
    // qp_grasp.push_back(q1);
    // t_action.assign({t2});
    // action = robot.multi_joint_plan(qp_grasp, t_action, dt);
    // ROS_INFO("运动到放置位置.....");
    // robot.move(action, ctrl_mode);
    // ROS_INFO("运动完成!");

                //     //旋转
                // thetax = -0.23;
                // // if (camera_data[3] >= 0){
                // //     if (camera_data[3] >= 0.5) camera_data[3] = 0.5;
                // //     thetax +=  ( - camera_data[3]);
                // // }else{
                // //     if (camera_data[3] <= -0.5) camera_data[3] = -0.5;
                // //     thetax += ( - camera_data[3]);
                // // }
                // // thetax = -0.27+0.5;
                // cout<<"thetax: "<<thetax<<endl;
                // rpy << -1.62239, 0.466443, 0.23323;
                // // rpy << -1.62239, 0.466443, 0;
                // r_c = rpy2rotationMatrix(rpy[2], rpy[1], rpy[0]);
                // r_d = rpy2rotationMatrix(thetax, 0, 0) * r_c;
                // ypr = r_d.eulerAngles(2, 1, 0);
                // rpy << ypr[2], ypr[1], ypr[0];
                // rpy << -1.62239, 0.466443, 0;
                // cout << "rpy: " << rpy << endl;
                // // vec << camera_data[0]-0.035, camera_data[1]-0.033, camera_data[2] + 0.1;
                // // robot.kdl_ik(jnt_init, vec, rpy, pose0, right_arm);
                // // if(qp_grasp.size() > 0) qp_grasp.clear();
                // // qp_grasp.push_back(jnt_init);
                // // qp_grasp.push_back(pose0);
                // // t_action.assign({t3});
                // // action = robot.multi_joint_plan(qp_grasp, t_action, dt);

                // robot.kdl_fk(jnt_init, pose, right_arm);
                // pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
                // vec << camera_data[0]-0.035, camera_data[1]-0.033, camera_data[2] + 0.1;
                // pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
                // action = robot.line_plan(jnt_init, pose_start, pose_end, t3, right_arm);