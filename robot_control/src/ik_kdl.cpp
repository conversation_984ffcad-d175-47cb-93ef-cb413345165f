#include "robot_control/robot_control.h"

int main(int argc, char *argv[])
{
   ros::init(argc, argv, "ik_kdl");

   const char* urdf_path = getenv("URDF_PATH");
   ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7","r_arm_Link7_battery");

   // std::array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
   std::array<double, 7> j_init2{0,0,0,0,0,0,0 };
   std::array<double, 7> result;
   int kinematics_status;
   Vector3d vec, rpy;
   array<double, 7> q0, q1, q2, q3, q4, q5;
   array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
   array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
#if 0
   Eigen::Quaterniond quaternion(0.707, 0.0, 0.707, 0.0); 
   quaternion.normalize(); // 确保单位化
   // 将四元数转换为旋转矩阵
   Eigen::Matrix3d rotation_matrix = quaternion.toRotationMatrix();
   // 从旋转矩阵计算欧拉角
   Vector3d rpy = rotation_matrix.eulerAngles(0, 1, 2); //roll, pitch, yaw
#endif

   // vec << 0.35, -0.15, -0.13;
   // rpy << -M_PI_2, -M_PI_2, -M_PI; //roll, pitch, yaw
   // robot.kdl_ik(j_init, vec, rpy, result, left_arm);
   // array_cout("result", result);

   vec << 0.245574, -0.253655, -0.357;
   rpy << 1.50324, 2.35303, 2.23012; //roll, pitch, yaw
   robot.kdl_ik(j_init, vec, rpy, result, right_arm);
   array_cout("result", result);

   return 0;
}