#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"

// 定义颜色转义序列
#define RESET   "\033[0m"
#define GRE<PERSON>   "\033[32m"

int flag_hand = 0;

// 工作函数
void leftHandThread(DATA_PROC::Data_Pub* dataPub) {

    while(flag_hand == 0){
        usleep(100);
    }
    // usleep(5000);
    dataPub->leftHand({999,999,999,999,999,100},{999,999,999,999,999,999});
    ROS_INFO("%s手张开%s", GREEN, RESET);
}   

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "throw");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*5/180,0,0,0,0,0};
    array<double, 7> q0, q1, q2, q3, q4, q5;
    array<double, 7> pose0, pose1, pose2, pose3, pose4, pose5;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //键盘输入
    std::string input;

    DATA_PROC::Data_Pub dataPub(false);
    // sleep(5);
    // dataPub.leftHand({100,100,100,100,999,100});
    // return 0;
    dataPub.leftHand({999,999,999,999,999,999});

    // 启动线程
    std::thread t(leftHandThread, &dataPub);
    t.detach();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    } 
    // pose0 = arr_give(0.460, -0.502, -0.145, 1.256, -0.714, 0.531, -0.009);
    pose0 = arr_give(M_PI*220/180,-M_PI*0/180,0,M_PI*30/180,-M_PI*90/180,0,-M_PI*15/180);
    //左臂保持当前位置
    qp_grasp.push_back(l_jnt_init);
    qp_grasp.push_back(pose0);
    t_action.assign({5});
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({20});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");
    sleep(5);
    // return 0;
    // pose1 = arr_give(2,-M_PI*8/180,0,1.32,0,0.8,0);
    // pose2 = arr_give(1,-M_PI*8/180,0,1.88809,0,0,0);
    pose2 = arr_give(M_PI*166/180,-M_PI*0/180,0,-M_PI*25/180,-M_PI*90/180,0,M_PI*16/180);
    //左臂保持当前位置
    qp_grasp.clear();
    qp_grasp.push_back(pose0);
    // qp_grasp.push_back(pose1);
    qp_grasp.push_back(pose2);
    t_action.assign({0.5});//0.39
    action.left = robot.multi_joint_plan(qp_grasp, t_action, dt);

    qp_grasp.clear();
    qp_grasp.push_back(r_jnt_init);
    qp_grasp.push_back(r_jnt_init);
    t_action.assign({20});
    action.right = robot.multi_joint_plan(qp_grasp, t_action, dt);

    ROS_INFO("运动到目标位置.....");
    // flag_hand = 1;
    // usleep(110000);
    robot.move(action, ctrl_mode);
    ROS_INFO("运动完成!");

    sleep(1);

    return 0;
}