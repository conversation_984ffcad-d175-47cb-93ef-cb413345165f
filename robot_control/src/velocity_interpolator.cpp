#include "robot_control/robot_control.h"
#include "arms_gen2_control/Def_Class.h"
#include <std_msgs/Int32.h>

#include <std_msgs/Int32MultiArray.h>
#include <std_msgs/Float32MultiArray.h>

std::vector<double> target_vel_(14, 0.0);   // 初始化为4个0

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {

    for(int i = 0; i < 7; i++)
    {
        jnt_effort_l[i] = msg->effort[i];
        jnt_effort_r[i] = msg->effort[i+7];
        jnt_position_l[i] = msg->position[i];
        jnt_position_r[i] = msg->position[i+7];
    }

    // // cout<<theta_limit<<endl;
    // for(int i = 0; i < 7; i++){
    //     if (jnt_position_l[i] < theta_limit(i,0)-0.001 || jnt_position_l[i] > theta_limit(i,1)+0.001){
    //         ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
    //         array_cout("jnt_position_l", jnt_position_l);
    //         ros::shutdown();
    //         exit(0);
    //     }
    //     if (jnt_position_r[i] < target_vel_theta_limit(i,0) || jnt_position_r[i] > theta_limit(i,1)){
    //         ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
    //         array_cout("jnt_position_r", jnt_position_r);
    //         ros::shutdown();
    //         exit(0);
    //     }
    //     if (jnt_effort_l[i] < effort_limit(i,0) || jnt_effort_l[i] > effort_limit(i,1)){
    //         ROS_ERROR("Error: left arm joint %d exceeds the limit effort.", i + 1);
    //         array_cout("jnt_effort_l", jnt_effort);
    //         ros::shutdown();
    //         exit(0);
    //     }
    //     if (jnt_effort_r[i] < effort_limit(i,0) || jnt_effort_r[i] > effort_limit(i,1)){
    //         ROS_ERROR("Error: right arm joint %d exceeds the limit effort.", i + 1);
    //         array_cout("jnt_effort_l", jnt_effort);
    //         ros::shutdown();
    //         exit(0);
    //     }
    // }

    return;
}

void velocityCallback(const std_msgs::Float64MultiArray::ConstPtr& msg) {
    if(msg->data.size() == 14) {
        target_vel_ = msg->data;
    }
}

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "velocity_interpolator");
    setlocale(LC_ALL,"");
    const char* urdf_path = getenv("URDF_PATH3");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");
    ROBOT_CONTROL::robot_control robot2(urdf_path,"base_link","AL7","AR7");

    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber sub2 = nh.subscribe("/target_velocity", 1, velocityCallback);



    ros::Publisher pub_motor, pub_rviz;
    pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);

    theta_limit = robot.theta_limit;
    effort_limit = robot.effort_limit;

    //启用多线程
 	ros::AsyncSpinner spinner(3);
 	spinner.start(); //开始标志

    //键盘输入
    std::string input;

    CAMERA_READ::camera_read camera;
    std_msgs::Float64MultiArray camera_data, door_data;
    Vector4d vec_camera;
    Vector3d vec_door;

    /*
    set init position
    */ 
    //左臂
    array<double, 7> l_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    //右臂
    array<double, 7> r_jnt_init = {0,-M_PI*10/180,0,0,0,0,0};
    // array<double, 7> jnt_init = {1.41,-0.64,-1.48,0.64,1.45,0.04,0};
    array<double, 7> jnt_init = arr_give(-20*M_PI/180, 0*M_PI/180, 0*M_PI/180, 100*M_PI/180, -0*M_PI/180, 10*M_PI/180, 0*M_PI/180);
    
    array<double, 7> q0, q1, q2, q3, q4, q5;
    def_msgs::Pose pose_l, pose_r;
    array<double, 7> pose0;
    array<double, 7> pose0_l, pose1_l, pose2_l, pose3_l, pose4_l, pose5_l;
    array<double, 7> pose0_r, pose1_r, pose2_r, pose3_r, pose4_r, pose5_r;
    array<double, 7> j_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
    VectorXd pose_start_l(6), pose_end_l(6), pose_start_r(6), pose_end_r(6);
    Vector3d vec(3), rpy(3);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;
    INSPIRE_HAND::Pos_Ctrl dataPub;
    // DATA_PROC::Data_Pub dataPub(false);

    // dataPub.rightHand();
    // dataPub.rightHand({400,400,400,400,600,999});
    // dataPub.rightHand();
    // dataPub.doubleHand();
    // dataPub.doubleHand({400,400,400,400,600,999},{400,400,400,400,600,999});
    // dataPub.doubleHand();

    /*
    获取初始位置
    */
    istest = 0;  
    ctrl_mode = robot_ctrl;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            ctrl_mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }


    //读取当前位置
    if(istest == 1){
    }else{
        sensor_msgs::JointState JointState;
        robot.getCurrentState(JointState);
        Getpos(JointState, l_jnt_init, r_jnt_init);
    }
    pose0_l = l_jnt_init;
    pose0_r = r_jnt_init;
    while (ros::ok())
    {
        //限位
        // for(int i = 0; i < 7; i++){

        //     if (pose0_l[i] < theta_limit(i,0)){
        //         ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //         pose0_l[i] = theta_limit(i,0);
        //     }
        //     else if (pose0_l[i] > theta_limit(i,1)){
        //         ROS_ERROR("Error: left arm joint %d exceeds the limit position.", i + 1);
        //         pose0_l[i] = theta_limit(i,1);
        //     }

        //     if (pose0_r[i] < theta_limit(i,0)){
        //         ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //         pose0_r[i] = theta_limit(i,0);
        //     }
        //     else if (pose0_r[i] > theta_limit(i,1)){
        //         ROS_ERROR("Error: right arm joint %d exceeds the limit position.", i + 1);
        //         pose0_r[i] = theta_limit(i,1);
        //     }
        // }


        for (size_t j = 0; j < 7; j++)
        {
            pose0_l[j] = pose0_l[j] + target_vel_[j]*0.001;
            pose0_r[j] = pose0_r[j] + target_vel_[j + 7]*0.001;
        }

        sensor_msgs::JointState jnt_state_msgs_rviz;
        int num_jnt = 14;
        std_msgs::Float64MultiArray jnt_state_msgs;
        jnt_state_msgs.data.resize(28);
        int num = 7;
        int num2 = 7;
        ros::Rate rate(1000);

        if(ctrl_mode == robot_ctrl){
            for(int j = 0; j < num; j++){
                jnt_state_msgs.data[j] = pose0_l[j];
            }
            for(int j = 0; j < num2; j++){
                jnt_state_msgs.data[j + num] = pose0_r[j];
            }
            for(int j = num + num2; j < 28; j++){
                jnt_state_msgs.data[j] = 0;
            }
            pub_motor.publish(jnt_state_msgs);

        }

        jnt_state_msgs_rviz.header.stamp = ros::Time::now();
        jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

        jnt_state_msgs_rviz.name.resize(num_jnt);
        jnt_state_msgs_rviz.position.resize(num_jnt);
        jnt_state_msgs_rviz.velocity.resize(num_jnt);

         for(int j = 0; j < num; j++){
               jnt_state_msgs_rviz.name[j+num2] = "l_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j+num2] = pose0_l[j];
               jnt_state_msgs_rviz.velocity[j+num2] = 0;
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         for(int j = 0; j < num2; j++){
               jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j] = pose0_r[j];
               jnt_state_msgs_rviz.velocity[j] = 0;
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         // jnt_state_msgs.position[0] = 0.2;
         pub_rviz.publish(jnt_state_msgs_rviz);

        rate.sleep();
    
    }

    return 0;
}



//持续发布测试
//rostopic pub -r 10 /h16_controls/sw std_msgs/Int32MultiArray "data: [0, 0, 0, 0]"
//rostopic pub -r 10 /h16_controls/small_knob std_msgs/Float32MultiArray "data: [0.0, 0.0]"