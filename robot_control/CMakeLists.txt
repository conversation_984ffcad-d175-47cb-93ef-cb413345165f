cmake_minimum_required(VERSION 3.0.2)
project(robot_control)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  message_generation
  serial
  visualization_msgs
  rosbag
  rosbag_storage
)

## Generate services in the 'srv' folder
add_service_files(
  FILES
  get_angle_act.srv
  get_current.srv
  get_error.srv
  get_force_act.srv
  set_angle.srv
  set_current_limit.srv
  set_speed.srv
)

## Generate added messages and services with any dependencies listed here
generate_messages(
  DEPENDENCIES
  std_msgs
)

catkin_package(
 INCLUDE_DIRS include
 LIBRARIES
#  CATKIN_DEPENDS message_generation roscpp serial std_msgs visualization_msgs
 DEPENDS roscpp
)

#############
## EIGEN3 ##
#############
find_package(Eigen3 REQUIRED)
include_directories(${EIGEN3_INCLUDE_DIRS})

#########
## COAL ##
#########
# Using coal library instead of hpp-fcl
# 设置conda环境路径
#set(CONDA_ENV_PATH $ENV{HOME}/anaconda3/envs/coal)
set(CONDA_ENV_PATH /root/miniconda/envs/coal)
# 添加conda环境中的库路径
list(APPEND CMAKE_PREFIX_PATH ${CONDA_ENV_PATH})
# 查找coal库
find_package(coal REQUIRED)

include_directories(
include
  ${catkin_INCLUDE_DIRS}
  ${CONDA_ENV_PATH}/include
  ${CONDA_ENV_PATH}/include/coal
)

# 设置运行时库路径（RPATH）
set(CMAKE_INSTALL_RPATH "${CONDA_ENV_PATH}/lib")

#########
## KDL ##
#########
find_package(orocos_kdl REQUIRED)
include_directories(${orocos_kdl_INCLUDE_DIRS})
find_package(kdl_parser REQUIRED)
include_directories(${kdl_parser_INCLUDE_DIRS})

find_package(orocos_kdl REQUIRED)
find_package(kdl_parser REQUIRED)



catkin_package(
#   INCLUDE_DIRS include
#  CATKIN_DEPENDS roscpp rospy std_msgs
#  DEPENDS system_lib
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  #${EIGEN3_INCLUDE_DIR}
)
include_directories(/usr/include/eigen3)

add_library(robot_lib5
  src/robot_control.cpp
)

## Declare a C++ library
add_library(arms_gen2_control_lib2
  utils/DataProc_Callback.cpp
  utils/DataProc_DataPrint.cpp
  utils/DataProc_DataSave.cpp
  utils/DataProc_Init.cpp
  utils/DataProc_InspireHand.cpp
  utils/DataProc_Move.cpp
  utils/Kinematics_Solver.cpp
  utils/Kinematics_Trans.cpp
  utils/AnomDet_Detection.cpp
  utils/AnomDet_HppFcl.cpp
  utils/TrajPlan_Adjust.cpp
  utils/TrajPlan_DMP.cpp
  utils/TrajPlan_Inter.cpp
)

add_library(def_lib5
  include/arms_gen2_control/Def_Struct.h
  include/arms_gen2_control/Def_Class.h
  src/lib01_SubFunc.cpp
  src/lib02_PlanFunc.cpp
  src/lib03_PreDefinedActions.cpp
)

target_link_libraries(robot_lib5
  ${catkin_LIBRARIES}
  ${orocos_kdl_LIBRARIES}
  ${kdl_parser_LIBRARIES}
  ${COAL_LIBRARIES}
)

add_executable(dance src/dance.cpp)
target_link_libraries(dance
  robot_lib5
  arms_gen2_control_lib2
)

add_executable(velocity_interpolator_four src/show/velocity_interpolator_four.cpp)
target_link_libraries(velocity_interpolator_four
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(jy_key src/show/jy_key.cpp)
target_link_libraries(jy_key
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(jy_joint src/show/jy_joint.cpp)
target_link_libraries(jy_joint
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(look src/show/look.cpp)
target_link_libraries(look
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(door src/show/door.cpp)
target_link_libraries(door
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(throw src/throw.cpp)
target_link_libraries(throw
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(velocity_interpolator src/velocity_interpolator.cpp)
target_link_libraries(velocity_interpolator
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(play_badminton src/play_badminton.cpp)
target_link_libraries(play_badminton
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(hand_test2 src/hand_test2.cpp)
target_link_libraries(hand_test2
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(hand_l_force src/hand_l_force.cpp)
target_link_libraries(hand_l_force
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(hand_r_force src/hand_r_force.cpp)
target_link_libraries(hand_r_force
  robot_lib5
  arms_gen2_control_lib2
)

add_executable(fk_kdl src/fk_kdl.cpp)
target_link_libraries(fk_kdl
  robot_lib5
)
add_executable(ik_kdl src/ik_kdl.cpp)
target_link_libraries(ik_kdl
  robot_lib5
)
add_executable(get_JointState src/get_JointState.cpp)
target_link_libraries(get_JointState
  robot_lib5
)
add_executable(multi_joint_plan src/multi_joint_plan.cpp)
target_link_libraries(multi_joint_plan
  robot_lib5
)
add_executable(game_hand src/game_hand/game_hand.cpp)
target_link_libraries(game_hand
  robot_lib5
)
add_executable(joint_position_controller src/joint_position_controller.cpp)
target_link_libraries(joint_position_controller
  robot_lib5
)
add_executable(peizi src/peizi.cpp)
target_link_libraries(peizi
  robot_lib5
)
add_executable(jidan src/jidan.cpp)
target_link_libraries(jidan
  robot_lib5
)
add_executable(fama src/fama.cpp)
target_link_libraries(fama
  robot_lib5
)
add_executable(line_plan src/line_plan.cpp)
target_link_libraries(line_plan
  robot_lib5
)
add_executable(game_goal src/game_hand/game_goal.cpp)
target_link_libraries(game_goal
  robot_lib5
)
add_executable(game_init src/game_hand/game_init.cpp)
target_link_libraries(game_init
  robot_lib5
)
add_executable(game_goal2 src/game_hand/game_goal2.cpp)
target_link_libraries(game_goal2
  robot_lib5
)
add_executable(game_battery src/game_hand/game_battery.cpp)
target_link_libraries(game_battery
  robot_lib5
)
add_executable(game_hand2 src/game_hand/game_hand2.cpp)
target_link_libraries(game_hand2
  robot_lib5
)
add_executable(get_camera src/get_camera.cpp)
target_link_libraries(get_camera
  robot_lib5
)
add_executable(assembly src/assembly.cpp)
target_link_libraries(assembly
  robot_lib5
)
add_executable(grasp_assembly src/grasp_assembly.cpp)
target_link_libraries(grasp_assembly
  robot_lib5
  arms_gen2_control_lib2
  def_lib5
)
add_executable(grasp_f src/grasp_f.cpp)
target_link_libraries(grasp_f
  robot_lib5
  arms_gen2_control_lib2
)
add_executable(keyboard_cartesian_controller src/keyboard_cartesian_controller.cpp)
target_link_libraries(keyboard_cartesian_controller
  robot_lib5
)
add_executable(circle_plan src/circle_plan.cpp)
target_link_libraries(circle_plan
  robot_lib5
)
add_executable(servo_control src/show/servo_control.cpp)
target_link_libraries(servo_control
  robot_lib5
)
add_executable(four_control src/four_control/four_control.cpp)
target_link_libraries(four_control
  robot_lib5
)
add_executable(four_initial_pose src/four_control/four_initial_pose.cpp)
target_link_libraries(four_initial_pose
  robot_lib5
)
add_executable(four_joint_position_control src/four_control/four_joint_position_control.cpp)
target_link_libraries(four_joint_position_control
  robot_lib5
)
add_executable(demo_salute src/demo_salute.cpp)
target_link_libraries(demo_salute
  robot_lib5
  arms_gen2_control_lib2
)