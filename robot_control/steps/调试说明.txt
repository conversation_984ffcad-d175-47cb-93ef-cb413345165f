
终端1：
第一步启动zlg主站程序
	ssh root@*************
	密码：root
	进入/opt/demo目录执行./demo_arm 8脚本


终端2：
cd HumanRobot
//关闭防火墙
sudo route add -net *********** netmask *************** dev enp114s0(一代)
sudo route add -net *********** netmask *************** dev enp4s0（二代）

第二步启动上位机
	roslaunch robot_description_v2 display.launch
	
第三步启动规划程序
	
注意：
离开时务必关闭主站电源和驱动电源。

一代图像检测：
启动docker
1 docker start deploy
2 如果报错：
	sudo rm -rf /tmp/.deployment.xauth/ 删除报错的文件
	sudo touch /tmp/.deployment.xauth
3 启动容器： docker start deploy
4 进入容器：docker exec -it deploy  bash
5 进入挂载目录：cd /home/<USER>/zhuaqu5
6 图形界面需要 xhost +
7 执行：python3 Gripper_detectBYonnx.py 


二代图像检测
启动conda
conda activate env1
进入目录~/object_detect/human_robot_visual
cd ~/object_detect/human_robot_visual
执行：python3 juice_demo.py(到水)
python3 fold_clothe_demo.py（叠衣服）
python3 wash_plate.py(洗盘子)
python3 grasp_orange.py(抓橘子)
python3 fall_mm.py(·倒mm豆)


语音识别和合成
1 进入home/humanoid/voice目录
2 source devel/setup.bash
3 roslaunch xfei_asr voiceRecognition.launch 执行语音识别
4 roslaunch xfei_asr TxtToVoice.launch 执行语音合成
5 rostopic pub xfwakeup std_msgs/String "ok" 唤醒语音识别
6 当在终端看到Start Listening...开始说话

大模型启动
1 conda activate env1

2 进入挂载目录：cd /llm/llm_v11

3 python zhipu_ros2.py 运行大模型


语音和大模型调试注意：
2 语音合成和语音识别可能会不工作 需要重启
3 语音合成和识别算法ctrl+c反应较慢
4 大模型运行后没反映，请重启目标检测算法


inspire手：
最新inspire手+腕控制程序在~/dexhand/wrist_hand0812目录下
1 查看端口 dmesg|grep tty*
  给权限  sudo chmod a+rw /dev/ttyUSB*（* 是串口号）
2 启动手
	若串口为ttyUSB0，终端指令 roslaunch inspire_hand hand_control.launch test_flag:=1
	若串口为其他，终端指令 roslaunch inspire_hand hand_control.launch port:=/dev/ttyUSB* test_flag:=1 （* 是串口号）
3 启动左手节点 rosrun inspire_hand hand_control_setangle_lefthand
启动双手节点 rosrun inspire_hand hand_control_setangle
注：左手张开指令 rosservice call /inspire_hand/set_angle 999 999 999 999 999 999 1
