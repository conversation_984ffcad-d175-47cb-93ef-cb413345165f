import roboticstoolbox as rtb
import numpy as np
from spatialmath.base import *
import matplotlib.pyplot as plt

plt.rcParams['axes.facecolor'] = 'white'

l0 = 0.2536
l1 = 0.2486
l2 = 0.1745
l3 = 0.07
l4 = 0.08

link1_R = rtb.DHLink(d=l0, alpha=-np.pi/2, offset= -np.pi/2)
link2_R = rtb.DHLink(d=0,  alpha= np.pi/2, offset= np.pi/2)
link3_R = rtb.DHLink(d=l1, alpha=-np.pi/2, offset= np.pi/2)
link4_R = rtb.DHLink(d=0,  alpha= np.pi/2, offset= 0)
link5_R = rtb.DHLink(d=l2, alpha=-np.pi/2, offset=0)
link6_R = rtb.DHLink(a=l3, d=0,  alpha= -np.pi/2, offset=-np.pi/2)
link7_R = rtb.DHLink(a=l4, d=0,  alpha= 0, offset= 0)

Tx = trotx(90,'deg')
Arm_R = rtb.DHRobot([link1_R,link2_R,link3_R,link4_R,link5_R,link6_R,link7_R], name="arm_r", base=Tx)

dt = 0.01
q = np.loadtxt('res.txt')
n = q.shape[0]
t = np.linspace(0,n*dt,n)

print(np.deg2rad(q[-1]))
print(q[-1])

# a = robot.plot(np.deg2rad(q),'pyplot',shadow=False,dt=0.01,vellipse=True,limits=[-0.2,0.5,-0.6,0.6,-0.6,0.2],movie="move.gif")
# a = Arm_R.plot(np.deg2rad(q),'pyplot',shadow=False,dt=dt,vellipse=True,limits=[-0.2,0.5,-0.6,0.6,-0.6,0.2])
# a = robot.teach(np.deg2rad([27.98,-39.03,11.32,77.07,-60]))
# a.hold()
 
plt.figure()
plt.plot(t,q[...,0],label='theta1',linewidth=1.5)
plt.plot(t,q[...,1],label='theta2',linewidth=1.5)
plt.plot(t,q[...,2],label='theta3',linewidth=1.5)
plt.plot(t,q[...,3],label='theta4',linewidth=1.5)
plt.plot(t,q[...,4],label='theta5',linewidth=1.5)
plt.plot(t,q[...,5],label='theta5',linewidth=1.5)
plt.plot(t,q[...,6],label='theta7',linewidth=1.5)
plt.legend()
# plt.savefig("jntpos.png")
plt.show()
