import pyautogui
import time
import pyperclip
from pynput import keyboard


# 开启终端
pyautogui.hotkey('ctrl','alt','t')
time.sleep(1)

# 连接主站
pyperclip.copy('ssh root@*************')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(1)

pyperclip.copy('./demo 8')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(5)

# 启动上位机
pyautogui.hotkey('ctrl','shift','o')
time.sleep(0.5)

pyperclip.copy('cd HumanRobot')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyperclip.copy('sudo route add -net *********** netmask *************** dev enp4s0')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)
pyautogui.typewrite('1\n',0.007)
time.sleep(1)

pyperclip.copy('roslaunch robot_description_v2 display.launch')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)


# 打开深度相机
pyautogui.hotkey('ctrl','shift','e')
time.sleep(0.5)

pyperclip.copy('conda activate env1')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(2)

pyperclip.copy('cd ~/object_detect/zhuaqu6')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)
pyperclip.copy('python3 Gripper_detectBYonnx.py')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)


pyautogui.keyDown('ctrl')
pyautogui.keyDown('shift')
pyautogui.keyDown('left')
time.sleep(0.8)
pyautogui.keyUp('left')
pyautogui.keyUp('ctrl')
pyautogui.keyUp('shift')

pyautogui.hotkey('alt','up')
time.sleep(0.5)
pyautogui.hotkey('ctrl','shift','e')
time.sleep(0.5)

pyautogui.keyDown('ctrl')
pyautogui.keyDown('shift')
pyautogui.keyDown('left')
time.sleep(0.8)
pyautogui.keyUp('left')
pyautogui.keyUp('ctrl')
pyautogui.keyUp('shift')

# 启动灵巧手
pyperclip.copy('cd ~/dexhand/wrist_hand0812')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyperclip.copy('sudo chmod a+rw /dev/ttyUSB0')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)
pyautogui.typewrite('1\n',0.007)
time.sleep(1)

pyperclip.copy('source devel/setup.bash')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyperclip.copy('roslaunch inspire_hand hand_control.launch test_flag:=1')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyautogui.hotkey('ctrl','shift','e')
time.sleep(0.5)

pyperclip.copy('source devel/setup.bash')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyperclip.copy('rosrun inspire_hand hand_control_setangle_lefthand')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

# 打开机械臂
pyautogui.hotkey('alt','down')
time.sleep(0.5)
pyautogui.hotkey('ctrl','shift','e')
time.sleep(0.5)

pyperclip.copy('cd ~/human_robot_ws')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

pyperclip.copy('source devel/setup.bash')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5)
pyautogui.hotkey('enter')
time.sleep(0.5)

""" pyperclip.copy('rosrun arms_gen2_control Action0')
pyautogui.hotkey('ctrl','shift','v')
time.sleep(0.5) """
# pyautogui.hotkey('enter')
# time.sleep(0.5)

