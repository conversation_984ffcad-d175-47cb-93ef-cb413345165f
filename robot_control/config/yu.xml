<?xml version='1.0' encoding='UTF-8'?>
<root>
 <tabbed_widget name="Main Window" parent="main_window">
  <Tab containers="1" tab_name="tab1">
   <Container>
    <DockSplitter orientation="-" sizes="0.166287;0.167426;0.166287;0.166287;0.167426;0.166287" count="6">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="0.136054" left="-0.005590" bottom="-0.046175"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="0.078378" left="-0.005590" bottom="-0.078009"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="0.083953" left="-0.005590" bottom="-0.096369"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="3297.453148" left="-0.005590" bottom="-3296.540685"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="3.103546" left="-0.005590" bottom="-3.123972"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="7.981867" top="3297.453726" left="-0.005590" bottom="-3296.543939"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab2">
   <Container>
    <DockSplitter orientation="-" sizes="0.142531;0.143672;0.142531;0.142531;0.142531;0.143672;0.142531" count="7">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="1.600046" left="0.000000" bottom="1.584908"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="-0.341397" left="0.000000" bottom="-0.613595"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="1.663869" left="0.000000" bottom="1.636663"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="-1.368175" left="0.000000" bottom="-1.538973"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="-1.549341" left="0.000000" bottom="-1.577567"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="-0.083395" left="0.000000" bottom="-0.098180"/>
       <limitY/>
      </plot>
     </DockArea>
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="15.001203" top="0.643757" left="0.000000" bottom="0.542911"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab2">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="5.974573" top="0.042564" left="0.000063" bottom="-0.017891"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab3">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="5.974573" top="0.042564" left="0.000063" bottom="-0.017891"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab4">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="5.974573" top="-0.058104" left="0.000063" bottom="-0.343134"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab5">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="5.974573" top="-0.149803" left="0.000063" bottom="-0.150406"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab6">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="18.614202" top="0.350270" left="0.000000" bottom="-0.755271"/>
       <limitY/>
       <curve name="/car_pos/error/data[1]" color="#d62728"/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab7">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="5.974573" top="0.100000" left="0.000063" bottom="-0.100000"/>
       <limitY/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab8">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="107.920992" top="1.187115" left="7.921401" bottom="-0.581715"/>
       <limitY/>
       <curve name="/motor_state/arm_motor_state_actual/joint1/effort" color="#ff7f0e"/>
       <curve name="/motor_state/arm_motor_state_actual/joint2/effort" color="#f14cc1"/>
       <curve name="/motor_state/arm_motor_state_actual/joint3/effort" color="#9467bd"/>
       <curve name="/motor_state/arm_motor_state_actual/joint4/effort" color="#17becf"/>
       <curve name="/motor_state/arm_motor_state_actual/joint5/effort" color="#bcbd22"/>
       <curve name="/motor_state/arm_motor_state_actual/joint6/effort" color="#1f77b4"/>
       <curve name="/motor_state/arm_motor_state_actual/joint7/effort" color="#d62728"/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <Tab containers="1" tab_name="tab9">
   <Container>
    <DockSplitter orientation="-" sizes="1" count="1">
     <DockArea name="...">
      <plot flip_x="false" style="Lines" mode="TimeSeries" flip_y="false">
       <range right="107.920992" top="0.923305" left="7.921401" bottom="-0.509105"/>
       <limitY/>
       <curve name="/motor_state/arm_motor_state_actual/joint8/effort" color="#1ac938"/>
       <curve name="/motor_state/arm_motor_state_actual/joint9/effort" color="#ff7f0e"/>
       <curve name="/motor_state/arm_motor_state_actual/joint10/effort" color="#f14cc1"/>
       <curve name="/motor_state/arm_motor_state_actual/joint11/effort" color="#9467bd"/>
       <curve name="/motor_state/arm_motor_state_actual/joint12/effort" color="#17becf"/>
       <curve name="/motor_state/arm_motor_state_actual/joint13/effort" color="#bcbd22"/>
       <curve name="/motor_state/arm_motor_state_actual/joint14/effort" color="#1f77b4"/>
      </plot>
     </DockArea>
    </DockSplitter>
   </Container>
  </Tab>
  <currentTabIndex index="9"/>
 </tabbed_widget>
 <use_relative_time_offset enabled="1"/>
 <!-- - - - - - - - - - - - - - - -->
 <!-- - - - - - - - - - - - - - - -->
 <Plugins>
  <plugin ID="DataLoad CSV">
   <parameters delimiter="0" time_axis=""/>
  </plugin>
  <plugin ID="DataLoad MCAP"/>
  <plugin ID="DataLoad ROS bags">
   <use_header_stamp value="false"/>
   <discard_large_arrays value="true"/>
   <max_array_size value="100"/>
   <boolean_strings_to_number value="true"/>
   <remove_suffix_from_strings value="true"/>
   <selected_topics value="/car_pos/act;/car_pos/error;/car_pos/vir;/motor_state/arm_motor_state_actual"/>
  </plugin>
  <plugin ID="DataLoad ULog"/>
  <plugin ID="ROS Topic Subscriber">
   <use_header_stamp value="false"/>
   <discard_large_arrays value="true"/>
   <max_array_size value="100"/>
   <boolean_strings_to_number value="true"/>
   <remove_suffix_from_strings value="true"/>
   <selected_topics value="/flag/toe_touch_ground;/imu/data;/imu/data_re;/imu/marker;/joint_states;/marker/toe_force;/motor_state/arm_error_code;/motor_state/arm_motor_state_actual;/motor_state/arm_motor_state_theoretical;/motor_state/leg_error_code;/motor_state/leg_fram_cnt;/motor_state/leg_motor_state_actual;/motor_state/leg_motor_state_extend_theoretical;/motor_state/leg_motor_state_theoretical;/motor_state/leg_vbus;/motor_state/runtime_leg_motor_state_actual;/odom;/rosout;/rosout_agg;/sensors/sridata;/tf;/yesense/command_resp;/yesense/gnss_data;/yesense/gps_data;/yesense/imu_status;/yesense/inertial_data;/yesense/nav_data;/yesense/sensor_data;/car_pos/act;/car_pos/error;/car_pos/vir;/car_vel/act;/car_vel/vir;/grasp_control_l;/grasp_control_r;/motor_command/arm_position_control"/>
  </plugin>
  <plugin ID="UDP Server"/>
  <plugin ID="WebSocket Server"/>
  <plugin ID="ZMQ Subscriber"/>
  <plugin ID="Fast Fourier Transform"/>
  <plugin ID="Quaternion to RPY"/>
  <plugin ID="Reactive Script Editor">
   <library code="--[[ Helper function to create a series from arrays&#xa;&#xa; new_series: a series previously created with ScatterXY.new(name)&#xa; prefix:     prefix of the timeseries, before the index of the array&#xa; suffix_X:   suffix to complete the name of the series containing the X value. If [nil], use the index of the array.&#xa; suffix_Y:   suffix to complete the name of the series containing the Y value&#xa; timestamp:   usually the tracker_time variable&#xa;              &#xa; Example:&#xa; &#xa; Assuming we have multiple series in the form:&#xa; &#xa;   /trajectory/node.{X}/position/x&#xa;   /trajectory/node.{X}/position/y&#xa;   &#xa; where {N} is the index of the array (integer). We can create a reactive series from the array with:&#xa; &#xa;   new_series = ScatterXY.new(&quot;my_trajectory&quot;) &#xa;   CreateSeriesFromArray( new_series, &quot;/trajectory/node&quot;, &quot;position/x&quot;, &quot;position/y&quot;, tracker_time );&#xa;--]]&#xa;&#xa;function CreateSeriesFromArray( new_series, prefix, suffix_X, suffix_Y, timestamp )&#xa;  &#xa;  --- clear previous values&#xa;  new_series:clear()&#xa;  &#xa;  --- Append points to new_series&#xa;  index = 0&#xa;  while(true) do&#xa;&#xa;    x = index;&#xa;    -- if not nil, get the X coordinate from a series&#xa;    if suffix_X ~= nil then &#xa;      series_x = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_X) )&#xa;      if series_x == nil then break end&#xa;      x = series_x:atTime(timestamp)&#x9; &#xa;    end&#xa;    &#xa;    series_y = TimeseriesView.find( string.format( &quot;%s.%d/%s&quot;, prefix, index, suffix_Y) )&#xa;    if series_y == nil then break end &#xa;    y = series_y:atTime(timestamp)&#xa;    &#xa;    new_series:push_back(x,y)&#xa;    index = index+1&#xa;  end&#xa;end&#xa;&#xa;--[[ Similar to the built-in function GetSeriesNames(), but select only the names with a give prefix. --]]&#xa;&#xa;function GetSeriesNamesByPrefix(prefix)&#xa;  -- GetSeriesNames(9 is a built-in function&#xa;  all_names = GetSeriesNames()&#xa;  filtered_names = {}&#xa;  for i, name in ipairs(all_names)  do&#xa;    -- check the prefix&#xa;    if name:find(prefix, 1, #prefix) then&#xa;      table.insert(filtered_names, name);&#xa;    end&#xa;  end&#xa;  return filtered_names&#xa;end&#xa;&#xa;--[[ Modify an existing series, applying offsets to all their X and Y values&#xa;&#xa; series: an existing timeseries, obtained with TimeseriesView.find(name)&#xa; delta_x: offset to apply to each x value&#xa; delta_y: offset to apply to each y value &#xa;  &#xa;--]]&#xa;&#xa;function ApplyOffsetInPlace(series, delta_x, delta_y)&#xa;  -- use C++ indeces, not Lua indeces&#xa;  for index=0, series:size()-1 do&#xa;    x,y = series:at(index)&#xa;    series:set(index, x + delta_x, y + delta_y)&#xa;  end&#xa;end&#xa;"/>
   <scripts/>
  </plugin>
  <plugin ID="CSV Exporter"/>
  <plugin ID="ROS /rosout Visualization"/>
  <plugin ID="ROS Topic Re-Publisher"/>
 </Plugins>
 <!-- - - - - - - - - - - - - - - -->
 <previouslyLoaded_Datafiles/>
 <previouslyLoaded_Streamer name="ROS Topic Subscriber"/>
 <!-- - - - - - - - - - - - - - - -->
 <customMathEquations/>
 <snippets/>
 <!-- - - - - - - - - - - - - - - -->
</root>

