<launch>
  <arg name="paused" default="false"/>
  <arg name="use_sim_time" default="true"/>
  <arg name="gui" default="true"/>
  <arg name="headless" default="false"/>
  <arg name="debug" default="false"/>

  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="debug" value="$(arg debug)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="paused" value="$(arg paused)"/>
    <arg name="use_sim_time" value="$(arg use_sim_time)"/>
    <arg name="headless" value="$(arg headless)"/>
  </include>

  <param name="robot_description" command="$(find xacro)/xacro $(find arms_gen2)/urdf/arms_gen2_urdf.xacro" />
  <node
    name="spawn_model"
    pkg="gazebo_ros"
    type="spawn_model"
    args="-urdf -model arms_gen2 -param robot_description -z 0.1"
    output="screen"
  />

  <rosparam file="$(find arms_gen2)/config/controller_config.yaml" command="load"/>

  <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false"
	output="screen" ns="/Arms_Gen2" args="joint_state_controller
                                        jnt1_r_controller
                                        jnt2_r_controller
                                        jnt3_r_controller
                                        jnt4_r_controller
                                        jnt5_r_controller
                                        jnt6_r_controller
                                        jnt7_r_controller
                                        jnt1_l_controller
                                        jnt2_l_controller
                                        jnt3_l_controller
                                        jnt4_l_controller
                                        jnt5_l_controller
                                        jnt6_l_controller
                                        jnt7_l_controller"/>
  
</launch>