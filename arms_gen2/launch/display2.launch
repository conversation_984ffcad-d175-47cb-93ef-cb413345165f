<launch>
  <arg
    name="model" />
  <param
    name="robot_description"
    textfile="$(find arms_gen2)/urdf/arms_gen3_urdf.urdf" />
  <!-- <param name="robot_description" command="$(find xacro)/xacro $(find arms_gen2)/urdf/arms_gen2_urdf.xacro" /> -->
  <node
    name="joint_state_publisher_gui"
    pkg="joint_state_publisher_gui"
    type="joint_state_publisher_gui" />
  <!-- <node
    name="joint_state_publisher"
    pkg="joint_state_publisher"
    type="joint_state_publisher" /> -->
  <node
    name="robot_state_publisher"
    pkg="robot_state_publisher"
    type="robot_state_publisher" />
  <node
    name="rviz2"
    pkg="rviz"
    type="rviz"
    args="-d $(find arms_gen2)/config/defult_config.rviz" />
</launch>