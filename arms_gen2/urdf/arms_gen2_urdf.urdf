<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="arms_gen2">

  <link name="world" />
  <joint name="world_to_base_footprint" type="fixed">
        <parent link="world" />
        <child link="base_footprint"/>
        <origin xyz="0 0 0.8" rpy="0 0 0"/>
  </joint>

  <link name="base_footprint">
        <visual>
            <geometry>
                <sphere radius="0.001" />
            </geometry>
        </visual>
  </link>
  <link name="base_link">
    <inertial>
      <origin
        xyz="0.0022699 -0.06556 0.00064895"
        rpy="0 0 0" />
      <mass
        value="9.4962" />
      <inertia
        ixx="0.099659"
        ixy="-0.00034775"
        ixz="-6.3845E-05"
        iyy="0.05583"
        iyz="0.00016509"
        izz="0.060576" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="base_link2base_footprint" type="fixed">
        <parent link="base_footprint" />
        <child link="base_link"/>
        <origin xyz="0 0 0" rpy="1.5708 0 0"/>
  </joint>
  <link name="r_arm_Link1">
    <inertial>
      <origin
        xyz="0.0054077 -1.0079E-06 0.049262"
        rpy="0 0 0" />
      <mass
        value="2.0769" />
      <inertia
        ixx="0.0014285"
        ixy="-1.2629E-06"
        ixz="1.0683E-05"
        iyy="0.0013313"
        iyz="5.6547E-07"
        izz="0.0012659" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint1" type="revolute">
    <origin
      xyz="0 0 0.2016"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="r_arm_Link1" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link2">
    <inertial>
      <origin
        xyz="0.030445498299944 1.81823605061027E-08 -0.00992570305958366"
        rpy="0 0 0" />
      <mass
        value="0.0535693858885412" />
      <inertia
        ixx="4.1138936208078E-05"
        ixy="4.90996195884821E-11"
        ixz="-1.55301891344656E-05"
        iyy="5.88910959145939E-05"
        iyz="-9.20100747991942E-12"
        izz="5.08847911638598E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint2" type="revolute">
    <origin
      xyz="0.0356 0 0.052"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="r_arm_Link1" />
    <child
      link="r_arm_Link2" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link3">
    <inertial>
      <origin
        xyz="-0.00077096 3.2231E-05 0.040289"
        rpy="0 0 0" />
      <mass
        value="1.7543" />
      <inertia
        ixx="0.00098646"
        ixy="1.4682E-06"
        ixz="-1.5794E-05"
        iyy="0.00099837"
        iyz="1.9423E-06"
        izz="0.0010774" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91373 0.92549 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint3" type="revolute">
    <origin
      xyz="0.049 0 -0.035"
      rpy="1.5708 0 1.5708" />
    <parent
      link="r_arm_Link2" />
    <child
      link="r_arm_Link3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link4">
    <inertial>
      <origin
        xyz="-0.00040895 -0.010852 -0.034138"
        rpy="0 0 0" />
      <mass
        value="1.8511" />
      <inertia
        ixx="0.0010294"
        ixy="1.7019E-06"
        ixz="1.8412E-06"
        iyy="0.00095158"
        iyz="-9.9823E-06"
        izz="0.0011633" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91373 0.92549 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint4" type="revolute">
    <origin
      xyz="-0.041 0 0.1996"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="r_arm_Link3" />
    <child
      link="r_arm_Link4" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link5">
    <inertial>
      <origin
        xyz="7.1086E-05 0.00019673 0.033224"
        rpy="0 0 0" />
      <mass
        value="0.1362" />
      <inertia
        ixx="6.0336E-05"
        ixy="3.3879E-07"
        ixz="-5.2411E-08"
        iyy="6.6225E-05"
        iyz="-1.2548E-07"
        izz="5.5827E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint5" type="revolute">
    <origin
      xyz="0 -0.1395 -0.0410000000000012"
      rpy="1.5707963267949 0 0" />
    <parent
      link="r_arm_Link4" />
    <child
      link="r_arm_Link5" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link6">
    <inertial>
      <origin
        xyz="0.00015397 -0.057524 -0.025482"
        rpy="0 0 0" />
      <mass
        value="0.17352" />
      <inertia
        ixx="8.2852E-05"
        ixy="-1.5144E-07"
        ixz="-3.2863E-07"
        iyy="7.3636E-05"
        iyz="4.385E-06"
        izz="7.9464E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint6" type="revolute">
    <origin
      xyz="0 0.0295 0.035"
      rpy="-1.5708 0 0" />
    <parent
      link="r_arm_Link5" />
    <child
      link="r_arm_Link6" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="r_arm_Link7">
    <inertial>
      <origin
        xyz="0.098419 0.0043052 -0.022831"
        rpy="0 0 0" />
      <mass
        value="0.2298" />
      <inertia
        ixx="7.9303E-05"
        ixy="-1.3706E-06"
        ixz="-4.3132E-06"
        iyy="0.00011941"
        iyz="-7.6741E-07"
        izz="9.1235E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/r_arm_Link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_arm_Joint7" type="revolute">
    <origin
      xyz="0.0295 -0.07 -0.0295"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="r_arm_Link6" />
    <child
      link="r_arm_Link7" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>

  <link name="r_arm_Link7_tool"/>
  <joint name="r_arm_Link7_to_r_arm_Link7_tool" type="fixed">
        <parent link="r_arm_Link7" />
        <child link="r_arm_Link7_tool"/>
        <origin xyz="0.18 0.055 -0.003" rpy="0 0 0"/>
  </joint>

  <link name="l_arm_Link1">
    <inertial>
      <origin
        xyz="0.0054077 1.0079E-06 -0.044262"
        rpy="0 0 0" />
      <mass
        value="2.0769" />
      <inertia
        ixx="0.0014285"
        ixy="1.2629E-06"
        ixz="-1.0683E-05"
        iyy="0.0013313"
        iyz="5.6547E-07"
        izz="0.0012659" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint1" type="revolute">
    <origin
      xyz="0 0 -0.2066"
      rpy="0 0 0" />
    <parent
      link="base_link" />
    <child
      link="l_arm_Link1" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link2">
    <inertial>
      <origin
        xyz="0.030445 -1.8182E-08 0.015926"
        rpy="0 0 0" />
      <mass
        value="0.053569" />
      <inertia
        ixx="4.1139E-05"
        ixy="-4.91E-11"
        ixz="1.553E-05"
        iyy="5.8891E-05"
        iyz="-9.201E-12"
        izz="5.0885E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint2" type="revolute">
    <origin
      xyz="0.0416 0 -0.047"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="l_arm_Link1" />
    <child
      link="l_arm_Link2" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link3">
    <inertial>
      <origin
        xyz="-0.00077096 -3.2231E-05 -0.040289"
        rpy="0 0 0" />
      <mass
        value="1.7543" />
      <inertia
        ixx="0.00098646"
        ixy="-1.4682E-06"
        ixz="1.5794E-05"
        iyy="0.00099837"
        iyz="1.9423E-06"
        izz="0.0010774" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91373 0.92549 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint3" type="revolute">
    <origin
      xyz="0.049 0 0.041"
      rpy="-1.5708 0 1.5708" />
    <parent
      link="l_arm_Link2" />
    <child
      link="l_arm_Link3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link4">
    <inertial>
      <origin
        xyz="-0.00034786 -0.010852 0.03414"
        rpy="0 0 0" />
      <mass
        value="1.8511" />
      <inertia
        ixx="0.0010294"
        ixy="-1.2344E-06"
        ixz="1.8412E-06"
        iyy="0.00095158"
        iyz="9.2798E-06"
        izz="0.0011633" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint4" type="revolute">
    <origin
      xyz="-0.041 0 -0.1996"
      rpy="1.5708 0 1.5708" />
    <parent
      link="l_arm_Link3" />
    <child
      link="l_arm_Link4" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link5">
    <inertial>
      <origin
        xyz="-7.1086409115571E-05 0.000196728471945962 -0.0332241836787868"
        rpy="0 0 0" />
      <mass
        value="0.136203786147042" />
      <inertia
        ixx="6.03364568768581E-05"
        ixy="-3.38789023137641E-07"
        ixz="-5.24107296467038E-08"
        iyy="6.6225347822047E-05"
        iyz="1.25481880711014E-07"
        izz="5.58270660263295E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint5" type="revolute">
    <origin
      xyz="0 -0.1395 0.041"
      rpy="-1.5708 0 0" />
    <parent
      link="l_arm_Link4" />
    <child
      link="l_arm_Link5" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link6">
    <inertial>
      <origin
        xyz="0.00015488 -0.057524 0.025486"
        rpy="0 0 0" />
      <mass
        value="0.17352" />
      <inertia
        ixx="8.2852E-05"
        ixy="-1.5022E-07"
        ixz="-3.2863E-07"
        iyy="7.3636E-05"
        iyz="-4.5625E-06"
        izz="7.9464E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint6" type="revolute">
    <origin
      xyz="0 0.0295 -0.035"
      rpy="1.5708 0 0" />
    <parent
      link="l_arm_Link5" />
    <child
      link="l_arm_Link6" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>
  <link name="l_arm_Link7">
    <inertial>
      <origin
        xyz="0.094481 0.0038405 0.021825"
        rpy="0 0 0" />
      <mass
        value="0.2171" />
      <inertia
        ixx="7.8649E-05"
        ixy="-1.6003E-06"
        ixz="4.0641E-06"
        iyy="0.00011808"
        iyz="6.9271E-07"
        izz="8.8731E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://arms_gen2/meshes/l_arm_Link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_arm_Joint7" type="revolute">
    <origin
      xyz="0.0295 -0.07 0.0295"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="l_arm_Link6" />
    <child
      link="l_arm_Link7" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="100"
      velocity="1" />
    <dynamics
      damping="0.7"
      friction="0.3" />
  </joint>

  <link name="l_arm_Link7_tool"/>
  <joint name="l_arm_Link7_to_l_arm_Link7_tool" type="fixed">
      <parent link="l_arm_Link7" />
      <child link="l_arm_Link7_tool" />
      <origin xyz="0.18 0.055 0.003" rpy="0 0 0" />
  </joint>

</robot>