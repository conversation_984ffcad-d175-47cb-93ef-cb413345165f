import numpy as np

# 读取原始数据（假设是空格/制表符分隔的TXT文件）
data = np.loadtxt('JntPos_grasp.txt')  # 替换为你的文件名

# 提取时间列和其他数据列
time = data[:, 0]  # 第1列是时间（0, 0.1, 0.2, ...）
values = data[:, 1:]  # 其他列是数据

# 生成新的时间点（100Hz，步长0.01s）
new_time = np.arange(time[0], time[-1] + 0.01, 0.01)

# 线性插值（对每一列分别插值）
new_values = np.zeros((len(new_time), values.shape[1]))
for i in range(values.shape[1]):
    new_values[:, i] = np.interp(new_time, time, values[:, i])

# 合并时间列和插值后的数据
interpolated_data = np.column_stack((new_time, new_values))

# 保存到新的TXT文件（保留9位小数，制表符分隔）
np.savetxt('output_100hz.txt', interpolated_data, fmt='%.9f', delimiter='\t')
