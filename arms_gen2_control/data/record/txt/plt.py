import os
import re
from datetime import datetime
import numpy as np
from spatialmath.base import *
import matplotlib.pyplot as plt

plt.rcParams['axes.facecolor'] = 'white'

def getLastestFile(fileList, fileDir):
    # 如果找到了符合条件的文件
    if fileList:
        fileList.sort(key=lambda f: os.path.getmtime(os.path.join(fileDir, f)), reverse=True)
        
        # 获取最新修改时间的文件
        latest_file = max(fileList, key=lambda f: os.path.getmtime(os.path.join(fileDir, f)))
        
        print(f"读取文件: {latest_file}")

        # 保留最新的5个文件，删除其他文件
        files_to_delete = fileList[5:]  # 获取多余的文件
        for file in files_to_delete:
            file_path = os.path.join(fileDir, file)
            os.remove(file_path)  # 删除文件
        
        print("文件清理完成。")
        return latest_file
    else:
        print("没有找到符合条件的文件")
        return 0

if __name__ == "__main__":
    # 获取当前目录
    current_dir = os.getcwd() + "/src/arms_gen2_control/data/record/txt"
    print(current_dir)

    # 获取当前目录下所有以“record_data_”开头并且是txt文件的文件名
    txt_files1 = [f for f in os.listdir(current_dir+"/pub_pos") if f.startswith("pub_pos_") and f.endswith(".txt")]
    txt_files2 = [f for f in os.listdir(current_dir+"/pub_vel") if f.startswith("pub_vel_") and f.endswith(".txt")]
    txt_files3 = [f for f in os.listdir(current_dir+"/pub_acc") if f.startswith("pub_acc_") and f.endswith(".txt")]

    pub_pos_file = getLastestFile(txt_files1,current_dir+"/pub_pos")
    pub_vel_file = getLastestFile(txt_files2,current_dir+"/pub_vel")
    pub_acc_file = getLastestFile(txt_files3,current_dir+"/pub_acc")

    dt = 0.001
    q_pub = np.loadtxt(current_dir+"/pub_pos"+'/'+pub_pos_file)
    v_pub = np.loadtxt(current_dir+"/pub_vel"+'/'+pub_vel_file)
    a_pub = np.loadtxt(current_dir+"/pub_acc"+'/'+pub_acc_file)
    n = q_pub.shape[0]
    t = np.linspace(0,n*dt,n)

    
    fig1 = plt.figure(figsize=(20,10))
    ax11 = fig1.add_subplot(231)
    ax11.plot(t,q_pub[...,0],label='theta1',linewidth=1.5)
    ax11.plot(t,q_pub[...,1],label='theta2',linewidth=1.5)
    ax11.plot(t,q_pub[...,2],label='theta3',linewidth=1.5)
    ax11.plot(t,q_pub[...,3],label='theta4',linewidth=1.5)
    ax11.plot(t,q_pub[...,4],label='theta5',linewidth=1.5)
    ax11.plot(t,q_pub[...,5],label='theta5',linewidth=1.5)
    ax11.plot(t,q_pub[...,6],label='theta7',linewidth=1.5)
    ax11.legend()
    ax11.set_xlabel("time[s]")
    ax11.set_ylabel("joint angles[deg]")
    ax11.set_title("Angular trajectories of the joints of the right arm")


    ax12 = fig1.add_subplot(232)
    ax12.plot(t,v_pub[...,0],label='theta1',linewidth=1.5)
    ax12.plot(t,v_pub[...,1],label='theta2',linewidth=1.5)
    ax12.plot(t,v_pub[...,2],label='theta3',linewidth=1.5)
    ax12.plot(t,v_pub[...,3],label='theta4',linewidth=1.5)
    ax12.plot(t,v_pub[...,4],label='theta5',linewidth=1.5)
    ax12.plot(t,v_pub[...,5],label='theta5',linewidth=1.5)
    ax12.plot(t,v_pub[...,6],label='theta7',linewidth=1.5)
    ax12.legend()
    ax12.set_xlabel("time[s]")
    ax12.set_ylabel("joint angular velocity[deg/s]")
    ax12.set_title("angular velocity trajectories of the joints of the right arm")

    ax13 = fig1.add_subplot(233)
    ax13.plot(t,a_pub[...,0],label='theta1',linewidth=1.5)
    ax13.plot(t,a_pub[...,1],label='theta2',linewidth=1.5)
    ax13.plot(t,a_pub[...,2],label='theta3',linewidth=1.5)
    ax13.plot(t,a_pub[...,3],label='theta4',linewidth=1.5)
    ax13.plot(t,a_pub[...,4],label='theta5',linewidth=1.5)
    ax13.plot(t,a_pub[...,5],label='theta5',linewidth=1.5)
    ax13.plot(t,a_pub[...,6],label='theta7',linewidth=1.5)
    ax13.legend()
    ax13.set_xlabel("time[s]")
    ax13.set_ylabel("joint angular acceleration[deg/s^2]")
    ax13.set_title("angular acceleration trajectories of the joints of the right arm")

    ax14 = fig1.add_subplot(234)
    ax14.plot(t,q_pub[...,7],label='theta1',linewidth=1.5)
    ax14.plot(t,q_pub[...,8],label='theta2',linewidth=1.5)
    ax14.plot(t,q_pub[...,9],label='theta3',linewidth=1.5)
    ax14.plot(t,q_pub[...,10],label='theta4',linewidth=1.5)
    ax14.plot(t,q_pub[...,11],label='theta5',linewidth=1.5)
    ax14.plot(t,q_pub[...,12],label='theta5',linewidth=1.5)
    ax14.plot(t,q_pub[...,13],label='theta7',linewidth=1.5)
    ax14.legend()
    ax14.set_xlabel("time[s]")
    ax14.set_ylabel("joint angles[deg]")
    ax14.set_title("Angular trajectories of the joints of the left arm")


    ax15 = fig1.add_subplot(235)
    ax15.plot(t,v_pub[...,7],label='theta1',linewidth=1.5)
    ax15.plot(t,v_pub[...,8],label='theta2',linewidth=1.5)
    ax15.plot(t,v_pub[...,9],label='theta3',linewidth=1.5)
    ax15.plot(t,v_pub[...,10],label='theta4',linewidth=1.5)
    ax15.plot(t,v_pub[...,11],label='theta5',linewidth=1.5)
    ax15.plot(t,v_pub[...,12],label='theta5',linewidth=1.5)
    ax15.plot(t,v_pub[...,13],label='theta7',linewidth=1.5)
    ax15.legend()
    ax15.set_xlabel("time[s]")
    ax15.set_ylabel("joint angular velocity[deg/s]")
    ax15.set_title("angular velocity trajectories of the joints of the left arm")

    ax16 = fig1.add_subplot(236)
    ax16.plot(t,a_pub[...,7],label='theta1',linewidth=1.5)
    ax16.plot(t,a_pub[...,8],label='theta2',linewidth=1.5)
    ax16.plot(t,a_pub[...,9],label='theta3',linewidth=1.5)
    ax16.plot(t,a_pub[...,10],label='theta4',linewidth=1.5)
    ax16.plot(t,a_pub[...,11],label='theta5',linewidth=1.5)
    ax16.plot(t,a_pub[...,12],label='theta5',linewidth=1.5)
    ax16.plot(t,a_pub[...,13],label='theta7',linewidth=1.5)
    ax16.legend()
    ax16.set_xlabel("time[s]")
    ax16.set_ylabel("joint angular acceleration[deg/s^2]")
    ax16.set_title("angular acceleration trajectories of the joints of the left arm")

    fig1.savefig("./src/arms_gen2_control/data/record/txt/pub.png")

    plt.show()
