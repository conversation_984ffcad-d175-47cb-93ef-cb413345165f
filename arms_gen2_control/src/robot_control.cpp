#include "robot_control/robot_control.h"

namespace ROBOT_CONTROL{

   robot_control::robot_control(){}

   robot_control::robot_control(const char* urdf_path, const char* base_link, const char* l_end_link, const char* r_end_link)
   {
      //kdl初始化
      Tree my_tree; 
      kdl_parser::treeFromFile(urdf_path, my_tree);
      bool exit_value; 
      exit_value = my_tree.getChain(base_link,l_end_link,l_chain);
      if(!exit_value){ 
         cout << "Error:could not find chain Arm_left" << endl; 
      }else{
         unsigned int c_i = l_chain.getNrOfJoints();
         // std::cout << "左臂关节数: " << c_i << std::endl;
      }
      exit_value = my_tree.getChain(base_link,r_end_link,r_chain);
      if(!exit_value){ 
         cout << "Error:could not find chain Arm_right" << endl; 
      }else{
         unsigned int c_i = r_chain.getNrOfJoints();
         // std::cout << "右臂关节数: " << c_i << std::endl;
      }
      //关节限制
      theta_limit = MatrixXd(7, 2); // 初始化为 7x2 矩阵
      theta_limit << -M_PI, M_PI,
                     -M_PI, M_PI*20/180,
                     -M_PI, M_PI,
                     -1, 130*M_PI/180,
                     -M_PI, M_PI,
                     -M_PI, M_PI,
                     -M_PI, M_PI;
      //力矩限制
      effort_limit = MatrixXd(7, 2); // 初始化为 7x2 矩阵
      effort_limit << -15, 15,
                      -15, 15,
                      -15, 15,
                      -15, 15,
                      -15, 15,
                      -15, 15,
                      -15, 15;
      CurSubFlag = false;  
      current_sub = nh.subscribe("/motor_state/arm_motor_state_actual", 1000, &robot_control::CurrentStateCallback,this);
      pub_motor = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
      pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);
   }

   void robot_control::CurrentStateCallback(const sensor_msgs::JointState::ConstPtr &msg)
   {
      CurSubFlag = true;
      CurrentState = *msg;
      return;
   }


   bool robot_control::getCurrentState(sensor_msgs::JointState& JointState)
   {
      while(!CurSubFlag && ros::ok())
      {
         ROS_INFO("waiting for joint position");
         usleep(1000);
         ros::spinOnce();
      }
      JointState = CurrentState;
      if (CurSubFlag)
      {
         CurSubFlag = false;
         return true;
      }else{
         printf("%s \n","Error: could not getCurrentState : ");
         ros::shutdown();
         exit(0);
      }
      return false;
   }

   int robot_control::kdl_fk(std::array<double, 7> joint, def_msgs::Pose& pose, int arm){
      Chain chain;
      switch(arm){
         case 0:
            chain = l_chain;
            break;
         case 1:
            chain = r_chain;
            break;
         case 2:
            break;
      }
      // Create solver based on kinematic chain
      ChainFkSolverPos_recursive fksolver = ChainFkSolverPos_recursive(chain);

      // Create joint array
      unsigned int nj = chain.getNrOfJoints();
      KDL::JntArray jointpositions = JntArray(nj);

      // Assign some values to the joint positions
      for( int i=0; i<nj; i++){
         jointpositions(i) = joint[i];
      }

      // Create the frame that will contain the results
      KDL::Frame cartpos;

      // Calculate forward position kinematics
      int kinematics_status = fksolver.JntToCart(jointpositions,cartpos);

      if(kinematics_status == 0){
         Eigen::Matrix4d T = Eigen::Matrix4d::Identity();
         // 提取旋转矩阵和平移向量
         for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                  T(i, j) = cartpos.M(i, j);  // KDL 的 Rotation -> Eigen 的旋转部分
            }
            T(i, 3) = cartpos.p[i];  // KDL 的 Translation -> Eigen 的平移部分
         }
         EigenMatrixToPose(T, pose);
         // std::cout << cartpos <<std::endl;
         // printf("%s \n","Succes, thanks KDL!");
      }else{
         printf("%s \n","Error: could not calculate forward kinematics : ");
         ros::shutdown();
         exit(0);
      }
      
      return kinematics_status;
   }

   // rpy roll, pitch, yaw
   int robot_control::kdl_ik(std::array<double, 7> j_init, Eigen::Vector3d vec, Eigen::Vector3d rpy, std::array<double, 7>& result, int arm) {
      // const std::string first_joint_name = chain.getSegment(0).getName();
      Chain chain;
      switch(arm){
         case 0:
            chain = l_chain;
            break;
         case 1:
            chain = r_chain;
            break;
         case 2:
            break;
      }
      KDL::Vector vector = KDL::Vector(vec[0], vec[1], vec[2]);
      KDL::Rotation rot = KDL::Rotation::RPY(rpy[0], rpy[1], rpy[2]);
      KDL::Frame cartpos =  KDL::Frame(rot,vector);
      double eps=1E-5;
      int maxiter=500;
      double eps_joints=1E-15;
      KDL::ChainIkSolverPos_LMA iksolver = KDL::ChainIkSolverPos_LMA(chain,eps,maxiter,eps_joints);

      KDL::JntArray jointpositions =  KDL::JntArray(7);
      KDL::JntArray jointGuesspositions =  KDL::JntArray(7);
      // std::array<double, 7> j{0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
      for (size_t i = 0; i < 7; i++)
      {
         jointGuesspositions(i) = j_init[i];
      }

      int kinematics_status;
      kinematics_status = iksolver.CartToJnt(jointGuesspositions,cartpos,jointpositions);  

      if(kinematics_status == 0)
      {
         for(int i = 0;i < 7; i++)
         {
            result[i] = jointpositions(i);
            // std::cout << jointpositions(i) << std::endl;
         }
         // printf("%s \n","Success, thanks KDL!");
      }
      else
      {
         printf("%s \n","Error:could not calculate backword kinematics : ");
         return kinematics_status;
         ros::shutdown();
         exit(0);
      }

      for(int i = 0; i < 7; i++){
         if (result[i] < theta_limit(i,0) || result[i] > theta_limit(i,1)){
               ROS_ERROR("Error:  backword kinematics exceeds the limit position : ");
               array_cout("result", result);
               kinematics_status = -1;
               return kinematics_status;
               ros::shutdown();
               exit(0);

         }
      }
      return kinematics_status;
   }

   Plan_Res_Arm robot_control::multi_joint_plan(vector<std::array<double, 7>> qp, vector<double> t, double dt){
      int n = qp.size();
      
      vector<std::array<double, 7>> qv;
      vector<std::array<double, 7>> qa;

      //速度都设为相邻速度的最小值
      std::array<double, 7> qv0 = {0, 0, 0 ,0 ,0 ,0 , 0};
      qv.push_back(qv0);
      for (size_t i = 1; i < n - 1; i++)
      {
         std::array<double, 7> qp_l = qp[i-1];
         std::array<double, 7> qp_c = qp[i];
         std::array<double, 7> qp_r = qp[i+1];
         std::array<double, 7> qvx;
         for (size_t j = 0; j < 7; j++)
         {
               double l = (qp_c[j] - qp_l[j])/t[i-1]*2;
               double r = (qp_r[j] - qp_c[j])/t[i]*2;
               if( l*r <= 0){
                  qvx[j] = 0;
               }else if(l > 0){
                  qvx[j] = min(l, r);
               }else if(l < 0){
                  qvx[j] = max(l, r);
               }
         }
         qv.push_back(qvx);
         // array_cout("qvx", qvx);
         
      }
      qv.push_back(qv0);        

      //加速度都设为0
      for (size_t i = 0; i < n; i++)
      {
         std::array<double, 7> qax = {0, 0, 0 ,0 ,0 ,0 , 0};
         qa.push_back(qax);
      }
      double tt = 0.0;
      for (size_t i = 0; i < t.size(); i++)
      {
         tt += t[i];
      }
      
      int sum = std::round(tt / dt);
      MatrixXd x = MatrixXd::Zero(sum,7);
      MatrixXd dx = MatrixXd::Zero(sum,7);
      MatrixXd ddx = MatrixXd::Zero(sum,7);
      VectorXd time(sum);
      double t_c = dt;
      int ii = 0;
      for (size_t i = 0; i < t.size(); i++)
      {
         std::array<double, 7> p0 = qp[i];
         std::array<double, 7> v0 = qv[i];
         std::array<double, 7> a0 = qa[i];
         std::array<double, 7> p1 = qp[i+1];
         std::array<double, 7> v1 = qv[i+1];
         std::array<double, 7> a1 = qa[i+1];
         double qp0[7];
         double qv0[7];
         double qa0[7];
         double qpf[7];
         double qvf[7];
         double qaf[7];
         std::copy(p0.begin(), p0.end(), qp0);
         std::copy(v0.begin(), v0.end(), qv0);
         std::copy(a0.begin(), a0.end(), qa0);
         std::copy(p1.begin(), p1.end(), qpf);
         std::copy(v1.begin(), v1.end(), qvf);
         std::copy(a1.begin(), a1.end(), qaf);

         double ct = dt;
         int num = std::round(t[i] / dt);
         for (size_t k = 0; k < num; k++)
         {
               double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t[i]);
               double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , 7);
               for (size_t j = 0; j < 7; j++)
               {
                  x(ii,j) = pva[0][j];    
                  dx(ii,j) = pva[1][j];
                  ddx(ii,j) = pva[2][j];
               }
               time[ii] = t_c;
               ct += dt;
               t_c += dt;
               ii++;
         }
      }

      Plan_Res_Arm res;
      res.t = time;
      res.pos = x;
      res.vel = dx;
      res.acc = ddx;
      res.error_flag = 0;
      return res;
   }

   // Plan_Res_Arm robot_control::line_plan(array<double, 7> q_init, VectorXd pose_start, VectorXd pose_end, double t, int arm){

   //    double qp0[7] = { 0.0 };
   //    double qv0[7] = { 0.0 };
   //    double qa0[7] = { 0.0 };
   //    double qpf[7] = { 0.0 }; 
   //    double qvf[7] = { 0.0 };
   //    double qaf[7] = { 0.0 };
   //    const int nn = sizeof(qp0) / sizeof(qp0[0]);
   //    for (size_t i = 0; i < pose_start.size(); i++)
   //    {
   //       qp0[i] = pose_start[i];
   //       qpf[i] = pose_end[i];      
   //    }
   //    int num = std::round(t / 0.001);
   //    MatrixXd x = MatrixXd::Zero(num,nn);
   //    MatrixXd dx = MatrixXd::Zero(num,nn);
   //    MatrixXd ddx = MatrixXd::Zero(num,nn);
   //    VectorXd time(num);
   //    double ct = 0.001;
   //    Plan_Res_Arm res;
   //    res.error_flag = 0;
   //    for (size_t i = 0; i < num; i++)
   //    {
   //       double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t);
   //       double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
   //       Vector3d vec, rpy;
   //       vec << pva[0][0], pva[0][1], pva[0][2];
   //       rpy << pva[0][3], pva[0][4], pva[0][5]; //roll, pitch, yaw
   //       // array<double, 7> q_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
   //       array<double, 7> joint;
   //       int a = kdl_ik(q_init, vec, rpy, joint, arm);
   //       if (a!=0)
   //       {
   //          res.error_flag = 1;
   //          // return res;
   //       }
         

   //       for (size_t j = 0; j < nn; j++)
   //       {
   //             x(i,j) = joint[j];    
   //             // dx(i,j) = pva[1][j];
   //             // ddx(i,j) = pva[2][j];
   //       }
   //       time[i] = ct;
   //       ct += 0.001;
   //    }

   //    res.t = time;
   //    res.pos = x;
   //    res.vel = dx;
   //    res.acc = ddx;
   //    return res;
   // }

   Plan_Res_Arm robot_control::line_plan(array<double, 7> q_init, VectorXd pose_start, VectorXd pose_end, double t, int arm){
      double dtl = 0.1;
      double qp0[7] = { 0.0 };
      double qv0[7] = { 0.0 };
      double qa0[7] = { 0.0 };
      double qpf[7] = { 0.0 }; 
      double qvf[7] = { 0.0 };
      double qaf[7] = { 0.0 };
      const int nn = sizeof(qp0) / sizeof(qp0[0]);
      for (size_t i = 0; i < pose_start.size(); i++)
      {
         qp0[i] = pose_start[i];
         qpf[i] = pose_end[i];      
      }
      int num = std::round(t / dtl) + 1;
      MatrixXd x = MatrixXd::Zero(num,nn);
      MatrixXd dx = MatrixXd::Zero(num,nn);
      MatrixXd ddx = MatrixXd::Zero(num,nn);
      VectorXd time(num);
      double ct = 0.0;
      Plan_Res_Arm res;
      res.error_flag = 0;
      for (size_t i = 0; i < num; i++)
      {
         double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, t);
         double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
         Vector3d vec, rpy;
         vec << pva[0][0], pva[0][1], pva[0][2];
         rpy << pva[0][3], pva[0][4], pva[0][5]; //roll, pitch, yaw
         // array<double, 7> q_init{1.38, -0.11, 1.62, -0.63, -1.76, 0.11, 1.23 };
         array<double, 7> joint;
         int a = kdl_ik(q_init, vec, rpy, joint, arm);
         if (a!=0)
         {
            res.error_flag = 1;
            // return res;
         }
         
         for (size_t j = 0; j < nn; j++)
         {
            x(i,j) = joint[j];    
         }

         time[i] = ct;
         ct += dtl;
      }

      // 计算速度 dx (中心差分法)
      for (int i = 1; i < num - 1; ++i) {
         dx.row(i) = (x.row(i + 1) - x.row(i - 1)) / (2 * dtl); // 中心差分
      }

      // 计算加速度 ddx (速度的中心差分法)
      for (int i = 1; i < num - 1; ++i) {
         ddx.row(i) = (dx.row(i + 1) - dx.row(i - 1)) / (2 * dtl); // 中心差分
      }

      // 计算插值后的位置（1000Hz）
      int num2 = std::round(t / 0.001);
      MatrixXd x2 = MatrixXd::Zero(num2,nn);
      MatrixXd dx2 = MatrixXd::Zero(num2,nn);
      MatrixXd ddx2 = MatrixXd::Zero(num2,nn);

      for (size_t i = 0; i < num - 1; i++)
      {
         // 将第 i 行的值赋给 qp0
         for (int j = 0; j < 7; j++) {
            qp0[j] = x(i, j);
            qv0[j] = dx(i, j);
            qa0[j] = ddx(i, j);
            qpf[j] = x(i+1, j);
            qvf[j] = dx(i+1, j);
            qaf[j] = ddx(i+1, j);
         }
         ct = 0.001;
         int l = dtl/0.001;
         for (size_t k = 0; k < l; k++)
         {
            double** aaa = Interp5rdPoly_Param_fcn(qp0, qv0, qa0, qpf, qvf, qaf, dtl);
            double** pva = Interp5rdPoly_Data_t_fcn(aaa, ct , nn);
            for (size_t m = 0; m < nn; m++)
            {
               x2(i*l+k, m) = pva[0][m];  
               dx2(i*l+k, m) = pva[1][m];
               ddx2(i*l+k, m) = pva[2][m];  
            }
            ct += 0.001;
         }

      }
      
      res.t = x2;
      res.pos = x2;
      res.vel = dx2;
      res.acc = ddx2;
      return res;
   }

   void robot_control::move(Plan_Res_Arm motion, int move_mode){

      sensor_msgs::JointState jnt_state_msgs_rviz;
      int num_jnt = 14;
      std_msgs::Float64MultiArray jnt_state_msgs;
      jnt_state_msgs.data.resize(14);
      int n = motion.t.rows();
      int num = 7;
      ros::Rate rate(1000);

      for(int i = 0; i < n; i++){
         if(move_mode == robot_ctrl){
               for(int j = 0; j < num; j++){
                  jnt_state_msgs.data[j] = motion.pos(i,j);
                  jnt_state_msgs.data[j+7] = 0;
               }
               pub_motor.publish(jnt_state_msgs);
         }

         jnt_state_msgs_rviz.header.stamp = ros::Time::now();
         jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

         jnt_state_msgs_rviz.name.resize(num_jnt);
         jnt_state_msgs_rviz.position.resize(num_jnt);
         jnt_state_msgs_rviz.velocity.resize(num_jnt);
         
         for(int j = 0; j < num; j++){
               jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j] = motion.pos(i,j);
               jnt_state_msgs_rviz.velocity[j] = motion.vel(i,j);
               jnt_state_msgs_rviz.name[j+num] = "l_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j+num] = 0;
               jnt_state_msgs_rviz.velocity[j+num] = 0;
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         // jnt_state_msgs.position[0] = 0.2;
         pub_rviz.publish(jnt_state_msgs_rviz);

         rate.sleep();
      }
   }

   void robot_control::move(Plan_Res_Dual motion, int move_mode){

      sensor_msgs::JointState jnt_state_msgs_rviz;
      int num_jnt = 14;
      std_msgs::Float64MultiArray jnt_state_msgs;
      jnt_state_msgs.data.resize(28);
      int n1 = motion.left.t.rows();
      int n2 = motion.right.t.rows();
      int n = min(n1, n2);
      int num = 7;
      ros::Rate rate(1000);

      for(int i = 0; i < n; i++){
         if(move_mode == robot_ctrl){
               for(int j = 0; j < num; j++){
                  jnt_state_msgs.data[j] = motion.left.pos(i,j);
                  jnt_state_msgs.data[j+num] = motion.right.pos(i,j);
                  jnt_state_msgs.data[j+2*num] = 0;
                  jnt_state_msgs.data[j+3*num] = 0;
               }
               pub_motor.publish(jnt_state_msgs);
         }

         jnt_state_msgs_rviz.header.stamp = ros::Time::now();
         jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

         jnt_state_msgs_rviz.name.resize(num_jnt);
         jnt_state_msgs_rviz.position.resize(num_jnt);
         jnt_state_msgs_rviz.velocity.resize(num_jnt);
         
         for(int j = 0; j < num; j++){
               jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j] = motion.right.pos(i,j);
               jnt_state_msgs_rviz.velocity[j] = motion.right.vel(i,j);
               jnt_state_msgs_rviz.name[j+num] = "l_arm_Joint" + to_string(j+1);
               jnt_state_msgs_rviz.position[j+num] = motion.left.pos(i,j);
               jnt_state_msgs_rviz.velocity[j+num] = motion.left.vel(i,j);
               // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

         }
         // jnt_state_msgs.position[0] = 0.2;
         pub_rviz.publish(jnt_state_msgs_rviz);

         rate.sleep();
      }
   }
}

namespace CAMERA_READ{

   camera_read::camera_read()
   {
      detect_flag = false;
      vision_pose_base = nh.subscribe("/gripper_det_box", 1, &camera_read::CurrentStateCallback,this);
   }

   void camera_read::CurrentStateCallback(const std_msgs::Float64MultiArray::ConstPtr& msg)
   {
      detect_flag = true;
      msg_camera = *msg;  
      return;
   }

   bool camera_read::getCurrentState(std_msgs::Float64MultiArray& camera_data)
   {
      while(!detect_flag && ros::ok())
      {
         ROS_INFO("waiting for camera data");
         usleep(1000);
         ros::spinOnce();
      }
      if (detect_flag)
      {
         camera_data = msg_camera;
         detect_flag = false;
         return true;
      }else{
         printf("%s \n","Error: could not get camera data");
         ros::shutdown();
         exit(0);
      }
      return false;
   }

}

namespace INSPIRE_HAND{
    void Pos_Ctrl::serviceCall(const vector<int>& pos_cmd, const vector<int>& vel_cmd, int hand_id){
        if(hand_id == 1){
            fingle_speed1_.request.speed0 = vel_cmd[0];
            fingle_speed1_.request.speed1 = vel_cmd[1];
            fingle_speed1_.request.speed2 = vel_cmd[2];
            fingle_speed1_.request.speed3 = vel_cmd[3];
            fingle_speed1_.request.speed4 = vel_cmd[4];
            fingle_speed1_.request.speed5 = vel_cmd[5];
            fingle_speed1_.request.control_hand_id = hand_id;

            fingle_angle1_.request.angle0 = pos_cmd[0];
            fingle_angle1_.request.angle1 = pos_cmd[1];
            fingle_angle1_.request.angle2 = pos_cmd[2];
            fingle_angle1_.request.angle3 = pos_cmd[3];
            fingle_angle1_.request.angle4 = pos_cmd[4];
            fingle_angle1_.request.angle5 = pos_cmd[5];
            fingle_angle1_.request.control_hand_id = hand_id;

            fingle_angle_act1_.request.control_hand_id = hand_id;
            fingle_force_act1_.request.control_hand_id = hand_id;
            fingle_current1_.request.control_hand_id = hand_id;

            if(!set_speed1_.call(fingle_speed1_)){}
            ROS_INFO("set speed [hand id: %d]:\n%d %d %d %d %d %d", hand_id,vel_cmd[0],vel_cmd[1],vel_cmd[2],vel_cmd[3],vel_cmd[4],vel_cmd[5]);
            if(!set_angle1_.call(fingle_angle1_)){}
            ROS_INFO("set angle [hand id: %d]:\n%d %d %d %d %d %d",hand_id,pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5]);
            
            while(1){
                get_current1_.call(fingle_current1_);
                get_force1_.call(fingle_force_act1_);

                if(fingle_current1_.response.current[0] == 0 &&
                   fingle_current1_.response.current[1] == 0 &&
                   fingle_current1_.response.current[2] == 0 &&
                   fingle_current1_.response.current[3] == 0 &&
                   fingle_current1_.response.current[4] == 0 &&
                   fingle_current1_.response.current[5] == 0){
                    break;
                }
                if(fingle_force_act1_.response.curforce[0] > 1500 ||
                   fingle_force_act1_.response.curforce[1] > 1500 ||
                   fingle_force_act1_.response.curforce[2] > 1500 ||
                   fingle_force_act1_.response.curforce[3] > 1500 ||
                   fingle_force_act1_.response.curforce[4] > 1500 ||
                   fingle_force_act1_.response.curforce[5] > 1500){
                    break;
                }
                // set_angle1_.call(fingle_angle1_);
            }
            if(!get_angle1_.call(fingle_angle_act1_)){}
            ROS_INFO("current angle [hand id: %d]:\n%.1f %.1f %.1f %.1f %.1f %.1f",hand_id,
                                                      fingle_angle_act1_.response.curangle[0],
                                                      fingle_angle_act1_.response.curangle[1],
                                                      fingle_angle_act1_.response.curangle[2],
                                                      fingle_angle_act1_.response.curangle[3],
                                                      fingle_angle_act1_.response.curangle[4],
                                                      fingle_angle_act1_.response.curangle[5]);
            if(!get_force1_.call(fingle_force_act1_)){}
            ROS_INFO("current force [hand id: %d]:\n%.1f %.1f %.1f %.1f %.1f %.1f\n",hand_id,
                                                      fingle_force_act1_.response.curforce[0],
                                                      fingle_force_act1_.response.curforce[1],
                                                      fingle_force_act1_.response.curforce[2],
                                                      fingle_force_act1_.response.curforce[3],
                                                      fingle_force_act1_.response.curforce[4],
                                                      fingle_force_act1_.response.curforce[5]);
        }else if(hand_id == 2){
            fingle_speed2_.request.speed0 = vel_cmd[0];
            fingle_speed2_.request.speed1 = vel_cmd[1];
            fingle_speed2_.request.speed2 = vel_cmd[2];
            fingle_speed2_.request.speed3 = vel_cmd[3];
            fingle_speed2_.request.speed4 = vel_cmd[4];
            fingle_speed2_.request.speed5 = vel_cmd[5];
            fingle_speed2_.request.control_hand_id = hand_id;

            fingle_angle2_.request.angle0 = pos_cmd[0];
            fingle_angle2_.request.angle1 = pos_cmd[1];
            fingle_angle2_.request.angle2 = pos_cmd[2];
            fingle_angle2_.request.angle3 = pos_cmd[3];
            fingle_angle2_.request.angle4 = pos_cmd[4];
            fingle_angle2_.request.angle5 = pos_cmd[5];
            fingle_angle2_.request.control_hand_id = hand_id;

            fingle_angle_act2_.request.control_hand_id = hand_id;
            fingle_force_act2_.request.control_hand_id = hand_id;
            fingle_current2_.request.control_hand_id = hand_id;

            if(!set_speed2_.call(fingle_speed2_)){}
            ROS_INFO("set speed [hand id: %d]:\n%d %d %d %d %d %d", hand_id,vel_cmd[0],vel_cmd[1],vel_cmd[2],vel_cmd[3],vel_cmd[4],vel_cmd[5]);
            if(!set_angle2_.call(fingle_angle2_)){}
            ROS_INFO("set angle [hand id: %d]:\n%d %d %d %d %d %d",hand_id,pos_cmd[0],pos_cmd[1],pos_cmd[2],pos_cmd[3],pos_cmd[4],pos_cmd[5]);
            
            while(1){
                get_current2_.call(fingle_current2_);
                get_force2_.call(fingle_force_act2_);
                if(fingle_current2_.response.current[0] == 0 &&
                   fingle_current2_.response.current[1] == 0 &&
                   fingle_current2_.response.current[2] == 0 &&
                   fingle_current2_.response.current[3] == 0 &&
                   fingle_current2_.response.current[4] == 0 &&
                   fingle_current2_.response.current[5] == 0){
                    break;
                }
                if(fingle_force_act2_.response.curforce[0] > 1500 ||
                   fingle_force_act2_.response.curforce[1] > 1500 ||
                   fingle_force_act2_.response.curforce[2] > 1500 ||
                   fingle_force_act2_.response.curforce[3] > 1500 ||
                   fingle_force_act2_.response.curforce[4] > 1500 ||
                   fingle_force_act2_.response.curforce[5] > 1500){
                    break;
                }
                // set_angle2_.call(fingle_angle2_);
            }
            if(!get_angle2_.call(fingle_angle_act2_)){}
            ROS_INFO("current angle [hand id: %d]:\n%.1f %.1f %.1f %.1f %.1f %.1f",hand_id,
                                                      fingle_angle_act2_.response.curangle[0],
                                                      fingle_angle_act2_.response.curangle[1],
                                                      fingle_angle_act2_.response.curangle[2],
                                                      fingle_angle_act2_.response.curangle[3],
                                                      fingle_angle_act2_.response.curangle[4],
                                                      fingle_angle_act2_.response.curangle[5]);
            if(!get_force2_.call(fingle_force_act2_)){}
            ROS_INFO("current force [hand id: %d]:\n%.1f %.1f %.1f %.1f %.1f %.1f\n",hand_id,
                                                      fingle_force_act2_.response.curforce[0],
                                                      fingle_force_act2_.response.curforce[1],
                                                      fingle_force_act2_.response.curforce[2],
                                                      fingle_force_act2_.response.curforce[3],
                                                      fingle_force_act2_.response.curforce[4],
                                                      fingle_force_act2_.response.curforce[5]);
        }else{
            ROS_ERROR("wrong hand id!!!");
        }
    }
    void Pos_Ctrl::rightHand(){
        serviceCall(default_pos_,default_vel_,2);
    }
    void Pos_Ctrl::rightHand(const vector<int>& pos_cmd){
        serviceCall(pos_cmd,default_vel_,2);
    }
    void Pos_Ctrl::rightHand(const vector<int>& pos_cmd, const vector<int>& vel_cmd){
        serviceCall(pos_cmd,vel_cmd,2);
    }

    void Pos_Ctrl::leftHand(){
        serviceCall(default_pos_,default_vel_,1);
    }
    void Pos_Ctrl::leftHand(const vector<int>& pos_cmd){
        serviceCall(pos_cmd,default_vel_,1);
    }
    void Pos_Ctrl::leftHand(const vector<int>& pos_cmd, const vector<int>& vel_cmd){
        serviceCall(pos_cmd,vel_cmd,1);
    }

    void Pos_Ctrl::doubleHand(){
        thread t1(&Pos_Ctrl::serviceCall,this,default_pos_,default_vel_,1);
        thread t2(&Pos_Ctrl::serviceCall,this,default_pos_,default_vel_,2);

        t1.join();
        t2.join();
    }
    void Pos_Ctrl::doubleHand(const vector<int>& pos_cmd_r, const vector<int>& pos_cmd_l){
        thread t1(&Pos_Ctrl::serviceCall,this,pos_cmd_l,default_vel_,1);
        thread t2(&Pos_Ctrl::serviceCall,this,pos_cmd_r,default_vel_,2);

        t1.join();
        t2.join();
    }
    void Pos_Ctrl::doubleHand(const vector<int>& pos_cmd_r, const vector<int>& vel_cmd_r, const vector<int>& pos_cmd_l, const vector<int>& vel_cmd_l){
        thread t1(&Pos_Ctrl::serviceCall,this,pos_cmd_l,vel_cmd_l,1);
        thread t2(&Pos_Ctrl::serviceCall,this,pos_cmd_r,vel_cmd_r,2);

        t1.join();
        t2.join();
    }
}