/*
抓递水果
    1、左手抓取水果
    2、左手移动到中间位置，手心向上，右手移动到抓取水果位置上方
    3、左手张开，右手抓取水果
    4、左手移动到待机位置，右手向前伸出，手心向上
*/ 
#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592653589

double dt = 0.0014;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::INTER interLib;
ACT_LIB::MOVE moveLib;

std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }

    //关节位置校验
    for(int i = 0; i < 7; i++){
        if (jnt_pos_current_l[i] < -PI || jnt_pos_current_l[i] > PI){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_l[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
        if (jnt_pos_current_r[i] < -PI || jnt_pos_current_r[i] > PI){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_r[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
    }

    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action07_fold_towel");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

	//启用多线程
/*  	ros::AsyncSpinner spinner(3); // Use 2 threads
 	spinner.start(); //开始标志 */

    /*
    move_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
    // int ctrl_mode = 1;
    CTRL_MODE ctrl_mode = robot_ctrl;
    // CTRL_MODE ctrl_mode = stop;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    int is_test = 0;
    int count = 0;

    int act1_done_flag;
    int act2_enable_flag;
    int act3_enable_flag;
    int act4_enable_flag;

    Vector3d pos_grasp;
    Vector3d pose_grasp;
    pose_grasp << PI/4, PI/6, -PI/2;

    Vector3d pos_grasp_pre;
    Vector3d pose_grasp_pre;

    VectorXd t_act1_pre(2);
    VectorXd phi_act1_pre(1);
    MatrixXd pos_act1_pre_l(1,3);
    MatrixXd pose_act1_pre_l(1,3);
    Plan_Res_MulDOF act1_pre_l;
    Plan_Res_MulDOF act1_pre_r;
    int n1_pre;
    VectorXd t_act1(2);
    VectorXd phi_act1(1);
    MatrixXd pos_act1_l(1,3);
    MatrixXd pose_act1_l(1,3);
    Plan_Res_MulDOF act1_r;
    Plan_Res_MulDOF act1_l; 
    int n1;

    VectorXd t_act2_l(4);
    VectorXd phi_act2_l(3); 
    MatrixXd pos_act2_l(3,3);
    MatrixXd pose_act2_l(3,3);
    Plan_Res_MulDOF act2_l;
    Plan_Res_MulDOF act2_r;
    int n2;

    VectorXd t_act3(2);
    VectorXd phi_act3_l(1);
    MatrixXd pos_act3_l(1,3);
    MatrixXd pose_act3_l(1,3);
    Plan_Res_MulDOF act3_l;
    Plan_Res_MulDOF act3_r;
    int n3;

    int n_put;
    MatrixXd pos_putdown(3,3);
    pos_putdown.row(0) << 0.45, -0.47, -0.24;
    pos_putdown.row(1) << 0.37, -0.45, -0.24;
    pos_putdown.row(2) << 0.37, -0.38, -0.24;
    Vector3d pos_putdown_i;

    int pub_count;
    int detect_count;
    bool no_detect_flag;

    while(1){
        ROS_INFO(">>>>>>>>第%d次拿放>>>>>>>>\n",count+1);

        #pragma region GET_POS_MOTOR
            ROS_INFO("获取当前关节位置...");
            if(is_test){
                jnt_end_r << 0, -8*PI/180, 0, 0, 0, 0, 0;
                jnt_end_l << 0, -8*PI/180, 0, 0, 0, 0, 0;
            }else{
                flag = false;
                while(!flag && ros::ok())
                {
                    // ROS_INFO("waiting for initial position");
                    usleep(1000);
                    ros::spinOnce();
                }
                ROS_INFO("left_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
                jnt_pos_current_l(0)*180/PI,jnt_pos_current_l(1)*180/PI,jnt_pos_current_l(2)*180/PI,jnt_pos_current_l(3)*180/PI,jnt_pos_current_l(4)*180/PI,jnt_pos_current_l(5)*180/PI,jnt_pos_current_l(6)*180/PI);
                ROS_INFO("right_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
                jnt_pos_current_r(0)*180/PI,jnt_pos_current_r(1)*180/PI,jnt_pos_current_r(2)*180/PI,jnt_pos_current_r(3)*180/PI,jnt_pos_current_r(4)*180/PI,jnt_pos_current_r(5)*180/PI,jnt_pos_current_r(6)*180/PI);

                jnt_end_r = jnt_pos_current_r;
                jnt_end_l = jnt_pos_current_l;
            }
        #pragma endregion

        #pragma region GET_POS_GRASP
            if(is_test){
                pos_grasp << 0.35, -0.2, -0.4;
            }else{
                detect_flag = false;
                no_detect_flag = false;
                detect_count = 0;
                ROS_INFO("获取目标点位置...");
                while(!detect_flag && ros::ok())
                {
                    ros::spinOnce();
                    usleep(1000);
                    // ROS_INFO("no detect");
                    if(detect_count > 3000){
                        no_detect_flag = true;
                        break;
                    }
                    detect_count++;
                }
                if(no_detect_flag){
                    ROS_WARN("未检测到目标!");
                    break;
                }
                if (container.size() >= 4) 
                {
                    
                    pos_grasp(0) = container[0];
                    pos_grasp(1) = -container[1];
                    pos_grasp(2) = container[2];
                    
                    container.clear();
                }
                ROS_INFO("目标点位置：[%.4f, %.4f, %.4f]",pos_grasp(0),pos_grasp(1),pos_grasp(2));
                // pos_grasp += Vector3d(0.055, -0.035, 0.075) + Vector3d(0.03, -0.005, 0.03);
                pos_grasp += Vector3d(0.055, -0.035, 0.075) + Vector3d(0.03, -0.015, 0.02);
            }
        #pragma endregion

        #pragma region ACTION1
            /*  
            1、抓取水果
                - 若是第一次抓取，左手dmp规划到预备抓取位置，否则左手直接插值到预备抓取位置，右手不动
                - 左手移动到抓取位置，右手不动
            */
            act1_done_flag = 0;

            pos_grasp_pre << 0.35, pos_grasp(1)-0.05, pos_grasp(2)+0.08;

            /*
            移动到预备抓取位置
            */
            pose_grasp_pre = Vector3d(PI/4, PI/12, -PI/2);

            t_act1_pre << 0, 4;
            phi_act1_pre << -PI/3;

            pos_act1_pre_l.row(0) = pos_grasp_pre;
            pose_act1_pre_l.row(0) = pose_grasp_pre;

            if(count == 0){
                act1_pre_l = dmpLib.action1_dmp(pos_grasp_pre,pose_grasp_pre,dt/1.5);
            }else{
                act1_pre_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act1_pre,pos_act1_pre_l,pose_act1_pre_l,phi_act1_pre,dt);
            }
            if(act1_pre_l.error_flag){
                ROS_ERROR("移动到预备抓取位置失败(第%d次抓取)!",count+1);
                return 0;
            }
            n1_pre = act1_pre_l.t.size();
            act1_pre_r = interLib.stay_still(jnt_end_r,n1_pre,dt);

            jnt_end_r = act1_pre_r.pos.row(n1_pre-1);
            jnt_end_l = act1_pre_l.pos.row(n1_pre-1);

            /*
            移动到抓取位置
            */
            
            t_act1 << 0, 3;
            phi_act1 << -PI/3;
            pos_act1_l.row(0) = pos_grasp;
            pose_act1_l.row(0) = pose_grasp;
                                 
            act1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act1,pos_act1_l,pose_act1_l,phi_act1,dt);
            if(act1_l.error_flag){
                ROS_ERROR("移动到抓取位置失败(第%d次抓取)!",count+1);
                return 0;
            }
            n1 = act1_l.t.size();
            act1_r = interLib.stay_still(jnt_end_r,n1,dt);

            jnt_end_r = act1_r.pos.row(n1-1);
            jnt_end_l = act1_l.pos.row(n1-1);

            act1_r = adjustFunc.traj_splic(act1_pre_r,act1_r);
            act1_l = adjustFunc.traj_splic(act1_pre_l,act1_l);

            ROS_INFO("<--阶段一开始(第%d次抓取)-->",count+1);
            dataSave.save2txt(act1_r,act1_l,fpWrite);
            ros::spinOnce();
            moveLib.move(act1_r,act1_l,ctrl_mode);

            /*
            抓取手势
            */
            pub_count = 0;
            grasp_msg.data = 10;
            while(pub_count < 100)
            {   
                grasp_pub_l.publish(grasp_msg);
                pub_count++;
            }
            sleep(2);
            ROS_INFO("手部抓取[手势%d]成功",grasp_msg.data); 

            ROS_INFO("<--阶段一结束(第%d次抓取)-->",count+1);
            act1_done_flag = 1;
        #pragma endregion

        #pragma region ACTION2
            /*
            2、放置物体到托盘
                - 左手抬起
                - 向左侧移动到托盘位置
                - 左手释放
            */
            act2_enable_flag = 1;
            if(!act1_done_flag || !act2_enable_flag){
                return 0;
            }

            n_put = count % 3;
            pos_putdown_i = pos_putdown.row(n_put);

            t_act2_l << 0, 3, 6, 10;
            phi_act2_l << -PI/4, -PI/4, -PI/3;
            pos_act2_l.row(0) = pos_grasp_pre;
            pos_act2_l.row(1) = pos_putdown_i + Vector3d(0, 0, 0.1);
            pos_act2_l.row(2) = pos_putdown_i;
            
            pose_act2_l.row(0) = Vector3d(PI/4, PI/12, -PI/2);
            pose_act2_l.row(1) = Vector3d(PI/2.7, PI/20, -PI/2);
            pose_act2_l.row(2) = Vector3d(PI/2.5, PI/6, -PI/2);
            
            act2_l = interPlan.cub_spline_inter_mulDOF(jnt_end_l,t_act2_l,pos_act2_l,pose_act2_l,phi_act2_l,MatrixXd::Zero(7,2),dt);
            if(act2_l.error_flag){
                ROS_ERROR("移动到放置位置失败(第%d次抓取)!",count+1);
                return 0;
            }
            n2 = act2_l.t.size();
            act2_r = interLib.stay_still(jnt_end_r,n2,dt);
            
            jnt_end_r = act2_r.pos.row(n2-1);
            jnt_end_l = act2_l.pos.row(n2-1);

            ROS_INFO("<--阶段二开始(第%d次抓取)-->",count+1);
            dataSave.save2txt(act2_r,act2_l,fpWrite);
            ros::spinOnce();
            moveLib.move(act2_r,act2_l,ctrl_mode);

            /*
            松手
            */
            pub_count = 0;
            grasp_msg.data = 2;
            while(pub_count < 100)
            {   
                grasp_pub_l.publish(grasp_msg);
                pub_count++;
            }
            sleep(2);
            ROS_INFO("手部释放[手势%d]成功",grasp_msg.data);

            ROS_INFO("<--阶段二结束(第%d次抓取)-->",count+1);
        #pragma endregion

        #pragma region ACTION3
            /*
            4、左手抬起
            */
            act3_enable_flag = 1;
            if(!act3_enable_flag){
                return 0;
            }

            t_act3 << 0, 4;
            phi_act3_l << -PI/4;
            pos_act3_l.row(0) = pos_act2_l.row(1);
            pose_act3_l.row(0) = pose_act2_l.row(1);

            act3_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3,pos_act3_l,pose_act3_l,phi_act3_l,dt);
            if(act3_l.error_flag){
                ROS_ERROR("左手抬起失败(第%d次抓取)!",count+1);
                return 0;
            }
            n3 = act3_l.t.size();
            act3_r = interLib.stay_still(jnt_end_r,n3,dt);

            jnt_end_r = act3_r.pos.row(n3-1);
            jnt_end_l = act3_l.pos.row(n3-1);

            ROS_INFO("<--阶段三开始(第%d次抓取)-->",count+1);
            dataSave.save2txt(act3_r,act3_l,fpWrite);
            ros::spinOnce();
            moveLib.move(act3_r,act3_l,ctrl_mode);
            ROS_INFO("<--阶段三结束(第%d次抓取)-->\n",count+1);
        #pragma endregion
        count++;
        container.clear();

    }

    if(count){
        #pragma region ACTION4
            /*
            5、回归初始位置
            */
            act4_enable_flag = 1;
            if(!act4_enable_flag){
                return 0;
            }
            
            Vector3d pos_standby = Vector3d(0.4, -0.25, -0.15);
            Vector3d pose_standby = Vector3d(PI/4, 0, -PI/2);

            VectorXd t_act4(2);
            t_act4 << 0, 4;
            VectorXd phi_act4(1);
            phi_act4 << -PI/2;
            MatrixXd pos_act4(1,3);
            pos_act4.row(0) = pos_standby;
            MatrixXd pose_act4(1,3);
            pose_act4.row(0) = pose_standby;

            Plan_Res_MulDOF act4_phase1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act4,pos_act4,pose_act4,phi_act4,dt);
            if(act4_phase1_l.error_flag){
                ROS_ERROR("回归初始位置第一阶段规划失败!");
                return 0;
            }

            Plan_Res_MulDOF act4_phase2 = dmpLib.action1_dmp(pos_standby,pose_standby,dt/1.5,1,true,phi_act4(0));
            if(act4_phase2.error_flag){
                ROS_ERROR("回归初始位置第二阶段规划失败!");
                return 0;
            }
            int n_dmp = act4_phase2.t.size();
            MatrixXd pos_act4_dmp(n_dmp,7);
            MatrixXd vel_act4_dmp(n_dmp,7);
            MatrixXd acc_act4_dmp(n_dmp,7);
            for(int i=0;i<n_dmp;i++){
                pos_act4_dmp.row(i) = act4_phase2.pos.row(n_dmp-1-i);
                vel_act4_dmp.row(i) = act4_phase2.vel.row(n_dmp-1-i);
                acc_act4_dmp.row(i) = act4_phase2.acc.row(n_dmp-1-i);
            }
            Plan_Res_MulDOF act4_phase2_inv;
            act4_phase2_inv.pos = pos_act4_dmp;
            act4_phase2_inv.vel = vel_act4_dmp;
            act4_phase2_inv.acc = acc_act4_dmp;
            act4_phase2_inv.t = act4_phase2.t;

            Plan_Res_MulDOF act4_l = adjustFunc.traj_splic(act4_phase1_l,act4_phase2_inv);
            int n4 = act4_l.t.rows();
            Plan_Res_MulDOF act4_r = interLib.stay_still(jnt_end_r,n4,dt);

            ROS_INFO("开始回归初始位置...");
            dataSave.save2txt(act4_r,act4_l,fpWrite);
            ros::spinOnce();
            moveLib.move(act4_r,act4_l,ctrl_mode);
            ROS_INFO("已回归初始位置\n");

        #pragma endregion
    }
    

    fclose(fpWrite);
    return 0;
}


// 目标点位置：[0.3106, -0.2047, -0.3792]、[0.3709, -0.1340, -0.3793]、[0.3173, -0.0854, -0.3736]
// 目标点位置：[0.3119, -0.2138, -0.3763]、[0.3843, -0.1386, -0.3770]、[0.3232, -0.0890, -0.3729]
