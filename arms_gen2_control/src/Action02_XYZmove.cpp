#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include "sensor_msgs/JointState.h"

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

double dt = 0.0006;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
DATA_PROC_FUNC::DATA_PUB dataPub;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
ACT_LIB::MOVE moveLib;


FILE *fpWrite=fopen("res.txt","w");

float left_init_pos[7] = {0.0};
float right_init_pos[7] = {0.0};
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        left_init_pos[i] = msg->position[i];
        right_init_pos[i] = msg->position[i+7];
    }
    return;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action02_XYZmove");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);

    /*
    set/get current position
    */ 
    VectorXd jnt_pos_current_r(7);
    VectorXd jnt_pos_current_l(7);
    int istest = 0;
    if(istest){
        jnt_pos_current_r << PI*15.3/180, -PI*20.5/180, PI*25.6/180, PI*66.5/180, PI*23/180, PI*17.9/180, PI*25.6/180;
        jnt_pos_current_l << PI*15.3/180, -PI*20.5/180, PI*25.6/180, PI*66.5/180, PI*23/180, PI*17.9/180, PI*25.6/180;
    }else{
        while(!flag && ros::ok())
        {
            ROS_INFO("waiting for initial position");
            usleep(1000);
            ros::spinOnce();
        }
        ROS_INFO("left_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        left_init_pos[0]*180/PI,left_init_pos[1]*180/PI,left_init_pos[2]*180/PI,left_init_pos[3]*180/PI,left_init_pos[4]*180/PI,left_init_pos[5]*180/PI,left_init_pos[6]*180/PI);
        ROS_INFO("right_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        right_init_pos[0]*180/PI,right_init_pos[1]*180/PI,right_init_pos[2]*180/PI,right_init_pos[3]*180/PI,right_init_pos[4]*180/PI,right_init_pos[5]*180/PI,right_init_pos[6]*180/PI);

        jnt_pos_current_r << left_init_pos[0], left_init_pos[1], left_init_pos[2], left_init_pos[3], left_init_pos[4], left_init_pos[5], left_init_pos[6];
        jnt_pos_current_l << right_init_pos[0], right_init_pos[1], right_init_pos[2], right_init_pos[3], right_init_pos[4], right_init_pos[5], right_init_pos[6];
    }

    double phi_current_r = jnt_pos_current_r(2);
    double phi_current_l = jnt_pos_current_l(2);

    KineRes kine_res_r = kineFunc.fkine(jnt_pos_current_r);
    KineRes kine_res_l = kineFunc.fkine(jnt_pos_current_l);

    Vector3d cart_pos_current_r = kine_res_r.cart_end_position;
    Vector3d cart_pos_current_l = kine_res_l.cart_end_position;
    Vector3d pose_current_r;
    Vector3d pose_current_l;
    pose_current_r << PI/2 - kine_res_r.eul_r, kine_res_r.eul_p, kine_res_r.eul_y;
    pose_current_l << PI/2 - kine_res_l.eul_r, kine_res_l.eul_p, kine_res_l.eul_y;

    /*
    get bais position
    */ 
    Vector3d bais_r;
    Vector3d bais_l;
    switch (argc)
    {
        case 1:
            bais_r(0) = 0;
            bais_r(1) = 0;
            bais_r(2) = 0;
            bais_l(0) = 0;
            bais_l(1) = 0;
            bais_l(2) = 0;
            break;
        case 2:
            bais_r(0) = atof(argv[1]);
            bais_r(1) = 0;
            bais_r(2) = 0;
            bais_l(0) = 0;
            bais_l(1) = 0;
            bais_l(2) = 0;
            break;
        case 3:
            bais_r(0) = atof(argv[1]);
            bais_r(1) = atof(argv[2]);
            bais_r(2) = 0;
            bais_l(0) = 0;
            bais_l(1) = 0;
            bais_l(2) = 0;
            break;
        case 4:
            bais_r(0) = atof(argv[1]); 
            bais_r(1) = atof(argv[2]);
            bais_r(2) = atof(argv[3]);
            bais_l(0) = 0;
            bais_l(1) = 0;
            bais_l(2) = 0;
            break;
        case 5:
            bais_r(0) = atof(argv[1]);
            bais_r(1) = atof(argv[2]);
            bais_r(2) = atof(argv[3]);
            bais_l(0) = atof(argv[4]);
            bais_l(1) = 0;
            bais_l(2) = 0;
            break;
        case 6:
            bais_r(0) = atof(argv[1]);
            bais_r(1) = atof(argv[2]);
            bais_r(2) = atof(argv[3]);
            bais_l(0) = atof(argv[4]);
            bais_l(1) = atof(argv[5]);
            bais_l(2) = 0;
        default:
            bais_r(0) = atof(argv[1]);
            bais_r(1) = atof(argv[2]);
            bais_r(2) = atof(argv[3]);
            bais_l(0) = atof(argv[4]);
            bais_l(1) = atof(argv[5]);
            bais_l(2) = atof(argv[6]);
    }

    /*
    traj_current2standby
    */
    double max_bais_r = max(bais_r(0),max(bais_r(1),bais_r(2)));
    double max_bais_l = max(bais_l(0),max(bais_l(1),bais_l(2)));
    // double t_end = (max(max_bais_r,max_bais_l)+0.001)*500;
    double t_end = 4;

    VectorXd t_act(2);
    VectorXd phi_act_r(1);
    VectorXd phi_act_l(1);
    t_act << 0, t_end;
    phi_act_r << phi_current_r;
    phi_act_l << phi_current_l;

    MatrixXd position_action_r(1,3);
    MatrixXd pose_action_r(1,3);
    position_action_r.row(0) = cart_pos_current_r + bais_r;
    pose_action_r.row(0) = pose_current_r;

    MatrixXd position_action_l(1,3);
    MatrixXd pose_action_l(1,3);
    position_action_l.row(0) = cart_pos_current_l + bais_l;
    pose_action_l.row(0) = pose_current_l;
    
    Plan_Res_MulDOF act_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r,t_act,position_action_r,pose_action_r,phi_act_r,dt);
    if(act_r.error_flag){
        ROS_ERROR("ACTION_R PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF act_l = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_l,t_act,position_action_l,pose_action_l,phi_act_l,dt);
    if(act_l.error_flag){
        ROS_ERROR("ACTION_L PLANNING FAILED!");
        return 0;
    }

    dataSave.save2txt(act_r,act_l,fpWrite);
    moveLib.move(act_r,act_l,3);
    fclose(fpWrite);
    
    return 0;
}
