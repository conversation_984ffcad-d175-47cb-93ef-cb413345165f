/*
叠毛巾
    1、抓取边角
    2、拖拽，调整第一次折叠后离身体的距离
    3、横向折叠，松手
    4、右手抓近右边角，左手按住毛巾左侧
    5、纵向折叠，右手到松手点后，左手先撤出，右手再松手
    6、右手按住毛巾，拖至中间
*/ 
#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592653589

double dt = 0.0005;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::INTER interLib;
ACT_LIB::MOVE moveLib;

std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }
    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action07_fold_towel");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

    /*
    move_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
    CTRL_MODE ctrl_mode = robot_ctrl;
    // CTRL_MODE ctrl_mode = stop;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    Vector3d bias_hand;
    bias_hand << 0.035, 0.07, 0.035;

    double L_towel = 0.34;

    #pragma region ACTION1
        /*  
        1、抓取边角
            - 双手dmp规划到预备抓取点
            - 手预备抓取
            - 移动到抓取点
            - 手抓取
        */
        int act1_done_flag = 0;

        /*
        获取抓取点
        */ 

        Vector3d pos_grasp_1st_r;
        Vector3d pose_grasp_1st_r;
        Vector3d pos_grasp_1st_l;
        Vector3d pose_grasp_1st_l;

        #pragma region GET POSITIONS BASED ON VISION
            ROS_INFO("Get postions based on vision");
            while(!detect_flag && ros::ok())
            {
                ros::spinOnce();
                usleep(1000);
                ROS_INFO("no detect");
            }
            /* 接受两个目标物体的位置信息 */
            if (container.size() >= 6) 
            {
                pos_grasp_1st_l(0) = container[0];
                pos_grasp_1st_l(1) = -container[1];
                pos_grasp_1st_l(2) = container[2];
                pos_grasp_1st_r(0) = container[3];
                pos_grasp_1st_r(1) = container[4];
                pos_grasp_1st_r(2) = container[5];
                container.erase(container.begin(), container.begin() + 6);
            }
            cout << "cart_pos_grasp_l: " << pos_grasp_1st_l(0) << ", " << pos_grasp_1st_l(1) << ", " << pos_grasp_1st_l(2) << endl;
            cout << "cart_pos_grasp_r: " << pos_grasp_1st_r(0) << ", " << pos_grasp_1st_r(1) << ", " << pos_grasp_1st_r(2) << endl;
            // cart_pos_grasp_l = cart_pos_grasp_l + Vector3d(0.08, -0.085, 0.025);
            // cart_pos_grasp_r = cart_pos_grasp_r + Vector3d(0.06, -0.045, -0.01);
            pos_grasp_1st_l = pos_grasp_1st_l + Vector3d(0.051, -0.053, 0.105) + Vector3d(-0.015, 0, 0);
            pos_grasp_1st_r = pos_grasp_1st_r + Vector3d(0.002, -0.015, 0.078) + Vector3d(-0.015, 0, 0);
        #pragma endregion
 
        pose_grasp_1st_r << PI/4, PI/12, -PI/2;
        pose_grasp_1st_l << PI/4, PI/12, -PI/2;

        Vector3d pos_grasp_1st_pre_r;
        pos_grasp_1st_pre_r << 0.35, pos_grasp_1st_r(1), pos_grasp_1st_r(2)+0.06;

        Vector3d pos_grasp_1st_pre_l;
        pos_grasp_1st_pre_l << 0.35, pos_grasp_1st_l(1), pos_grasp_1st_l(2)+0.06;

        /*
        移动到预备抓取位置
        */
        Plan_Res_MulDOF act1_pre_r;
        Plan_Res_MulDOF act1_pre_l;
        act1_pre_r = dmpLib.action1_dmp(pos_grasp_1st_pre_r,pose_grasp_1st_r,dt);
        if(act1_pre_r.error_flag){
            ROS_ERROR("ACTION1_INIT2GRASP_PRE_R PLANNING FAILED!");
            return 0;
        }
        act1_pre_l = dmpLib.action1_dmp(pos_grasp_1st_pre_l,pose_grasp_1st_l,dt);
        if(act1_pre_l.error_flag){
            ROS_ERROR("ACTION1_INIT2GRASP_PRE_L PLANNING FAILED!");
            return 0;
        }

        ROS_INFO("ACTION1 STARTING!");
        ROS_INFO("- ACTION1_INIT2GRASP_PRE STARTING!");
        dataSave.save2txt(act1_pre_r,act1_pre_l,fpWrite);
        moveLib.move(act1_pre_r,act1_pre_l,ctrl_mode);
        ROS_INFO("- ACTION1_INIT2GRASP_PRE FINISHED!");

        int n1_pre = act1_pre_r.t.size();
        jnt_end_r = act1_pre_r.pos.row(n1_pre-1);
        jnt_end_l = act1_pre_l.pos.row(n1_pre-1);

        /*
        预备抓取手势
        */
        grasp_msg.data = 7;//抓取
        while(!grasp_finshed_l && !grasp_finshed_r && ros::ok())
        {
            // ROS_INFO("hand is grasping!"); 
            grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            ros::spinOnce();
            usleep(1000);
        }
        sleep(3);
        ROS_INFO("hand grasp sucessed!"); 

        /*
        移动到抓取位置
        */
        VectorXd t_act1(2);
        t_act1 << 0, 3;
        VectorXd phi_act1(1);
        phi_act1 << -PI/4;

        MatrixXd pos_act1_r(1,3);
        pos_act1_r.row(0) = pos_grasp_1st_r;
        MatrixXd pose_act1_r(1,3);
        pose_act1_r.row(0) = pose_grasp_1st_r;
        
        MatrixXd pos_act1_l(1,3);
        pos_act1_l.row(0) = pos_grasp_1st_l;
        MatrixXd pose_act1_l(1,3);
        pose_act1_l.row(0) = pose_grasp_1st_l;

        Plan_Res_MulDOF act1_r;
        Plan_Res_MulDOF act1_l;                      
        act1_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act1,pos_act1_r,pose_act1_r,phi_act1,dt);
        if(act1_r.error_flag){
            ROS_ERROR("ACTION1_PRE2GRASP_R PLANNING FAILED!");
            return 0;
        }
        act1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act1,pos_act1_l,pose_act1_l,phi_act1,dt);
        if(act1_l.error_flag){
            ROS_ERROR("ACTION1_PRE2GRASP_L PLANNING FAILED!");
            return 0;
        }

        int n1 = act1_r.t.size();
        jnt_end_r = act1_r.pos.row(n1-1);
        jnt_end_l = act1_l.pos.row(n1-1);
        
        ROS_INFO("- ACTION1_PRE2GRASP STARTING!");
        dataSave.save2txt(act1_r,act1_l,fpWrite);
        moveLib.move(act1_r,act1_l,ctrl_mode);
        ROS_INFO("- ACTION1_PRE2GRASP FINISHED!");

        /*
        抓取手势
        */
        int count = 0;
        grasp_msg.data = 8;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("hand grasp sucessed!"); 

        ROS_INFO("ACTION1 FINISHED!\n");
        act1_done_flag = 1;
    #pragma endregion

    #pragma region ACTION2
        /*
        2、第一次折叠
            - 向上后方移动，将毛巾最远边拖至原中线位置
            - 折叠
        */
        int act2_enable_flag = 1;
        if(!act1_done_flag || !act2_enable_flag){
            return 0;
        }

        VectorXd t_act2(5);
        t_act2 << 0, 4, 6, 9, 13;
        VectorXd phi_act2(4);
        phi_act2 << -PI/2, -PI/2, -PI/4, -PI/4;

        MatrixXd pos_act2_r(4,3);
        // pos_act2_r.row(0) = pos_grasp_1st_r + Vector3d(-L_towel/2*sin(PI/8), 0, L_towel/2*cos(PI/8));
        // pos_act2_r.row(1) = pos_grasp_1st_r + Vector3d(L_towel/2-0.1*sin(PI/8), 0.02, 0.1*cos(PI/8));
        // pos_act2_r.row(0) = pos_grasp_1st_r + Vector3d(-0.0, -0.012, 0.24);
        // pos_act2_r.row(1) = pos_grasp_1st_r + Vector3d(0.15, -0.007, 0.14);
        // pos_act2_r.row(2) = pos_grasp_1st_r + Vector3d(0.11, -0.00, 0.10);
        // pos_act2_r.row(0) = Vector3d(0.28, -0.19, -0.120);
        // pos_act2_r.row(1) = Vector3d(0.31, -0.155, -0.175);
        // pos_act2_r.row(2) = Vector3d(0.55, -0.15, -0.175);
        // pos_act2_r.row(3) = Vector3d(0.46, -0.18, -0.255);
        pos_act2_r.row(0) = Vector3d(0.44, -0.15, -0.120);
        pos_act2_r.row(1) = Vector3d(0.49, -0.155, -0.175);
        pos_act2_r.row(2) = Vector3d(0.26, -0.180, -0.175);
        pos_act2_r.row(3) = Vector3d(0.415, -0.175, -0.260);
        MatrixXd pose_act2_r(4,3);
        // pose_act2_r.row(0) = Vector3d(PI/6,-PI/4,-PI/2);
        // pose_act2_r.row(1) = Vector3d(PI/6,PI/6,-PI/2);
        // pose_act2_r.row(2) = Vector3d(PI/3,PI/6,-PI/2);
        // pose_act2_r.row(3) = Vector3d(PI/3,PI/12,-PI/2);
        pose_act2_r.row(0) = Vector3d(PI/3,-PI/4,-PI/2);
        pose_act2_r.row(1) = Vector3d(PI/3,PI/6,-PI/2);
        pose_act2_r.row(2) = Vector3d(PI/8,PI/12,-PI/2);
        pose_act2_r.row(3) = Vector3d(PI/2.5,PI/4,-PI/2);
        // pos_act2_r.row(0) -= kineFunc.biasHand(pose_act2_r.row(0),bias_hand);
        // pos_act2_r.row(1) -= kineFunc.biasHand(pose_act2_r.row(1),bias_hand);

        MatrixXd pos_act2_l(4,3);
        // pos_act2_l.row(0) = pos_grasp_1st_l + Vector3d(-L_towel/2*sin(PI/8), 0, L_towel/2*cos(PI/8));
        // pos_act2_l.row(1) = pos_grasp_1st_l + Vector3d(L_towel/2-0.1*sin(PI/8), 0.02, 0.1*cos(PI/8));
        // pos_act2_l.row(0) = pos_grasp_1st_l + Vector3d(-0.0, -0.012, 0.25);
        // pos_act2_l.row(1) = pos_grasp_1st_l + Vector3d(0.15, -0.007, 0.14);
        // pos_act2_l.row(2) = pos_grasp_1st_l + Vector3d(0.11, -0.00, 0.10);
        // pos_act2_l.row(0) = Vector3d(0.28, -0.19, -0.100);
        // pos_act2_l.row(1) = Vector3d(0.31, -0.155, -0.155);
        // pos_act2_l.row(2) = Vector3d(0.55, -0.15, -0.155);
        // pos_act2_l.row(3) = Vector3d(0.46, -0.18, -0.235);
        pos_act2_l.row(0) = Vector3d(0.45, -0.15, -0.100);
        pos_act2_l.row(1) = Vector3d(0.50, -0.155, -0.156);
        pos_act2_l.row(2) = Vector3d(0.27, -0.180, -0.156);
        pos_act2_l.row(3) = Vector3d(0.425, -0.205, -0.240);
        MatrixXd pose_act2_l(4,3);
        // pose_act2_l.row(0) = Vector3d(PI/6,-PI/4,-PI/2);
        // pose_act2_l.row(1) = Vector3d(PI/6,PI/6,-PI/2);
        // pose_act2_l.row(2) = Vector3d(PI/3,PI/6,-PI/2);
        // pose_act2_l.row(3) = Vector3d(PI/3,PI/12,-PI/2);
        pose_act2_l.row(0) = Vector3d(PI/3,-PI/4,-PI/2);
        pose_act2_l.row(1) = Vector3d(PI/3,PI/6,-PI/2);
        pose_act2_l.row(2) = Vector3d(PI/8,PI/12,-PI/2);
        pose_act2_l.row(3) = Vector3d(PI/2.5,PI/4,-PI/2);
        // pos_act2_l.row(0) -= kineFunc.biasHand(pose_act2_l.row(0),bias_hand);
        // pos_act2_l.row(1) -= kineFunc.biasHand(pose_act2_l.row(1),bias_hand);

        // VectorXd t_act2(4);
        // t_act2 << 0, 4, 6, 9;
        // VectorXd phi_act2(3);
        // phi_act2 << -PI/2, -PI/2, -PI/4;

        // MatrixXd pos_act2_r(3,3);
        // pos_act2_r.row(0) = Vector3d(0.28, -0.19, -0.150);
        // pos_act2_r.row(1) = Vector3d(0.54, -0.15, -0.175);
        // pos_act2_r.row(2) = Vector3d(0.46, -0.18, -0.235);
        // MatrixXd pose_act2_r(3,3);
        // pose_act2_r.row(0) = Vector3d(PI/6,-PI/4,-PI/2);
        // pose_act2_r.row(1) = Vector3d(PI/3,PI/6,-PI/2);
        // pose_act2_r.row(2) = Vector3d(PI/3,PI/12,-PI/2);

        // MatrixXd pos_act2_l(3,3);
        // pos_act2_l.row(0) = Vector3d(0.28, -0.19, -0.130);
        // pos_act2_l.row(1) = Vector3d(0.54, -0.15, -0.155);
        // pos_act2_l.row(2) = Vector3d(0.46, -0.18, -0.215);
        // MatrixXd pose_act2_l(3,3);
        // pose_act2_l.row(0) = Vector3d(PI/6,-PI/4,-PI/2);
        // pose_act2_l.row(1) = Vector3d(PI/3,PI/6,-PI/2);
        // pose_act2_l.row(2) = Vector3d(PI/3,PI/12,-PI/2);

        Plan_Res_MulDOF act2_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act2,pos_act2_r,pose_act2_r,phi_act2,dt);
        if(act2_r.error_flag){
            ROS_ERROR("ACTION2_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act2,pos_act2_l,pose_act2_l,phi_act2,dt);
        if(act2_l.error_flag){
            ROS_ERROR("ACTION2_L PLANNING FAILED!");
            return 0;
        }

        int n2 = act2_r.t.size();
        jnt_end_r = act2_r.pos.row(n2-1);
        jnt_end_l = act2_l.pos.row(n2-1);
        
        ROS_INFO("ACTION2 STARTING!");
        dataSave.save2txt(act2_r,act2_l,fpWrite);
        moveLib.move(act2_r,act2_l,ctrl_mode);
        

        /*
        松手
        */
        count = 0;
        grasp_msg.data = 7;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("hand release sucessed!");

        ROS_INFO("ACTION2 FINISHED!\n");

    #pragma endregion

    #pragma region ACTION3
        /*
        3、第二次折叠
            - 右手抓近右边角，左手按住毛巾左侧
            - 纵向折叠，右手到松手点后，左手先撤出，右手再松手
        */
        int act3_enable_flag = 0;
        if(!act3_enable_flag){
            return 0;
        }

        /*
        获取抓取点
        */ 
        Vector3d pos_grasp_2nd_r;
        Vector3d pose_grasp_2nd_r;
        pose_grasp_2nd_r << PI/4, PI/12, -PI/2;

        Vector3d pos_grasp_2nd_l;
        Vector3d pose_grasp_2nd_l;
        pose_grasp_2nd_l << PI/4, PI/12, -PI/2;

        while(!flag && ros::ok())
        {
            ROS_INFO("waiting for initial position");
            usleep(1000);
            ros::spinOnce();
        }
        // jnt_end_l << left_init_pos[0], left_init_pos[1], left_init_pos[2], left_init_pos[3], left_init_pos[4], left_init_pos[5], left_init_pos[6];
        // jnt_end_r << right_init_pos[0], right_init_pos[1], right_init_pos[2], right_init_pos[3], right_init_pos[4], right_init_pos[5], right_init_pos[6];

        #pragma region GET POSITIONS BASED ON VISION
            ROS_INFO("Get postions based on vision");
            detect_flag = false;
            while(!detect_flag && ros::ok())
            {
                ros::spinOnce();
                usleep(1000);
                ROS_INFO("no detect");
            }
            /* 接受两个目标物体的位置信息 */
            if (container.size() >= 6) 
            {
                pos_grasp_2nd_l(0) = container[0];
                pos_grasp_2nd_l(1) = -container[1];
                pos_grasp_2nd_l(2) = container[2];
                pos_grasp_2nd_r(0) = container[3];
                pos_grasp_2nd_r(1) = container[4];
                pos_grasp_2nd_r(2) = container[5];
                container.erase(container.begin(), container.begin() + 6);
            }
            cout << "cart_pos_grasp_l: " << pos_grasp_2nd_l(0) << ", " << pos_grasp_2nd_l(1) << ", " << pos_grasp_2nd_l(2) << endl;
            cout << "cart_pos_grasp_r: " << pos_grasp_2nd_r(0) << ", " << pos_grasp_2nd_r(1) << ", " << pos_grasp_2nd_r(2) << endl;
            // cart_pos_grasp_l = cart_pos_grasp_l + Vector3d(0.08, -0.085, 0.025);
            // cart_pos_grasp_r = cart_pos_grasp_r + Vector3d(0.06, -0.045, -0.01);
            // pos_grasp_2nd_l = pos_grasp_2nd_l + Vector3d(0.075, -0.032, 0.115) + Vector3d(-0.015, 0, 0);
            // pos_grasp_2nd_r = pos_grasp_2nd_r + Vector3d(0.040, -0.015, 0.090) + Vector3d(-0.025, -0.01, 0);
            // pos_grasp_2nd_l = pos_grasp_2nd_l + Vector3d(0.051, -0.059, 0.100) + Vector3d(-0.015, 0, 0);
            // pos_grasp_2nd_r = pos_grasp_2nd_r + Vector3d(0.013, -0.017, 0.078) + Vector3d(-0.015, 0, 0);
            pos_grasp_1st_l = pos_grasp_1st_l + Vector3d(0.051, -0.053, 0.105) + Vector3d(-0.015, 0, 0);
            pos_grasp_1st_r = pos_grasp_1st_r + Vector3d(0.002, -0.015, 0.078) + Vector3d(-0.015, 0, 0);

            // pos_grasp_2nd_r = Vector3d(0.34, -0.16, -0.430449);
            // pos_grasp_2nd_l = Vector3d(0.31, -0.16, -0.431511);
        #pragma endregion
        

        /*
        左右手移动到抓取点
        */
        VectorXd t_act3_p1(3);
        t_act3_p1 << 0, 4, 7;
        VectorXd phi_act3_p1_r(2);
        VectorXd phi_act3_p1_l(2);
        phi_act3_p1_r << -PI/4, -PI/4;
        phi_act3_p1_l << -PI/4, -PI/4;

        MatrixXd pos_act3_p1_r(2,3);
        pos_act3_p1_r.row(0) = pos_grasp_2nd_r + Vector3d(-0.02, 0, 0.06);
        pos_act3_p1_r.row(1) = pos_grasp_2nd_r;
        MatrixXd pose_act3_p1_r(2,3);
        pose_act3_p1_r.row(0) = pose_grasp_2nd_r;
        pose_act3_p1_r.row(1) = pose_grasp_2nd_r;

        MatrixXd pos_act3_p1_l(2,3);
        pos_act3_p1_l.row(0) = pos_grasp_2nd_l + Vector3d(-0.02, 0, 0.06);
        pos_act3_p1_l.row(1) = pos_grasp_2nd_l;
        MatrixXd pose_act3_p1_l(2,3);
        pose_act3_p1_l.row(0) = pose_grasp_2nd_l;
        pose_act3_p1_l.row(1) = pose_grasp_2nd_l;

        Plan_Res_MulDOF act3_p1_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p1,pos_act3_p1_r,pose_act3_p1_r,phi_act3_p1_r,dt);
        if(act3_p1_r.error_flag){
            ROS_ERROR("ACTION3_PHASE1_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p1,pos_act3_p1_l,pose_act3_p1_l,phi_act3_p1_l,dt);
        if(act3_p1_l.error_flag){
            ROS_ERROR("ACTION3_PHASE1_L PLANNING FAILED!");
            return 0;
        }

        int n3_p1 = act3_p1_r.t.size();
        jnt_end_r = act3_p1_r.pos.row(n3_p1-1);
        jnt_end_l = act3_p1_l.pos.row(n3_p1-1);

        ROS_INFO("- ACTION3_PHASE1 STARTING!");
        dataSave.save2txt(act3_p1_r,act3_p1_l,fpWrite);
        moveLib.move(act3_p1_r,act3_p1_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE1 FINISHED!");

        /*
        抓取
        */
        count = 0;
        grasp_msg.data = 8;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("hand grasp sucessed!");

        /*
        向右拖动毛巾
        */
        VectorXd t_act3_p2(2);
        t_act3_p2 << 0, 5;
        VectorXd phi_act3_p2_r(1);
        VectorXd phi_act3_p2_l(1);
        phi_act3_p2_r << -PI/2;
        phi_act3_p2_l << -PI/2;

        MatrixXd pos_act3_p2_r(1,3);
        pos_act3_p2_r.row(0) = pos_grasp_2nd_r + Vector3d(0.02, -0.16, 0.06);
        
        MatrixXd pose_act3_p2_r(1,3);
        pose_act3_p2_r.row(0) = Vector3d(PI/8, -PI/12, -PI/2);

        MatrixXd pos_act3_p2_l(1,3);
        pos_act3_p2_l.row(0) = pos_grasp_2nd_l + Vector3d(0.025, 0.16, 0.04);
        
        MatrixXd pose_act3_p2_l(1,3);
        pose_act3_p2_l.row(0) = Vector3d(PI/8, -PI/12, -PI/2);

        Plan_Res_MulDOF act3_p2_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p2,pos_act3_p2_r,pose_act3_p2_r,phi_act3_p2_r,dt);
        if(act3_p2_r.error_flag){
            ROS_ERROR("ACTION3_PHASE2_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p2,pos_act3_p2_l,pose_act3_p2_l,phi_act3_p2_l,dt);
        if(act3_p2_l.error_flag){
            ROS_ERROR("ACTION3_PHASE2_L PLANNING FAILED!");
            return 0;
        }

        int n3_p2 = act3_p2_r.t.size();
        jnt_end_r = act3_p2_r.pos.row(n3_p2-1);
        jnt_end_l = act3_p2_l.pos.row(n3_p2-1);

        ROS_INFO("- ACTION3_PHASE2 STARTING!");
        dataSave.save2txt(act3_p2_r,act3_p2_l,fpWrite);
        moveLib.move(act3_p2_r,act3_p2_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE2 FINISHED!");

        /*
        左手松开
        */
        count = 0;
        grasp_msg.data = 7;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            // grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("left hand release sucessed!");

        /*
        右手开始折叠操作
        */
        VectorXd t_act3_p3(3);
        t_act3_p3 << 0, 4, 8;
        VectorXd phi_act3_p3_r(2);
        VectorXd phi_act3_p3_l(2);
        phi_act3_p3_r << -PI/2, -PI/2;
        phi_act3_p3_l << -PI/2, -PI/2;

        MatrixXd pos_act3_p3_r(2,3);
        pos_act3_p3_r.row(0) = pos_grasp_2nd_r + Vector3d(0.02, 0.0, 0.1);
        pos_act3_p3_r.row(1) = pos_grasp_2nd_r + Vector3d(0.0, 0.17, 0.06);
        MatrixXd pose_act3_p3_r(2,3);
        pose_act3_p3_r.row(0) = Vector3d(PI/4, PI/12, -PI/2);
        pose_act3_p3_r.row(1) = Vector3d(PI/8, PI/12, -PI/2);

        MatrixXd pos_act3_p3_l(2,3);
        pos_act3_p3_l.row(0) = pos_grasp_2nd_l + Vector3d(0.02, -0.05, 0.05);
        pos_act3_p3_l.row(1) = pos_grasp_2nd_l + Vector3d(0.02, -0.05, 0.05);
        MatrixXd pose_act3_p3_l(2,3);
        pose_act3_p3_l.row(0) = Vector3d(PI/4, PI/12, -PI/2);
        pose_act3_p3_l.row(1) = Vector3d(PI/4, PI/12, -PI/2);

        Plan_Res_MulDOF act3_p3_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p3,pos_act3_p3_r,pose_act3_p3_r,phi_act3_p3_r,dt);
        if(act3_p3_r.error_flag){
            ROS_ERROR("ACTION3_PHASE3_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p3_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p3,pos_act3_p3_l,pose_act3_p3_l,phi_act3_p3_l,dt);
        if(act3_p3_l.error_flag){
            ROS_ERROR("ACTION3_PHASE3_L PLANNING FAILED!");
            return 0;
        }

        int n3_p3 = act3_p3_r.t.size();
        jnt_end_r = act3_p3_r.pos.row(n3_p3-1);
        jnt_end_l = act3_p3_l.pos.row(n3_p3-1);

        ROS_INFO("- ACTION3_PHASE3 STARTING!");
        dataSave.save2txt(act3_p3_r,act3_p3_l,fpWrite);
        moveLib.move(act3_p3_r,act3_p3_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE3 FINISHED!");

        /*
        右手松开
        */
        count = 0;
        grasp_msg.data = 7;
        while(count < 1000)
        {
            // grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("hand release sucessed!");


        ROS_INFO("ACTION3 FINISHED!\n");
    #pragma endregion

    #pragma region ACTION4
        /*
        4、毛巾拖到右手边
            - 右手按住毛巾中间
            - 移动
        */
        int act4_enable_flag = 0;
        if(!act4_enable_flag){
            return 0;
        }

        Vector3d pos_push;
        pos_push = Vector3d(pos_grasp_2nd_r(0),0.05-L_towel,pos_grasp_2nd_r(2)) + Vector3d(L_towel/4,3*L_towel/4,0);
        Vector3d pose_push1;
        Vector3d pose_push2;
        pose_push1 << PI/3,0,-PI/2;
        pose_push2 << PI/3,0,-PI/2;

        VectorXd t_act4(3);
        t_act4 << 0, 4, 10;
        VectorXd phi_act4(2);
        phi_act4 << -PI/2,-PI/2;
        MatrixXd pos_act4(2,3);
        pos_act4.row(0) = pos_push;
        pos_act4.row(1) = pos_push + Vector3d(0.0,-0.2,0);
        MatrixXd pose_act4(2,3);
        pose_act4.row(0) = pose_push1;
        pose_act4.row(1) = pose_push2;

        Plan_Res_MulDOF act4_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act4,pos_act4,pose_act4,phi_act4,dt);
        if(act4_r.error_flag){
            ROS_ERROR("ACTION3_PHASE3_R PLANNING FAILED!");
            return 0;
        }
        int n4 = act4_r.t.size();
        Plan_Res_MulDOF act4_l = interLib.stay_still(jnt_end_l,n4,dt);

        ROS_INFO("ACTION4 STARTING!");
        dataSave.save2txt(act4_r,act4_l,fpWrite);
        moveLib.move(act4_r,act4_l,ctrl_mode);
        ROS_INFO("ACTION4 FINISHED!\n");
    #pragma endregion

    fclose(fpWrite);
    return 0;
}