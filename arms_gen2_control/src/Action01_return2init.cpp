#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include "sensor_msgs/JointState.h"
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"
#include "robot_control/robot_control.h"
using namespace std;

#define PI 3.141592654

// double dt = 0.003;
// double dt = 0.001;
double tau = 1;

std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

DATA_PROC_FUNC::DATA_SAVE dataSave;
DATA_PROC_FUNC::DATA_PUB dataPub;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::ADJUST adjustFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
ACT_LIB::DMP dmpLib;
ACT_LIB::MOVE moveLib;

FILE *fpWrite=fopen("res.txt","w");

float left_init_pos[7] = {0.0};
float right_init_pos[7] = {0.0};
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        left_init_pos[i] = msg->position[i];
        right_init_pos[i] = msg->position[i+7];
    }
    return;
}
void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action01_return2init");
    ros::NodeHandle nh;

    DATA_PROC::Data_Pub dataPub2(false);

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);
    /*
    set/get current position
    */ 
    VectorXd jnt_pos_current_r(7);
    VectorXd jnt_pos_current_l(7);
    int istest = 0;
    if(istest){
        jnt_pos_current_r << PI*15.3/180, -PI*20.5/180, PI*25.6/180, PI*66.5/180, PI*23/180, PI*17.9/180, PI*25.6/180;
        jnt_pos_current_l << PI*15.3/180, -PI*20.5/180, PI*25.6/180, PI*66.5/180, PI*23/180, PI*17.9/180, PI*25.6/180;
    }else{
        ROS_INFO("waiting for initial position");
        while(!flag && ros::ok())
        {
            usleep(1000);
            ros::spinOnce();
        }
        ROS_INFO("left_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        left_init_pos[0]*180/PI,left_init_pos[1]*180/PI,left_init_pos[2]*180/PI,left_init_pos[3]*180/PI,left_init_pos[4]*180/PI,left_init_pos[5]*180/PI,left_init_pos[6]*180/PI);
        ROS_INFO("right_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        right_init_pos[0]*180/PI,right_init_pos[1]*180/PI,right_init_pos[2]*180/PI,right_init_pos[3]*180/PI,right_init_pos[4]*180/PI,right_init_pos[5]*180/PI,right_init_pos[6]*180/PI);

        jnt_pos_current_l << left_init_pos[0], left_init_pos[1], left_init_pos[2], left_init_pos[3], left_init_pos[4], left_init_pos[5], left_init_pos[6];
        jnt_pos_current_r << right_init_pos[0], right_init_pos[1], right_init_pos[2], right_init_pos[3], right_init_pos[4], right_init_pos[5], right_init_pos[6];
    }
       
    /*
    set standby position
    */ 
    Vector3d cart_pos_standby;
    cart_pos_standby << 0.4, -0.25, -0.15;
    Vector3d pose_standby;
    pose_standby << PI/4, 0, -PI/2;

    /*
    set init position
    */ 
    VectorXd jnt_init(7);
    jnt_init << 0,-PI*8/180,0,0,0,0,0;
    Vector3d pose_init;
    KineRes kin = kineFunc.fkine(jnt_init);
    Vector3d cart_pos_init = kin.cart_end_position;
    pose_init << PI/2 - kin.eul_r, kin.eul_p, kin.eul_y;

    /*
    traj_standby2init
    */ 
    Plan_Res_MulDOF action1_standby2init_inv = dmpLib.action1_dmp(cart_pos_standby,pose_standby,dt,1,true,-PI/2);
    if(action1_standby2init_inv.error_flag){
        ROS_ERROR("ACTION1_STANDBY2INIT PLANNING FAILED!");
        return 0;
    }
    int n2 = action1_standby2init_inv.t.rows();
    MatrixXd pos_inv(n2,7);
    MatrixXd vel_inv(n2,7);
    MatrixXd acc_inv(n2,7);
    for(int i = 0; i < n2; i++){
        pos_inv.row(i) = action1_standby2init_inv.pos.row(n2-1-i);
        vel_inv.row(i) = action1_standby2init_inv.vel.row(n2-1-i);
        acc_inv.row(i) = action1_standby2init_inv.acc.row(n2-1-i);
    }
    Plan_Res_MulDOF action1_standby2init;
    action1_standby2init.t = action1_standby2init_inv.t;
    action1_standby2init.pos = pos_inv;
    action1_standby2init.vel = vel_inv;
    action1_standby2init.acc = acc_inv;

    /*
    traj_current2standby
    */
    double t1_action1 = 5;
    double t2_action1 = action1_standby2init.t(n2-1);

    VectorXd t_action1_current2standby(2);
    VectorXd phi_action1_current2standby(1);
    t_action1_current2standby << 0, t1_action1;
    phi_action1_current2standby << -PI/2;

    MatrixXd position_action1_current2standby_r(1,3);
    MatrixXd pose_action1_current2standby_r(1,3);
    position_action1_current2standby_r.row(0) = cart_pos_standby;
    pose_action1_current2standby_r.row(0) = pose_standby;
    MatrixXd position_action1_current2standby_l(1,3);
    MatrixXd pose_action1_current2standby_l(1,3);
    position_action1_current2standby_l.row(0) = cart_pos_standby;
    pose_action1_current2standby_l.row(0) = pose_standby;
    
    Plan_Res_MulDOF action1_current2standby_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r,t_action1_current2standby,position_action1_current2standby_r,pose_action1_current2standby_r,phi_action1_current2standby,dt);
    if(action1_current2standby_r.error_flag){
        ROS_ERROR("ACTION1_CURRENT2STANDBY_R PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF action1_current2standby_l = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_l,t_action1_current2standby,position_action1_current2standby_l,pose_action1_current2standby_l,phi_action1_current2standby,dt);
    if(action1_current2standby_l.error_flag){
        ROS_ERROR("ACTION1_CURRENT2STANDBY_L PLANNING FAILED!");
        return 0;
    }
    int n1 = action1_current2standby_r.t.rows();

    
    /*
    traj_current2init
    */
    double t_action2_goal = t1_action1 + t2_action1;
    VectorXd t_action2_current2init(2);
    VectorXd phi_action2_current2init(1);
    t_action2_current2init << 0, t_action2_goal;
    phi_action2_current2init << 0;

    MatrixXd position_action2_current2init_r(1,3);
    MatrixXd pose_action2_current2init_r(1,3);
    position_action2_current2init_r.row(0) = cart_pos_init;
    pose_action2_current2init_r.row(0) = pose_init;
    MatrixXd position_action2_current2init_l(1,3);
    MatrixXd pose_action2_current2init_l(1,3);
    position_action2_current2init_l.row(0) = cart_pos_init;
    pose_action2_current2init_l.row(0) = pose_init;

    Plan_Res_MulDOF action2_current2init_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r,t_action2_current2init,position_action2_current2init_r,pose_action2_current2init_r,phi_action2_current2init,dt);
    if(action2_current2init_r.error_flag){
        ROS_ERROR("ACTION2_CURRENT2INIT_R PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF action2_current2init_l = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_l,t_action2_current2init,position_action2_current2init_l,pose_action2_current2init_l,phi_action2_current2init,dt);
    if(action2_current2init_l.error_flag){
        ROS_ERROR("ACTION1_CURRENT2INIT_L PLANNING FAILED!");
        return 0;
    }
    int n3 = action2_current2init_r.t.rows();

    /*
    Trajectory splicing
    */
    Plan_Res_MulDOF action1_r;
    Plan_Res_MulDOF action1_l;
    Plan_Res_MulDOF action2_r;
    Plan_Res_MulDOF action2_l;
    int n = max(n1+n2,n3);

    action1_r = adjustFunc.traj_splic(action1_current2standby_r,action1_standby2init);
    action1_r = adjustFunc.traj_extend(action1_r,n,dt);
    action1_l = adjustFunc.traj_splic(action1_current2standby_l,action1_standby2init);
    action1_l = adjustFunc.traj_extend(action1_l,n,dt);
    action2_r = adjustFunc.traj_extend(action2_current2init_r,n,dt);
    action2_l = adjustFunc.traj_extend(action2_current2init_l,n,dt);

    cout << "jnt_end_r1: " << action1_r.pos.row(n-1)(0)*180/PI << " " << action1_r.pos.row(n-1)(1)*180/PI << " " << action1_r.pos.row(n-1)(2)*180/PI << " " << action1_r.pos.row(n-1)(3)*180/PI << " " << action1_r.pos.row(n-1)(4)*180/PI << " " << action1_r.pos.row(n-1)(5)*180/PI << " " << action1_r.pos.row(n-1)(6)*180/PI << endl;
    cout << "jnt_end_l1: " << action1_l.pos.row(n-1)(0)*180/PI << " " << action1_l.pos.row(n-1)(1)*180/PI << " " << action1_l.pos.row(n-1)(2)*180/PI << " " << action1_l.pos.row(n-1)(3)*180/PI << " " << action1_l.pos.row(n-1)(4)*180/PI << " " << action1_l.pos.row(n-1)(5)*180/PI << " " << action1_l.pos.row(n-1)(6)*180/PI << endl;
    cout << "jnt_end_r2: " << action2_r.pos.row(n-1)(0)*180/PI << " " << action2_r.pos.row(n-1)(1)*180/PI << " " << action2_r.pos.row(n-1)(2)*180/PI << " " << action2_r.pos.row(n-1)(3)*180/PI << " " << action2_r.pos.row(n-1)(4)*180/PI << " " << action2_r.pos.row(n-1)(5)*180/PI << " " << action2_r.pos.row(n-1)(6)*180/PI << endl;
    cout << "jnt_end_l2: " << action2_l.pos.row(n-1)(0)*180/PI << " " << action2_l.pos.row(n-1)(1)*180/PI << " " << action2_l.pos.row(n-1)(2)*180/PI << " " << action2_l.pos.row(n-1)(3)*180/PI << " " << action2_l.pos.row(n-1)(4)*180/PI << " " << action2_l.pos.row(n-1)(5)*180/PI << " " << action2_l.pos.row(n-1)(6)*180/PI << endl;

    char key_input1;
    char key_input2;
    cout << "请输入右臂回零方式：(1)末端高于桌面，先到待机位再回零；(2)末端低于桌面，直接回零" << endl;
    key_input1 = getchar();
    getchar();
    cout << "请输入左臂回零方式：(1)末端高于桌面，先到待机位再回零；(2)末端低于桌面，直接回零" << endl;
    key_input2 = getchar();

    int count = 0;
    // grasp_msg.data = 2;
    // while(count < 100)
    // {   
    //     grasp_pub_r.publish(grasp_msg);
    //     grasp_pub_l.publish(grasp_msg);
    //     count++;
    // }
    //手控制
    dataPub2.doubleHand();
    ROS_INFO("hand release sucessed!"); 

    int move_mode = 3;
    //  CTRL_MODE move_mode = rviz_ctrl;
    if(key_input1 == '1' && key_input2 == '1'){
        dataSave.save2txt(action1_r,action1_l,fpWrite);
        fclose(fpWrite);
        moveLib.move(action1_r,action1_l,move_mode);
    }else if(key_input1 == '1' && key_input2 == '2'){
        dataSave.save2txt(action1_r,action2_l,fpWrite);
        fclose(fpWrite);
        moveLib.move(action1_r,action2_l,move_mode);
    }else if(key_input1 == '2' && key_input2 == '1'){
        dataSave.save2txt(action2_r,action1_l,fpWrite);
        fclose(fpWrite);
        moveLib.move(action2_r,action1_l,move_mode);
    }else if(key_input1 == '2' && key_input2 == '2'){
        dataSave.save2txt(action2_r,action2_l,fpWrite);
        fclose(fpWrite);
        moveLib.move(action2_r,action2_l,move_mode);
    }
    
    count = 0;
    grasp_msg.data = 0;
    while(count < 100)
    {   
        grasp_pub_r.publish(grasp_msg);
        grasp_pub_l.publish(grasp_msg);
        count++;
    }
    return 0;
}
