/*
倒水：
    （0、拧开瓶盖）
    1、双臂初始位置/待机位到抓取位置，左手抓水杯，右手抓饮料
        - 判断当前位置
            - 当前在初始位置，则初始位置->抓取位置
            - 当前在待机位置，则待机位置->抓取位置
            - 当前不在初始位置或待机位置，若末端低于桌面，则当前->初始位置->抓取位置；若末端高于桌面，则当前->待机位置->抓取位置
        - 左右手执行抓取
    2、双臂移动到偏中间位置，左手偏下，右手偏上
    3、左手略倾斜，右手倾斜同时向外向上小幅度移动，完成倒水
    4、左右手将手中物体放回桌面
    5、左右手返回待机位
*/ 

#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"
#include "robot_control/robot_control.h"

using namespace std;

#define PI 3.141592654

double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::MOVE moveLib;

std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }
    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    dt = 0.001;//0.001
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action04_pour_water");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

    //手控制
    // INSPIRE_HAND::Pos_Ctrl handCtrl;
    DATA_PROC::Data_Pub handCtrl(false);
    // handCtrl.doubleHand({999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600}, {999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600});
    // handCtrl.doubleHand({550, 550, 500, 500, 500, 10}, {600, 600, 600, 600, 600, 600}, {800, 800, 800, 800, 800, 100}, {600, 600, 600, 600, 600, 600});
    // // handCtrl.doubleHand({550, 550, 500, 500, 500, 10}, {800, 800, 800, 800, 800, 100});

    // return 0;

    /*
    move_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
    int ctrl_mode = 3;
    // CTRL_MODE ctrl_mode = rviz_ctrl;

    // int move_mode = 1;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    Vector3d bias_hand1;
    Vector3d bias_hand2;
    bias_hand1 << 0.0, 0.06, 0.0;
    bias_hand2 << 0.0, 0.06, 0.02;

    /*
    set standby position
    */ 
    Vector3d cart_pos_standby;
    cart_pos_standby << 0.35, -0.25, -0.1;
    Vector3d pose_standby;
    pose_standby << PI/4, 0, -PI/2;

    /*
    set init position
    */ 
    VectorXd jnt_init(7);
    jnt_init << 0,-PI*8/180,0,0,0,0,0;
    Vector3d pose_init;
    KineRes kin = kineFunc.fkine(jnt_init);
    Vector3d cart_pos_init = kin.cart_end_position;
    pose_init << PI/2 - kin.eul_r, kin.eul_p, kin.eul_y;

    /*
    set pour position
    */
    double L = 0.155;
    Vector3d cart_pos_hold;
    cart_pos_hold << 0.35, -0.05, -0.2;
    Vector3d pose_hold1;
    Vector3d pose_hold2;
    pose_hold1 << PI/6, 0, 0;
    pose_hold2 << PI/6, 0, 0;
    Vector3d bias_pos_hold1 = kineFunc.biasHand(pose_hold1, bias_hand1);
    Vector3d bias_pos_hold2 = kineFunc.biasHand(pose_hold2, bias_hand1);

    Vector3d cart_pos_pour;
    cart_pos_pour << cart_pos_hold(0)+L*sin(PI/6)-0.04, -(cart_pos_hold(1)+L*cos(PI/6)), cart_pos_hold(2)+0.045;
    Vector3d pose_pour1;
    Vector3d pose_pour2;
    Vector3d pose_pour3;
    pose_pour1 << PI/3, 0, 0;
    pose_pour2 << PI/3, 0, -PI/5;
    pose_pour3 << PI/3, 0, -PI/2.12;
    Vector3d bias_pos_pour1 = kineFunc.biasHand(pose_pour1, bias_hand1);
    Vector3d bias_pos_pour2 = kineFunc.biasHand(pose_pour1, bias_hand1) + Vector3d(0.03*sin(PI/6),-0.03*cos(PI/6),-0.03);
    Vector3d bias_pos_pour3 = kineFunc.biasHand(pose_pour1, bias_hand1) + Vector3d(0.05*sin(PI/6),-0.05*cos(PI/6),-0.08);
    Vector3d bias_pos_pour4 = kineFunc.biasHand(pose_pour1, bias_hand1) + Vector3d(0,0.05,-0.05);


    /*
    get grasp position
    */ 
    Vector3d cart_pos_grasp_r;
    Vector3d pose_grasp_r;
    Vector3d cart_pos_grasp_l;
    Vector3d pose_grasp_l;

    #pragma region GET POSITIONS BASED ON VISION
        ROS_INFO("Get postions based on vision");
        while(!detect_flag && ros::ok())
        {
            ros::spinOnce();
            usleep(1000);
            ROS_INFO("no detect");
        }
        /* 接受两个目标物体的位置信息 */
        if (container.size() >= 8) 
        {
            cart_pos_grasp_l(0) = container[0];
            cart_pos_grasp_l(1) = -container[1];
            cart_pos_grasp_l(2) = container[2];
            cart_pos_grasp_r(0) = container[4];
            cart_pos_grasp_r(1) = container[5];
            cart_pos_grasp_r(2) = container[6];
            container.erase(container.begin(), container.begin() + 8);
        }
        cout << "cart_pos_grasp_l: " << cart_pos_grasp_l(0) << ", " << cart_pos_grasp_l(1) << ", " << cart_pos_grasp_l(2) << endl;
        // cart_pos_grasp_l = cart_pos_grasp_l + Vector3d(0.08, -0.085, 0.025);
        // cart_pos_grasp_r = cart_pos_grasp_r + Vector3d(0.06, -0.045, -0.01);
        cart_pos_grasp_l = cart_pos_grasp_l + Vector3d(-0.012, -0.05, 0.04) + Vector3d(0.015, -0.007, 0.006) + Vector3d(0, 0, 0);
        cart_pos_grasp_r = cart_pos_grasp_r + Vector3d(0.033, -0.055, -0.02)+ Vector3d(0.02, 0.02, 0);
    #pragma endregion

    handCtrl.doubleHand({999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600}, {999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600});

    /*
    set/get current position
    */ 
    while(!flag && ros::ok())
    {
        ROS_INFO("waiting for initial position");
        usleep(1000);
        ros::spinOnce();
    }

    // jnt_pos_current_r << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180;
    // jnt_pos_current_l << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180; 
    // jnt_pos_current_r << 0,-PI*5/180,0,0,0,0,0;
    // jnt_pos_current_l << 0,-PI*5/180,0,0,0,0,0;
    Vector3d cart_pos_current_r = kineFunc.fkine(jnt_pos_current_r).cart_end_position;
    Vector3d cart_pos_current_l = kineFunc.fkine(jnt_pos_current_l).cart_end_position;


    

    pose_grasp_r << PI/4, 0, 0;
    pose_grasp_l << PI/4, 0, 0;

    /*
    1、双臂初始位置/待机位到抓取位置，左手抓水杯，右手抓饮料
        - 判断当前位置
            - 当前在初始位置，则初始位置->抓取位置
            - 当前在待机位置，则待机位置->抓取位置
            (- 当前不在初始位置或待机位置，若末端低于桌面，则当前->初始位置->抓取位置；若末端高于桌面，则当前->待机位置->抓取位置)
        - 左右手执行抓取
    */
    int act1_done_flag = 0;
    /*
    traj_init2grasp
    */ 
    Vector3d bias_pos_grasp;
    // bias_pos_grasp << -0.01, -0.06, 0.04;
    bias_pos_grasp << -0.02, -0.1, 0.03;

    Vector3d bias_pos_grasp_r;
    Vector3d bias_pos_grasp_l;
    if(cart_pos_grasp_r(0) < 0.33){
        bias_pos_grasp_r = bias_pos_grasp + Vector3d(0.33-cart_pos_grasp_r(0),0,0);
    }else{
        bias_pos_grasp_r = bias_pos_grasp;
    }
    if(cart_pos_grasp_l(0) < 0.33){
        bias_pos_grasp_l = bias_pos_grasp + Vector3d(0.33-cart_pos_grasp_l(0),0,0);
    }else{
        bias_pos_grasp_l = bias_pos_grasp;
    }
    Plan_Res_MulDOF action1_init2grasp_bias_r = dmpLib.action1_dmp(cart_pos_grasp_r+bias_pos_grasp_r,pose_grasp_r,dt);
    if(action1_init2grasp_bias_r.error_flag){
        ROS_ERROR("ACTION1_INIT2GRASP_BIAS_R PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF action1_init2grasp_bias_l = dmpLib.action1_dmp(cart_pos_grasp_l+bias_pos_grasp_l,pose_grasp_l,dt);
    if(action1_init2grasp_bias_l.error_flag){
        ROS_ERROR("ACTION1_INIT2GRASP_BIAS_L PLANNING FAILED!");
        return 0;
    }
    int n1 = action1_init2grasp_bias_r.t.size();

    VectorXd t_act1_traj1(2);
    t_act1_traj1 << 0, 3;
    VectorXd phi_act1_traj1(1);
    phi_act1_traj1 << -PI/4;
    MatrixXd pos_act1_traj1(1,3);
    MatrixXd pose_act1_traj1(1,3);
    pos_act1_traj1.row(0) = cart_pos_grasp_r;
    pose_act1_traj1.row(0) = pose_grasp_r;
    Plan_Res_MulDOF action1_bias2grasp_r = interPlan.quinitic_poly_inter_mulDOF(action1_init2grasp_bias_r.pos.row(n1-1),t_act1_traj1,pos_act1_traj1,pose_act1_traj1,phi_act1_traj1,dt);
    if(action1_bias2grasp_r.error_flag){
        ROS_ERROR("ACTION1_BIAS2GRASP_R PLANNING FAILED!");
        return 0;
    }
    pos_act1_traj1.row(0) = cart_pos_grasp_l;
    pose_act1_traj1.row(0) = pose_grasp_l;
    Plan_Res_MulDOF action1_bias2grasp_l = interPlan.quinitic_poly_inter_mulDOF(action1_init2grasp_bias_l.pos.row(n1-1),t_act1_traj1,pos_act1_traj1,pose_act1_traj1,phi_act1_traj1,dt);
    if(action1_bias2grasp_l.error_flag){
        ROS_ERROR("ACTION1_BIAS2GRASP_L PLANNING FAILED!");
        return 0;
    }

    Plan_Res_MulDOF action1_traj1_r = adjustFunc.traj_splic(action1_init2grasp_bias_r,action1_bias2grasp_r);
    Plan_Res_MulDOF action1_traj1_l = adjustFunc.traj_splic(action1_init2grasp_bias_l,action1_bias2grasp_l);

    /*
    traj_standby2grasp
    */
    
    VectorXd t_action1_traj2(3);
    t_action1_traj2 << 0, 5, 10;//
    VectorXd phi_action1_traj2(2);
    phi_action1_traj2 << -PI/4, -PI/4;

    MatrixXd pos_act1_traj2_r(2,3);
    MatrixXd pose_act1_traj2_r(2,3);
    pos_act1_traj2_r.row(0) = cart_pos_grasp_r + bias_pos_grasp + Eigen::Vector3d(0,0.04,0.01);
    pos_act1_traj2_r.row(1) = cart_pos_grasp_r;
    pose_act1_traj2_r.row(0) = pose_grasp_r;
    pose_act1_traj2_r.row(1) = pose_grasp_r;

    MatrixXd pos_act1_traj2_l(2,3);
    MatrixXd pose_act1_traj2_l(2,3);
    pos_act1_traj2_l.row(0) = cart_pos_grasp_l + bias_pos_grasp + Eigen::Vector3d(0,0.0,0.02);
    pos_act1_traj2_l.row(1) = cart_pos_grasp_l;
    pose_act1_traj2_l.row(0) = pose_grasp_l;
    pose_act1_traj2_l.row(1) = pose_grasp_l;
    MatrixXd vel_ends_act1 = MatrixXd::Zero(7,2);
    
    Plan_Res_MulDOF action1_traj2_r = interPlan.cub_spline_inter_mulDOF(jnt_pos_current_r,t_action1_traj2,pos_act1_traj2_r,pose_act1_traj2_r,phi_action1_traj2,vel_ends_act1,dt);
    // Plan_Res_MulDOF action1_traj2_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r,t_action1_traj2,pos_act1_traj2_r,pose_act1_traj2_r,phi_action1_traj2,dt);
    if(action1_traj2_r.error_flag){
        ROS_ERROR("ACTION1_STANDBY2GRASP_R PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF action1_traj2_l = interPlan.cub_spline_inter_mulDOF(jnt_pos_current_l,t_action1_traj2,pos_act1_traj2_l,pose_act1_traj2_l,phi_action1_traj2,vel_ends_act1,dt);
    // Plan_Res_MulDOF action1_traj2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_l,t_action1_traj2,pos_act1_traj2_l,pose_act1_traj2_l,phi_action1_traj2,dt);
    if(action1_traj2_l.error_flag){
        ROS_ERROR("ACTION1_STANDBY2GRASP_L PLANNING FAILED!");
        return 0;
    }

    Plan_Res_MulDOF action1_r;
    Plan_Res_MulDOF action1_l;
    if(sqrt(pow(cart_pos_current_r(0)-cart_pos_init(0),2)+pow(cart_pos_current_r(1)-cart_pos_init(1),2)+pow(cart_pos_current_r(2)-cart_pos_init(2),2)) < 1e-2){
        action1_r = action1_traj1_r;
        act1_done_flag = 1;
    }
    if(sqrt(pow(cart_pos_current_r(0)-cart_pos_standby(0),2)+pow(cart_pos_current_r(1)-cart_pos_standby(1),2)+pow(cart_pos_current_r(2)-cart_pos_standby(2),2)) < 1e-2){
        action1_r = action1_traj2_r;
        act1_done_flag = 1;
    }
    if(sqrt(pow(cart_pos_current_l(0)-cart_pos_init(0),2)+pow(cart_pos_current_l(1)-cart_pos_init(1),2)+pow(cart_pos_current_l(2)-cart_pos_init(2),2)) < 1e-2){
        action1_l = action1_traj1_l;
        act1_done_flag = 1;
    }
    if(sqrt(pow(cart_pos_current_l(0)-cart_pos_standby(0),2)+pow(cart_pos_current_l(1)-cart_pos_standby(1),2)+pow(cart_pos_current_l(2)-cart_pos_standby(2),2)) < 1e-2){
        action1_l = action1_traj2_l;
        act1_done_flag = 1;
    }

    int n_act1_r = action1_r.t.size();
    int n_act1_l = action1_l.t.size();
    int n_act1 = max(n_act1_r,n_act1_l);
    action1_r = adjustFunc.traj_extend(action1_r,n_act1,dt);
    action1_l = adjustFunc.traj_extend(action1_l,n_act1,dt);
    jnt_end_r = action1_r.pos.row(n_act1-1);
    jnt_end_l = action1_l.pos.row(n_act1-1);

    ROS_INFO("ACTION1_STANDBY2GRASP STARTING!");
    dataSave.save2txt(action1_r,action1_l,fpWrite);
    moveLib.move(action1_r,action1_l,ctrl_mode);
    ROS_INFO("ACTION1_STANDBY2GRASP FINISHED!");

    /*
    手部抓取
    */
    // grasp_msg.data = 5;//抓取
    // while(!grasp_finshed_l && !grasp_finshed_r && ros::ok())
    // {
    //     ROS_INFO("hand is grasping!"); 
    //     grasp_pub_l.publish(grasp_msg);
    //     grasp_pub_r.publish(grasp_msg);
    //     ros::spinOnce();
    //     usleep(1000);
    // }
    // sleep(4);
    // handCtrl.doubleHand({550, 550, 500, 500, 500, 10}, {800, 800, 800, 800, 800, 100});
    handCtrl.doubleHand({550, 550, 500, 500, 500, 10}, {600, 600, 600, 600, 600, 600}, {800, 800, 800, 800, 800, 100}, {600, 600, 600, 600, 600, 600});

    ROS_INFO("hand grasp sucessed!"); 

    /*
    2、双臂移动到偏中间位置，左手偏下，右手偏上
    3、右手倾斜完成倒水，延时2s后，右手运动到使水杯竖直状态的姿态
    */
    int act2_enable_flag = 1;
    if(!act1_done_flag || !act2_enable_flag){
        fclose(fpWrite);
        return 0;
    }
    VectorXd t_act2(5);
    t_act2 << 0, 4, 8, 11, 14;
    VectorXd phi_act2_r(4);
    phi_act2_r << -PI/4, -PI/3, -PI/2, -PI/4;
    VectorXd phi_act2_l(4);
    phi_act2_l << -PI/2, -PI/2, -PI/2, -PI/2;
    MatrixXd pos_act2_r(4,3);
    MatrixXd pose_act2_r(4,3);
    pos_act2_r.row(0) = cart_pos_pour - bias_pos_pour1 + Vector3d(0,0,0.01);
    pos_act2_r.row(1) = cart_pos_pour - bias_pos_pour2 + Vector3d(0,-0.01,0.01);
    pos_act2_r.row(2) = cart_pos_pour - bias_pos_pour3 + Vector3d(0.0,-0.01,0.02);
    pos_act2_r.row(3) = cart_pos_pour - bias_pos_pour2 + Vector3d(0.0,-0.04,0.02) + Vector3d(0,0,0.01);
    pose_act2_r.row(0) = pose_pour1;
    pose_act2_r.row(1) = pose_pour2;
    pose_act2_r.row(2) = pose_pour3;
    pose_act2_r.row(3) = pose_pour1;
    MatrixXd pos_act2_l(4,3);
    MatrixXd pose_act2_l(4,3);
    pos_act2_l.row(0) = cart_pos_hold - bias_pos_hold1 - Vector3d(0,-0.01,0.025);
    pos_act2_l.row(1) = cart_pos_hold - bias_pos_hold2 - Vector3d(0,-0.01,0.025);
    pos_act2_l.row(2) = cart_pos_hold - bias_pos_hold2 - Vector3d(0,0.005,0.025);
    pos_act2_l.row(3) = cart_pos_hold - bias_pos_hold1 - Vector3d(0,-0.01,0.025);
    pose_act2_l.row(0) = pose_hold1;
    pose_act2_l.row(1) = pose_hold2;
    pose_act2_l.row(2) = pose_hold2;
    pose_act2_l.row(3) = pose_hold1;
    MatrixXd vel_ends_act2 = MatrixXd::Zero(7,2);

    // Plan_Res_MulDOF action2_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_right,t_act2,pos_act2_r,pose_act2_r,phi_act2_r,dt);
    Plan_Res_MulDOF action2_r = interPlan.cub_spline_inter_mulDOF(jnt_end_r,t_act2,pos_act2_r,pose_act2_r,phi_act2_r,vel_ends_act2,dt);
    if(action2_r.error_flag){
        ROS_ERROR("ACTION2_R PLANNING FAILED!");
        fclose(fpWrite);
        return 0;
    }
    // Plan_Res_MulDOF action2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_left,t_act2,pos_act2_l,pose_act2_l,phi_act2_l,dt);
    Plan_Res_MulDOF action2_l = interPlan.cub_spline_inter_mulDOF(jnt_end_l,t_act2,pos_act2_l,pose_act2_l,phi_act2_l,vel_ends_act2,dt);
    if(action2_l.error_flag){
        ROS_ERROR("ACTION2_L PLANNING FAILED!");
        fclose(fpWrite);
        return 0;
    }
    int n_action2 = action2_r.t.size();
    jnt_end_r = action2_r.pos.row(n_action2-1);
    jnt_end_l = action2_l.pos.row(n_action2-1);

    ROS_INFO("ACTION2_POUR STARTING!");
    dataSave.save2txt(action2_r,action2_l,fpWrite);
    moveLib.move(action2_r,action2_l,ctrl_mode);
    ROS_INFO("ACTION2_POUR FINISHED!");


    /*
    4、左右手将手中物体放回桌面
    */
    int act3_enable_flag = 1;
    if(!act3_enable_flag){
        fclose(fpWrite);
        return 0;
    }
    VectorXd t_act3(2);
    t_act3 << 0, 5;
    VectorXd phi_act3(1);
    phi_act3 << -PI/4;
    MatrixXd pos_act3_r(1,3);
    MatrixXd pose_act3_r(1,3);
    pos_act3_r.row(0) = cart_pos_grasp_r;
    pose_act3_r.row(0) = pose_grasp_r;
    MatrixXd pos_act3_l(1,3);
    MatrixXd pose_act3_l(1,3);
    pos_act3_l.row(0) = cart_pos_grasp_l;
    pose_act3_l.row(0) = pose_grasp_l;
    
    Plan_Res_MulDOF action3_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3,pos_act3_r,pose_act3_r,phi_act3,dt);
    if(action3_r.error_flag){
        ROS_ERROR("ACTION3_R PLANNING FAILED!");
        fclose(fpWrite);
        return 0;
    }
    Plan_Res_MulDOF action3_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3,pos_act3_l,pose_act3_l,phi_act3,dt);
    if(action3_l.error_flag){
        ROS_ERROR("ACTION3_L PLANNING FAILED!");
        fclose(fpWrite);
        return 0;
    }
    int n_action3 = action3_r.t.size();
    jnt_end_r = action3_r.pos.row(n_action3-1);
    jnt_end_l = action3_l.pos.row(n_action3-1);

    ROS_INFO("ACTION3_PUTDOWN STARTING!");
    dataSave.save2txt(action3_r,action3_l,fpWrite);
    moveLib.move(action3_r,action3_l,ctrl_mode);
    ROS_INFO("ACTION3_PUTDOWN FINISHED!");

    /*
    手部释放
    */
    // int count = 0;
    // grasp_msg.data = 2;//释放
    // while(count < 1000)
    // {
    //     grasp_pub_l.publish(grasp_msg);
    //     grasp_pub_r.publish(grasp_msg);
    //     count++;
    // }
    // sleep(4);
    // handCtrl.doubleHand({999, 999, 999, 999, 999, 10}, {400, 400, 400, 400, 400, 400}, {999, 999, 999, 999, 999, 10}, {400, 400, 400, 400, 400, 400});
    handCtrl.doubleHand({999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600}, {999, 999, 999, 999, 999, 10}, {600, 600, 600, 600, 600, 600});

    ROS_INFO("hand release sucessed!"); 

    /*
    5、左右手返回待机位
    */
    int act4_enable_flag = 1;
    if(!act4_enable_flag){
        fclose(fpWrite);
        return 0;
    }

    Plan_Res_MulDOF action4_r;
    Plan_Res_MulDOF action4_l;

    int is_return2init = 1;
    if(!is_return2init){
        VectorXd t_act4(3);
        t_act4 << 0, 4, 10;
        VectorXd phi_act4(2);
        phi_act4 << -PI/4, -PI/4;
        MatrixXd pos_act4_r(2,3);
        MatrixXd pose_act4_r(2,3);
        pos_act4_r.row(0) = kineFunc.fkine(jnt_end_r).cart_end_position + Vector3d(-0.03, -0.1, 0.04);
        pos_act4_r.row(1) = cart_pos_standby;
        pose_act4_r.row(0) = pose_grasp_r;
        pose_act4_r.row(1) = pose_standby;
        MatrixXd pos_act4_l(2,3);
        MatrixXd pose_act4_l(2,3);
        pos_act4_l.row(0) = kineFunc.fkine(jnt_end_l).cart_end_position + Vector3d(-0.03, -0.1, 0.04);
        pos_act4_l.row(1) = cart_pos_standby;
        pose_act4_l.row(0) = pose_grasp_l;
        pose_act4_l.row(1) = pose_standby;

        action4_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act4,pos_act4_r,pose_act4_r,phi_act4,dt);
        if(action4_r.error_flag){
            ROS_ERROR("ACTION4_R PLANNING FAILED!");
            fclose(fpWrite);
            return 0;
        }
        action4_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act4,pos_act4_l,pose_act4_l,phi_act4,dt);
        if(action4_l.error_flag){
            ROS_ERROR("ACTION4_L PLANNING FAILED!");
            fclose(fpWrite);
            return 0;
        }
    }else{
        int n1_inv = action1_traj1_r.t.size();
        MatrixXd pos_inv_r(n1_inv,7);
        MatrixXd vel_inv_r(n1_inv,7);
        MatrixXd acc_inv_r(n1_inv,7);
        MatrixXd pos_inv_l(n1_inv,7);
        MatrixXd vel_inv_l(n1_inv,7);
        MatrixXd acc_inv_l(n1_inv,7);
        for(int i = 0; i < n1_inv; i++){
            pos_inv_r.row(i) = action1_traj1_r.pos.row(n1_inv-1-i);
            vel_inv_r.row(i) = action1_traj1_r.vel.row(n1_inv-1-i);
            acc_inv_r.row(i) = action1_traj1_r.acc.row(n1_inv-1-i);
            pos_inv_l.row(i) = action1_traj1_l.pos.row(n1_inv-1-i);
            vel_inv_l.row(i) = action1_traj1_l.vel.row(n1_inv-1-i);
            acc_inv_l.row(i) = action1_traj1_l.acc.row(n1_inv-1-i);
        }
        
        action4_r.t = action1_traj1_r.t;
        action4_r.pos = pos_inv_r;
        action4_r.vel = vel_inv_r;
        action4_r.acc = acc_inv_r;

        action4_l.t = action1_traj1_l.t;
        action4_l.pos = pos_inv_l;
        action4_l.vel = vel_inv_l;
        action4_l.acc = acc_inv_l;
    }

    ROS_INFO("ACTION4_STANDBY STARTING!");
    dataSave.save2txt(action4_r,action4_l,fpWrite);
    moveLib.move(action4_r,action4_l,ctrl_mode);
    fclose(fpWrite);
    ROS_INFO("ACTION4_STANDBY FINISHED!");


    return 0;
}