/*
抓递水果
    1、左手抓取水果
    2、左手移动到中间位置，手心向上，右手移动到抓取水果位置上方
    3、左手张开，右手抓取水果
    4、左手移动到待机位置，右手向前伸出，手心向上
*/ 
#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"
#include "robot_control/robot_control.h"
using namespace std;

#define PI 3.141592653589

// dt = 0.001;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::INTER interLib;
ACT_LIB::MOVE moveLib;

std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;

void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }

    //关节位置校验
    for(int i = 0; i < 7; i++){
        if (jnt_pos_current_l[i] < -PI || jnt_pos_current_l[i] > PI){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_l[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
        if (jnt_pos_current_r[i] < -PI || jnt_pos_current_r[i] > PI){
            cout << "error: current Joint" << i+1 << " " << jnt_pos_current_r[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
    }

    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action12_give_fruit");

    const char* urdf_path = getenv("URDF_PATH");
    ROBOT_CONTROL::robot_control robot(urdf_path,"base_footprint","l_arm_Link7_tool","r_arm_Link7_tool");

    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/pose_grasp_base", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

	//启用多线程
 	ros::AsyncSpinner spinner(3); // Use 2 threads
 	spinner.start(); //开始标志

    //手控制
    INSPIRE_HAND::Pos_Ctrl handCtrl;

    DATA_PROC::Data_Pub dataPub(false);

    def_msgs::Pose pose;
    Vector3d vec, rpy;
    Matrix4d T;
    VectorXd pose_start(6), pose_end(6);
    // 轨迹点集合
    Plan_Res_Dual action;
    vector<double> t_action;
    vector<array<double, 7>> qp_grasp;
    dt = 0.0015;
    /*
    move_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
    // int ctrl_mode = 1;
    int ctrl_mode = 3;
    // CTRL_MODE ctrl_mode = stop;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    int side_grasp = 0; // 0: left, 1: right
    int is_test = 0;
    #pragma region GET_POS_GRASP
        /*
        获取抓取点
        */
        Vector3d pos_grasp;
        Vector3d pose_grasp;
        Vector3d pos_grasp2;
        if(is_test){
            pos_grasp << 0.35, 0, -0.4;
            pos_grasp2 << pos_grasp(0), pos_grasp(1), pos_grasp(2);
        }else{
            ROS_INFO("Get postions based on vision");
            while(!detect_flag && ros::ok())
            {
                // ros::spinOnce();
                usleep(1000);
                // ROS_INFO("no detect");
            }
            /* 接受两个目标物体的位置信息 */
            if (container.size() >= 4) 
            {
                pos_grasp(0) = container[0];
                pos_grasp(2) = container[2];
                
                if(container[1] < 0){
                    side_grasp = 1;
                    pos_grasp(1) = container[1];

                    pos_grasp2(0) = container[0] + 0.035 - 0.01;
                    pos_grasp2(1) = container[1] + 0.035 - 0.0;
                    if(container[3] != 1){
                        pos_grasp2(2) = container[2] + 0.36850 - 0.38 + 0.009;
                    }else{
                        pos_grasp2(2) = container[2] + 0.36850 - 0.38 + 0.028;
                    }
                    // pos_grasp2(2) = -0.38 ;                    

                }else{
                    side_grasp = 0;
                    pos_grasp(1) = -container[1];

                    pos_grasp2(0) = container[0] + 0.025 - 0.002 + 0.02;
                    pos_grasp2(1) = container[1] - 0.01 + 0.007;
                    if(container[3] != 1){
                        pos_grasp2(2) = container[2] + 0.36850 - 0.38 + 0.003;
                    }else{
                        pos_grasp2(2) = container[2] + 0.36850 - 0.38 + 0.022;
                    }

                    // pos_grasp2(2) = -0.26 - 0.12;

                }
                container.erase(container.begin(), container.begin() + 4);
            }
            cout << "cart_pos_grasp: " << pos_grasp(0) << ", " << pos_grasp(1) << ", " << pos_grasp(2) << endl;
            
            // pos_grasp += Vector3d(0.055, -0.035, 0.075) + Vector3d(0.03, -0.005, 0.008);
            ROS_INFO_STREAM("OBJ_TYPE: " << container[3]);
            pos_grasp += Vector3d(0.055, -0.035, 0.075) + Vector3d(0.03, -0.005, 0.03);
        }
        pose_grasp << 1.5708, 2.61799, 2.35619;
    #pragma endregion

    #pragma region GET_POS_INIT
        if(is_test){
            jnt_end_r << 0, -7*PI/180, 0, 0, 0, 0, 0;
            jnt_end_l << 0, -7*PI/180, 0, 0, 0, 0, 0;
        }else{
            while(!flag && ros::ok())
            {
                // ROS_INFO("waiting for initial position");
                usleep(1000);
                // ros::spinOnce();
            }
            ROS_INFO("left_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
            jnt_pos_current_l(0)*180/PI,jnt_pos_current_l(1)*180/PI,jnt_pos_current_l(2)*180/PI,jnt_pos_current_l(3)*180/PI,jnt_pos_current_l(4)*180/PI,jnt_pos_current_l(5)*180/PI,jnt_pos_current_l(6)*180/PI);
            ROS_INFO("right_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
            jnt_pos_current_r(0)*180/PI,jnt_pos_current_r(1)*180/PI,jnt_pos_current_r(2)*180/PI,jnt_pos_current_r(3)*180/PI,jnt_pos_current_r(4)*180/PI,jnt_pos_current_r(5)*180/PI,jnt_pos_current_r(6)*180/PI);

            if(side_grasp){
                jnt_end_r = jnt_pos_current_l;
                jnt_end_l = jnt_pos_current_r;
            }else{
                jnt_end_r = jnt_pos_current_r;
                jnt_end_l = jnt_pos_current_l;
            }
            
        }
    #pragma endregion

    // handCtrl.leftHand({999,999,999,999,999,10}); 
    // handCtrl.rightHand({999,999,999,999,999,10}); 
    dataPub.doubleHand({999,999,999,999,999,10},{999,999,999,999,999,10});

    #pragma region ACTION1
        /*  
        1、左手抓取水果
            - 左手dmp抬手规划，右手不动
            - 左手抓取
        */
        int act1_done_flag = 0;

        Vector3d pos_grasp_pre;
        pos_grasp_pre << 0.35, pos_grasp(1)-0.05, pos_grasp(2)+0.08;

        /*
        移动到预备抓取位置
        */
        Plan_Res_MulDOF act1_pre_l;
        Plan_Res_MulDOF act1_pre_r;
        Vector3d pose_grasp_pre = Vector3d(PI/4, PI/12, -PI/2);
        act1_pre_l = dmpLib.action1_dmp(pos_grasp_pre,pose_grasp_pre,dt/1.5);
        if(act1_pre_l.error_flag){
            ROS_ERROR("ACTION1_INIT2GRASP_PRE PLANNING FAILED!");
            return 0;
        }
        int n1_pre = act1_pre_l.t.size();
        act1_pre_r = interLib.stay_still(jnt_end_r,n1_pre,dt);

        jnt_end_r = act1_pre_r.pos.row(n1_pre-1);
        jnt_end_l = act1_pre_l.pos.row(n1_pre-1);

        ROS_INFO("ACTION1 STARTING!");
        if(side_grasp){
            dataSave.save2txt(act1_pre_l,act1_pre_r,fpWrite);
            moveLib.move(act1_pre_l,act1_pre_r,ctrl_mode);
        }else{
            dataSave.save2txt(act1_pre_r,act1_pre_l,fpWrite);
            moveLib.move(act1_pre_r,act1_pre_l,ctrl_mode);
        }

        for (size_t i = 0; i < 7; i++)
        {
            cout << jnt_end_l(i) << ", " ;
        }

        // return 0;
        /*
        移动到抓取位置
        */
        if(side_grasp){
            array<double, 7> l_jnt_init, r_jnt_init;
            for (size_t i = 0; i < 7; i++)
            {
                r_jnt_init[i] = jnt_end_l[i];
                l_jnt_init[i] = jnt_end_r[i];
            }
            //左臂保持当前位置
            qp_grasp.push_back(l_jnt_init);
            qp_grasp.push_back(l_jnt_init);
            t_action.assign({100});
            action.left = robot.multi_joint_plan(qp_grasp, t_action, 0.001);        

            robot.kdl_fk(r_jnt_init, pose, right_arm);
            pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
            //固定高度
            // camera_data[2] =  -0.53;  
            vec << pos_grasp2[0] - 0.0, pos_grasp2[1], pos_grasp2[2] + 0.0;
            rpy << -1.5708, 0.523592, 0.785398;

            pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
            action.right = robot.line_plan(r_jnt_init, pose_start, pose_end, 5, right_arm);
            ROS_INFO("运动到抓取位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");

            jnt_end_l = action.right.pos.row(action.right.t.size()-1);
        }else{
            array<double, 7> l_jnt_init, r_jnt_init;
            for (size_t i = 0; i < 7; i++)
            {
                r_jnt_init[i] = jnt_end_r[i];
                l_jnt_init[i] = jnt_end_l[i];
            }
            //右臂保持当前位置
            qp_grasp.push_back(r_jnt_init);
            qp_grasp.push_back(r_jnt_init);
            t_action.assign({100});
            action.right = robot.multi_joint_plan(qp_grasp, t_action, 0.001);        

            robot.kdl_fk(l_jnt_init, pose, left_arm);
            pose_start << pose.position(0,3),pose.position(1,3),pose.position(2,3),pose.rpy[0],pose.rpy[1],pose.rpy[2];
            //固定高度
            // camera_data[2] =  -0.53;  
            vec << pos_grasp2[0] - 0.0, pos_grasp2[1], pos_grasp2[2] + 0.0;
            // rpy << 1.5708, 2.87979, 2.3562;
            rpy << 1.5708, 2.61799, 2.35619;
            // rpy << pose_end[3], pose_end[4], pose_end[5];
            // cout << rpy << endl;
            pose_end << vec[0], vec[1], vec[2], rpy[0], rpy[1], rpy[2];
            action.left = robot.line_plan(l_jnt_init, pose_start, pose_end, 5, left_arm);
            ROS_INFO("运动到抓取位置.....");
            robot.move(action, ctrl_mode);
            ROS_INFO("运动完成!");

            jnt_end_l = action.left.pos.row(action.left.t.size()-1);
        }
        // return 0;
    /*
        抓取手势10
    */
        if(side_grasp){
            // handCtrl.rightHand({200,350,550,550,750,10});
            dataPub.rightHand({200,350,550,550,750,10});
        }else{
            dataPub.leftHand({200,350,550,550,750,10});
            // handCtrl.leftHand({200,350,550,550,750,10});
        }

        ROS_INFO("ACTION1 FINISHED!\n");

        // return 0;

        act1_done_flag = 1;
    #pragma endregion

    #pragma region ACTION2
        /*
        2、左手移动到中间位置，手心向下，右手移动到抓取水果位置下方，手心向上
        */
        int act2_enable_flag = 1;
        if(!act1_done_flag || !act2_enable_flag){
            return 0;
        }

        VectorXd t_act2_l(2); 
        t_act2_l << 0, 9;
        VectorXd phi_act2_l(1); 
        phi_act2_l << -PI/2;
        MatrixXd pos_act2_l(1,3);
        pos_act2_l.row(0) = Vector3d(0.3 , -0.01, -0.04);
        MatrixXd pose_act2_l(1,3);
        pose_act2_l.row(0) = Vector3d(0, PI/6, -PI/2);
        Plan_Res_MulDOF action2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act2_l,pos_act2_l,pose_act2_l,phi_act2_l,dt);
        if(action2_l.error_flag){
            ROS_ERROR("ACTION2_L PLANNING FAILED!");
            return 0;
        }

        Vector3d pos_grasp_2nd;
        pos_grasp_2nd << 0.3, -0.01, -0.25;
        Vector3d pose_grasp_2nd;
        pose_grasp_2nd << 0, 0, PI/2+PI/12;
        Plan_Res_MulDOF action2_p1_r = dmpLib.action1_dmp(pos_grasp_2nd+Vector3d(0.05,-0.15,-0.02),Vector3d(PI/4, 0, PI/2),dt/1.5,1,true,-PI/2);
        if(action2_p1_r.error_flag){
            ROS_ERROR("ACTION2_PHASE1_R PLANNING FAILED!");
            return 0;
        }

        int n_act2_p1_r = action2_p1_r.t.size();
        VectorXd t_act2_p2_r(2);
        t_act2_p2_r << 0, 3;
        VectorXd phi_act2_p2_r(1);
        phi_act2_p2_r << -PI/2;
        MatrixXd pos_act2_p2_r(1,3);
        if(side_grasp){
            pos_act2_p2_r.row(0) = pos_grasp_2nd+Vector3d(0,0,0.03)+Vector3d(-0.005,0,0.028);
        }else{
            pos_act2_p2_r.row(0) = pos_grasp_2nd+Vector3d(0,0,0.03)+Vector3d(-0.03,0,0.028);
        }
        MatrixXd pose_act2_p2_r(1,3);
        pose_act2_p2_r.row(0) = pose_grasp_2nd;
        Plan_Res_MulDOF action2_p2_r = interPlan.quinitic_poly_inter_mulDOF(action2_p1_r.pos.row(n_act2_p1_r-1),t_act2_p2_r,pos_act2_p2_r,pose_act2_p2_r,phi_act2_p2_r,dt);
        if(action2_p2_r.error_flag){
            ROS_ERROR("ACTION2_PHASE2_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF action2_r = adjustFunc.traj_splic(action2_p1_r,action2_p2_r);
        
        int n_act2_r = action2_r.t.size();
        int n_act2_l = action2_l.t.size();
        int n_act2 = max(n_act2_r,n_act2_l);
        action2_r = adjustFunc.traj_extend(action2_r,n_act2,dt);
        action2_l = adjustFunc.traj_extend(action2_l,n_act2,dt);
        jnt_end_r = action2_r.pos.row(n_act2-1);
        jnt_end_l = action2_l.pos.row(n_act2-1);

        ROS_INFO("ACTION2 STARTING!");
        ROS_INFO("- action2_prepare_change_hand starting...");
        /*
        抓取手势11
        */
        if(side_grasp){
            dataPub.leftHand({999,999,999,999,800,300});
            
        }else{
            dataPub.rightHand({999,999,999,999,800,300});
        }        
        ROS_INFO("right hand grasp sucessed!");

        if(side_grasp){
            dataSave.save2txt(action2_l,action2_r,fpWrite);
            moveLib.move(action2_l,action2_r,ctrl_mode);
        }else{
            dataSave.save2txt(action2_r,action2_l,fpWrite);
            moveLib.move(action2_r,action2_l,ctrl_mode);
        }
        ROS_INFO("- action2_prepare_change_hand finished!");
        // return 0;
        /*
        松手
        */
        /*
        抓取手势11
        */
        if(side_grasp){
            dataPub.leftHand({600,600,600,600,800,300});
        }else{
            dataPub.rightHand({600,600,600,600,800,300});
        }        
        ROS_INFO("right hand grasp sucessed!");
        /*
        抓取手势2
        */
        if(side_grasp){
            dataPub.rightHand({999,999,999,999,999,999}, {400,400,400,400,400,400}); 
        }else{
            dataPub.leftHand({999,999,999,999,999,999}, {400,400,400,400,400,400});
        } 
        ROS_INFO("left hand release sucessed!");

        /*
        抓取手势10
        */
        if(side_grasp){
            dataPub.leftHand({200,350,550,550,750,200});
        }else{
            dataPub.rightHand({200,350,550,550,750,200});
        }         

        ROS_INFO("ACTION2 FINISHED!\n");

    #pragma endregion

    #pragma region ACTION3
        /*
        4、左手移动到待机位置，右手向前伸出，手心向上
        */
        int act3_enable_flag = 1;
        if(!act3_enable_flag){
            return 0;
        }

        VectorXd t_act3(3);
        t_act3 << 0, 3, 6;

        VectorXd phi_act3_r(2);
        phi_act3_r << -PI/2, -PI/4;
        MatrixXd pos_act3_r(2,3);
        pos_act3_r.row(0) = pos_grasp_2nd + Vector3d(0.05,-0.15,-0.02);
        pos_act3_r.row(1) = Vector3d(0.58, -0.23, -0.17);
        MatrixXd pose_act3_r(2,3);
        pose_act3_r.row(0) = Vector3d(PI/4, 0, PI/2);
        pose_act3_r.row(1) = Vector3d(PI/2.2, 0, PI/2);

        VectorXd phi_act3_l(2);
        phi_act3_l << -PI/4, -PI/6;
        MatrixXd pos_act3_l(2,3);
        pos_act3_l.row(0) = Vector3d(0.3, -0.01, -0.05) + Vector3d(0.05, -0.05, -0.0);
        pos_act3_l.row(1) = Vector3d(0.25, -0.2, -0.3);
        MatrixXd pose_act3_l(2,3);
        pose_act3_l.row(0) = Vector3d(PI/6, 0, -PI/2);
        pose_act3_l.row(1) = Vector3d(PI/4, 0, 0);

        Plan_Res_MulDOF act3_r = interPlan.cub_spline_inter_mulDOF(jnt_end_r,t_act3,pos_act3_r,pose_act3_r,phi_act3_r,MatrixXd::Zero(7,2),dt);
        if(act3_r.error_flag){
            ROS_ERROR("ACTION3_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_l = interPlan.cub_spline_inter_mulDOF(jnt_end_l,t_act3,pos_act3_l,pose_act3_l,phi_act3_l,MatrixXd::Zero(7,2),dt);
        if(act3_l.error_flag){
            ROS_ERROR("ACTION3_L PLANNING FAILED!");
            return 0;
        }

        int n3 = act3_r.t.size();
        jnt_end_r = act3_r.pos.row(n3-1);
        jnt_end_l = act3_l.pos.row(n3-1);

        ROS_INFO("ACTION3 STARTING!");
        ROS_INFO("- action3_give starting!");
        if(side_grasp){
            dataSave.save2txt(act3_l,act3_r,fpWrite);
            moveLib.move(act3_l,act3_r,ctrl_mode);
        }else{
            dataSave.save2txt(act3_r,act3_l,fpWrite);
            moveLib.move(act3_r,act3_l,ctrl_mode);
        }
        ROS_INFO("- action3_give finished!");

        /*
        抓取手势12
        */
        if(side_grasp){
            dataPub.leftHand({500,600,600,600,800,200});
        }else{
            dataPub.rightHand({500,600,600,600,800,200});
        } 

        ROS_INFO("right hand release sucessed!");

        ROS_INFO("ACTION3 FINISHED!\n");
        
    #pragma endregion

    #pragma region ACTION4
        /*
        5、回归初始位置
        */
        int act4_enable_flag = 0;
        if(!act4_enable_flag){
            return 0;
        }
        
        Vector3d pos_standby = Vector3d(0.3, -0.2, -0.25);
        Vector3d pose_standby = Vector3d(PI/3, 0, -PI/4);

        VectorXd t_act4(2);
        t_act4 << 0, 4;
        VectorXd phi_act4(1);
        phi_act4 << -PI/4;
        MatrixXd pos_act4(1,3);
        pos_act4.row(0) = pos_standby;
        MatrixXd pose_act4(1,3);
        pose_act4.row(0) = pose_standby;

        Plan_Res_MulDOF act4_phase1_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act4,pos_act4,pose_act4,phi_act4,dt);
        if(act4_phase1_r.error_flag){
            ROS_ERROR("ACTION4_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act4_phase1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act4,pos_act4,pose_act4,phi_act4,dt);
        if(act4_phase1_l.error_flag){
            ROS_ERROR("ACTION4_L PLANNING FAILED!");
            return 0;
        }

        Plan_Res_MulDOF act4_phase2 = dmpLib.action1_dmp(pos_standby,pose_standby,dt/1.5,1,true,phi_act4(0));
        if(act4_phase2.error_flag){
            ROS_ERROR("ACTION4_DMP PLANNING FAILED!");
            return 0;
        }
        int n_dmp = act4_phase2.t.size();
        MatrixXd pos_act4_dmp(n_dmp,7);
        MatrixXd vel_act4_dmp(n_dmp,7);
        MatrixXd acc_act4_dmp(n_dmp,7);
        for(int i=0;i<n_dmp;i++){
            pos_act4_dmp.row(i) = act4_phase2.pos.row(n_dmp-1-i);
            vel_act4_dmp.row(i) = act4_phase2.vel.row(n_dmp-1-i);
            acc_act4_dmp.row(i) = act4_phase2.acc.row(n_dmp-1-i);
        }
        Plan_Res_MulDOF act4_phase2_inv;
        act4_phase2_inv.pos = pos_act4_dmp;
        act4_phase2_inv.vel = vel_act4_dmp;
        act4_phase2_inv.acc = acc_act4_dmp;
        act4_phase2_inv.t = act4_phase2.t;

        Plan_Res_MulDOF act4_r = adjustFunc.traj_splic(act4_phase1_r,act4_phase2_inv);
        Plan_Res_MulDOF act4_l = adjustFunc.traj_splic(act4_phase1_l,act4_phase2_inv);

        ROS_INFO("ACTION4 STARTING!");
        ROS_INFO("- action4_return2init starting!");
        if(side_grasp){
            dataSave.save2txt(act4_l,act4_r,fpWrite);
            moveLib.move(act4_l,act4_r,ctrl_mode);
        }else{
            dataSave.save2txt(act4_r,act4_l,fpWrite);
            moveLib.move(act4_r,act4_l,ctrl_mode);
        }
        ROS_INFO("- action4_return2init finished!");
        ROS_INFO("ACTION4 FINISHED!\n");

    #pragma endregion

    fclose(fpWrite);
    return 0;
}