/*
welcome actions
    1.hand_wave
    2.hand_shake
    3.hand_heart
    4.like_gesture
    5.salute
    6.fist_palm_salute
*/

#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include "sensor_msgs/JointState.h"
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>


#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

double dt = 0.0015;
double tau = 1;

std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::INTER interLib;
ACT_LIB::MOVE moveLib;

FILE *fpWrite=fopen("res.txt","w");

float left_init_pos[7] = {0.0};
float right_init_pos[7] = {0.0};
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        left_init_pos[i] = msg->position[i];
        right_init_pos[i] = msg->position[i+7];
    }
    //关节位置校验
    for(int i = 0; i < 7; i++){
        if (left_init_pos[i] < -PI || left_init_pos[i] > PI){
            cout << "error: current Joint" << i+1 << " " << left_init_pos[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
        if (right_init_pos[i] < -PI || right_init_pos[i] > PI){
            cout << "error: current Joint" << i+1 << " " << right_init_pos[i]*180/PI << " exceeds the limit position[" << -180 << ", " << 180 << ']' << endl;
            ros::shutdown();
        }
    }
    return;
}
void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action03_welcome");
    ros::NodeHandle nh;
    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

	//启用多线程
 	ros::AsyncSpinner spinner(3); // Use 2 threads
 	spinner.start(); //开始标志

    CTRL_MODE mode = robot_ctrl;
    /*
    get current joint position
    */
    VectorXd jnt_pos_current_r(7);
    VectorXd jnt_pos_current_l(7);

    int hand_flag = 0;
    int istest = 0;

    // 检查是否有输入参数
    if(argc > 1) {
        // 将第一个参数转换为整数
        int a = atoi(argv[1]);
        if(a == 1){
            istest = 0;
            mode = rviz_ctrl;
            ROS_INFO("运行在仿真模式");
        } else if(a == 2){
            istest = 1;
            mode = rviz_ctrl;
            ROS_INFO("运行在测试模式");
        }
    }

    if(istest){
        // jnt_pos_current_r << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180;
        // jnt_pos_current_l << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180;
        jnt_pos_current_r << 0,-PI*5/180,0,0,0,0,0;
        jnt_pos_current_l << 0,-PI*5/180,0,0,0,0,0;
    }else{
        while(!flag && ros::ok())
        {
            ROS_INFO("waiting for initial position");
            usleep(1000);
            // ros::spinOnce();
        }
        ROS_INFO("left_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        left_init_pos[0]*180/PI,left_init_pos[1]*180/PI,left_init_pos[2]*180/PI,left_init_pos[3]*180/PI,left_init_pos[4]*180/PI,left_init_pos[5]*180/PI,left_init_pos[6]*180/PI);
        ROS_INFO("right_initpos: %.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f",
        right_init_pos[0]*180/PI,right_init_pos[1]*180/PI,right_init_pos[2]*180/PI,right_init_pos[3]*180/PI,right_init_pos[4]*180/PI,right_init_pos[5]*180/PI,right_init_pos[6]*180/PI);

        jnt_pos_current_l << left_init_pos[0], left_init_pos[1], left_init_pos[2], left_init_pos[3], left_init_pos[4], left_init_pos[5], left_init_pos[6];
        jnt_pos_current_r << right_init_pos[0], right_init_pos[1], right_init_pos[2], right_init_pos[3], right_init_pos[4], right_init_pos[5], right_init_pos[6];
    }
    
    KineRes kin_r = kineFunc.fkine(jnt_pos_current_r);
    KineRes kin_l = kineFunc.fkine(jnt_pos_current_l);
    Vector3d cart_pos_current_r = kin_r.cart_end_position;
    Vector3d cart_pos_current_l = kin_l.cart_end_position;
    Vector3d pose_current_r;
    pose_current_r << PI/2 - kin_r.eul_r,
                      kin_r.eul_p,
                      kin_r.eul_y;
    Vector3d pose_current_l;
    pose_current_l << PI/2 - kin_l.eul_r,
                      kin_l.eul_p,
                      kin_l.eul_y;

    Plan_Res_MulDOF action_r;
    Plan_Res_MulDOF action_l;
    // char key_input;
    // ROS_INFO("PLEASE INPUT ACTION TYPE: \n1.hand_wave 2.hand_shake 3.hand_heart 4.like_gesture 5.salute 6.fist_palm_salute");
    // key_input = getchar();
    // int action_type = atoi(&key_input);
    int action_type = 1;
    switch (action_type){
        case 1:{
            /*
            ACTION1: HAND_WAVE
            */
            Vector3d pos_hand_raise;
            pos_hand_raise << 0.3, -0.3, 0.25;
            Vector3d pos_hand_wave1;
            Vector3d pos_hand_wave2;
            pos_hand_wave1 = pos_hand_raise + Vector3d(0,0.05,-0.01);
            pos_hand_wave2 = pos_hand_raise + Vector3d(0,-0.05,-0.01);
            Vector3d pose_hand_raise;
            pose_hand_raise << PI, -PI/2, 0;
            Vector3d pose_hand_wave1;
            Vector3d pose_hand_wave2;
            pose_hand_wave1 << PI, -PI/2-PI/8, 0;
            pose_hand_wave2 << PI, -PI/2+PI/20, 0;

     
            VectorXd t_act1_r(10);
            t_act1_r << 0, 5, 6, 7.5, 9, 10.5, 12, 13.5, 14.5, 19.5;
            VectorXd phi_act1_r(9);
            phi_act1_r << -PI/10, -PI/10, 0, -PI/10, 0, -PI/10, 0, -PI/10, 0;
            MatrixXd pos_act1_r(9,3);
            pos_act1_r.row(0) = pos_hand_raise;
            pos_act1_r.row(1) = pos_hand_wave1;
            pos_act1_r.row(2) = pos_hand_wave2;
            pos_act1_r.row(3) = pos_hand_wave1;
            pos_act1_r.row(4) = pos_hand_wave2;
            pos_act1_r.row(5) = pos_hand_wave1;
            pos_act1_r.row(6) = pos_hand_wave2;
            pos_act1_r.row(7) = pos_hand_raise;
            pos_act1_r.row(8) = cart_pos_current_r;
            
            MatrixXd pose_act1_r(9,3);
            pose_act1_r.row(0) = pose_hand_raise;
            pose_act1_r.row(1) = pose_hand_wave1;
            pose_act1_r.row(2) = pose_hand_wave2;
            pose_act1_r.row(3) = pose_hand_wave1;
            pose_act1_r.row(4) = pose_hand_wave2;
            pose_act1_r.row(5) = pose_hand_wave1;
            pose_act1_r.row(6) = pose_hand_wave2;
            pose_act1_r.row(7) = pose_hand_raise;
            pose_act1_r.row(8) = pose_current_r;

            MatrixXd vel_ends = MatrixXd::Zero(7,2);
            
            action_r = interPlan.cub_spline_inter_mulDOF(jnt_pos_current_r, t_act1_r, pos_act1_r, pose_act1_r, phi_act1_r, vel_ends, dt);
            if(action_r.error_flag){
                ROS_ERROR("MOTION1 PLANNING FAILED!");
                return 0;
            }
            int n1 = action_r.t.size();
            action_l = interLib.stay_still(jnt_pos_current_l, n1, dt);
            break;
        }
        case 2:{
            /*
            ACTION2: HAND_SHAKE
            */
            Vector3d pos_hand_shake;
            pos_hand_shake << 0.45, -0.08, -0.33;
            Vector3d pose_hand_shake;
            pose_hand_shake << PI/3, PI/8, 0;

            VectorXd t_act2_r(2);
            t_act2_r << 0, 5;
            VectorXd phi_act2_r(1);
            phi_act2_r << -PI/4;
            MatrixXd pos_act2_r(1,3);
            pos_act2_r.row(0) = pos_hand_shake;
            MatrixXd pose_act2_r(1,3);
            pose_act2_r.row(0) = pose_hand_shake;

            action_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r, t_act2_r, pos_act2_r, pose_act2_r, phi_act2_r, dt);
            if(action_r.error_flag){
                ROS_ERROR("MOTION2 PLANNING FAILED!");
                return 0;
            }
            int n2 = action_r.t.size();
            action_l = interLib.stay_still(jnt_pos_current_l, n2, dt);

            hand_flag = 2;
            break;

        }
        case 5:{
            /*
            ACTION5: SALUTE
            */
            Vector3d pos_salute1;
            Vector3d pos_salute2;
            pos_salute1 << 0.2, -0.15, 0.25;
            pos_salute2 = pos_salute1 + Vector3d(0.1, 0, 0.02);
            Vector3d pose_salute1;
            Vector3d pose_salute2;
            pose_salute1 << -PI/8, -PI/6, -PI/2;
            pose_salute2 << PI/8, -PI/6, -PI*2.3/4;

            VectorXd t_act5_r(5);
            t_act5_r << 0, 5, 6, 6.5, 11.5;
            VectorXd phi_act5_r(4);
            phi_act5_r << -PI/6, -PI/5, -PI/5, 0;
            MatrixXd pos_act5_r(4,3);
            pos_act5_r.row(0) = pos_salute1;
            pos_act5_r.row(1) = pos_salute2;
            pos_act5_r.row(2) = pos_salute2;
            pos_act5_r.row(3) = cart_pos_current_r;
            MatrixXd pose_act5_r(4,3);
            pose_act5_r.row(0) = pose_salute1;
            pose_act5_r.row(1) = pose_salute2;
            pose_act5_r.row(2) = pose_salute2;
            pose_act5_r.row(3) = pose_current_r;

            action_r = interPlan.quinitic_poly_inter_mulDOF(jnt_pos_current_r, t_act5_r, pos_act5_r, pose_act5_r, phi_act5_r, dt);
            if(action_r.error_flag){
                ROS_ERROR("MOTION5 PLANNING FAILED!");
                return 0;
            }
            int n5 = action_r.t.size();
            action_l = interLib.stay_still(jnt_pos_current_l, n5, dt);
            break;
        }
        default:{
            ROS_ERROR("WRONG ACTION NUMBER!");
            return 0;
        }
    }
    
    dataSave.save2txt(action_r,action_l,fpWrite);
    fclose(fpWrite);
    moveLib.move(action_r,action_l,mode);

    /*
    手部抓取
    */
    sleep(2);
    if(hand_flag == 2){
        grasp_msg.data = 6;//抓取
        while(!grasp_finshed_l && !grasp_finshed_r && ros::ok())
        {
            ROS_INFO("hand is grasping!"); 
            // grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            // ros::spinOnce();
            usleep(1000);
        }
        // sleep(4);
        ROS_INFO("hand grasp sucessed!"); 
        sleep(4);
        
        int count = 0;
        grasp_msg.data = 4;//释放
        while(count < 1000)
        {
            // grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        // sleep(4);
        ROS_INFO("hand release sucessed!"); 
    }
    
    
    
    return 0;
}
