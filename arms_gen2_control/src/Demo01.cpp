#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include "sensor_msgs/JointState.h"
#include "std_msgs/Float64.h"

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

double l0 = 0.2536;
double l1 = 0.2486;
double l2 = 0.1745;
double l3 = 0.07;
double l4 = 0.08;

double dt = 0.001;

DATA_PROC_FUNC::DATA_SAVE dataSave;
DATA_PROC_FUNC::DATA_PUB dataPub;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Demo01");
    ros::NodeHandle nh;
    ros::Publisher pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1000);
    ros::Publisher pub_r_jnt1_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint1_position_controller/command",10);
    ros::Publisher pub_r_jnt2_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint2_position_controller/command",10);
    ros::Publisher pub_r_jnt3_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint3_position_controller/command",10);
    ros::Publisher pub_r_jnt4_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint4_position_controller/command",10);
    ros::Publisher pub_r_jnt5_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint5_position_controller/command",10);
    ros::Publisher pub_r_jnt6_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint6_position_controller/command",10);
    ros::Publisher pub_r_jnt7_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/r_joint7_position_controller/command",10);
    ros::Publisher pub_l_jnt1_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint1_position_controller/command",10);
    ros::Publisher pub_l_jnt2_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint2_position_controller/command",10);
    ros::Publisher pub_l_jnt3_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint3_position_controller/command",10);
    ros::Publisher pub_l_jnt4_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint4_position_controller/command",10);
    ros::Publisher pub_l_jnt5_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint5_position_controller/command",10);
    ros::Publisher pub_l_jnt6_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint6_position_controller/command",10);
    ros::Publisher pub_l_jnt7_gazebo = nh.advertise<std_msgs::Float64>("/Assem4/l_joint7_position_controller/command",10);
    sensor_msgs::JointState jnt_state_msgs;
    std_msgs::Float64 jnt_state_msgs_gazebo;


    VectorXd jnt_init(7);
    jnt_init << 0,-PI*5/180,0,0,0,0,0;
    // Vector3d pose_init;
    // // pose_init << PI, PI/2-PI*5/180, -PI/2;
    // KineRes kin = kineFunc.fkine(jnt_init);
    // Vector3d cart_pos_init = kin.cart_end_position;
    // pose_init << PI/2 - kin.eul_r, kin.eul_p, kin.eul_y;
    // JntPos ikin = kineFunc.invKine_JntPos(cart_pos_init,0,pose_init);
    // // cout << cart_pos_init << endl;
    // // cout << kin.eul_r/PI*180 << endl;
    // // cout << kin.eul_p/PI*180 << endl;
    // // cout << kin.eul_y/PI*180 << endl;
    // // cout << ikin.theta*180/PI << endl;

    Vector3d pose_end1;
    Vector3d pose_end2;
    Vector3d cart_pos_end;
    Vector3d phi;
    pose_end1 << PI/4, PI/4, -PI/2;
    pose_end2 << PI/4, PI/4, 0;
    // cart_pos_start << 0, -l0, -(l1+l2+l3+l4);
    cart_pos_end << 0.35, -0.15, -0.25;
    phi << 0, -PI/2, -PI/2;

    // // cout << kin.cart_end_position << endl;
    // // cout << cart_pos_start << endl;
    // // cout << kin.cart_end_position - cart_pos_start << endl;
    // // JntPos ikin1 = kineFunc.invKine_JntPos(cart_pos_start,0,pose_start);

    Vector3d t_seg;
    MatrixXd position_seg(2,3);
    MatrixXd pose_seg(2,3);
    Vector2d phi_seg;

    t_seg << 0,5,8;
    phi_seg << -PI/2,-PI/2;
    position_seg.row(0) = cart_pos_end;
    position_seg.row(1) = cart_pos_end;
    pose_seg.row(0) = pose_end1;
    pose_seg.row(1) = pose_end2;
    MatrixXd vel_ends(7,2);
    vel_ends << 0,0,
                0,0,
                0,0,
                0,0,
                0,0,
                0,0,
                0,0;

    Plan_Res_MulDOF motion1 = interPlan.quinitic_poly_inter_mulDOF(jnt_init,t_seg,position_seg,pose_seg,phi_seg,dt);
    if(motion1.error_flag){
        ROS_ERROR("MOTION1 PLANNING FAILED!");
        return 0;
    }
    Plan_Res_MulDOF motion2 = interPlan.cub_spline_inter_mulDOF(jnt_init,t_seg,position_seg,pose_seg,phi_seg,vel_ends,dt);
    if(motion2.error_flag){
        ROS_ERROR("MOTION2 PLANNING FAILED!");
        return 0;
    }
    // cout << motion1.t.rows() << endl;
    // cout << motion2.t.rows() << endl;
    dataSave.save2txt(motion1,motion2,fpWrite);
    fclose(fpWrite);
    
    // dataPub.pub2rviz(motion1,motion2);
    dataPub.pub2gazebo(motion1,motion2);
    
    return 0;
}
