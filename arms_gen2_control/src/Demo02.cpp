#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include "sensor_msgs/JointState.h"
#include "std_msgs/Float64.h"

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

double dt = 0.0005;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
DATA_PROC_FUNC::DATA_PUB dataPub;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Demo02");
    ros::NodeHandle nh;

    Vector3d pose_start;
    Vector3d pose_end1;
    Vector3d pose_end2;
    Vector3d cart_pos_start;
    Vector3d cart_pos_end;

    pose_end1 << PI/4, 0, -PI/2;
    pose_end2 << PI/4, PI/4, 0;
    cart_pos_end << 0.35, -0.2, -0.15;
    // Vector3d bias_pos_grasp;
    // bias_pos_grasp << -0.005, 0, 0.005;
    
    Plan_Res_MulDOF motion1 = dmpLib.action1_dmp(cart_pos_end,pose_end1,dt,1);
    if(motion1.error_flag){
        ROS_ERROR("MOTION1 PLANNING FAILED!");
        return 0;
    }

    int n1 = motion1.t.rows();
    cout << motion1.pos.row(n1-1)*180/PI<<endl;

    VectorXd t_motion2(2);
    t_motion2 << 0, 4;
    VectorXd phi_motion2(1);
    phi_motion2 << -PI/2;
    MatrixXd pos_motion2(1,3);
    pos_motion2.row(0) = cart_pos_end;
    MatrixXd pose_motion2(1,3);
    pose_motion2.row(0) = pose_end2;
    Plan_Res_MulDOF motion2 = interPlan.quinitic_poly_inter_mulDOF(motion1.pos.row(n1-1),t_motion2,pos_motion2,pose_motion2,phi_motion2,dt);

    Plan_Res_MulDOF motion = adjustFunc.traj_splic(motion1,motion2);

    dataSave.save2txt(motion1,motion1,fpWrite);
    fclose(fpWrite);
    
    // dataPub.pub2rviz(motion1,motion1);
    dataPub.pub2motors(motion1,motion1);
    
    return 0;
}
