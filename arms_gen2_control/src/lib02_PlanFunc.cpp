#include "string"
#include <stdio.h>
#include <iostream>
#include <fstream>

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

#define l0 0.2536
#define l1 0.2486
#define l2 0.1745
#define l3 0.07
#define l4 0.12

PLANNING_FUNC::DMP_FUNC dmpFunc;
PLANNING_FUNC::KINEMATICS kineFunc;
PLANNING_FUNC::INTERPOLATION interPlanFunc;

namespace TRAJ_PLAN{
    Plan_Res_MulDOF DMP::dmp(string pathJntPos, string pathAngArm, Train_Par trainPar, VectorXd theta_init, Vector3d x_goal,Vector3d pose, Vector4d jntLimit, double dt, double tau, bool useSelfDefPhi, double phi, bool ikine_info_show, bool plan_info_show){

        Plan_Res_MulDOF res;

        Demon_Traj data;
        data = dmpFunc.dataExt(pathJntPos,pathAngArm);
    
        Train_Res trainRes;
        trainRes = dmpFunc.dmpTrain(data,trainPar);
        int n = data.ArmJnt.size();
        double phi_start = data.ArmJnt(0);
        double phi_end = data.ArmJnt(n-1);

        if(useSelfDefPhi){
            phi_end = phi;
        }

        JntPos jnt_pos_end;
        jnt_pos_end = kineFunc.invKine_JntPos(x_goal,phi_end,pose);
        if(!jnt_pos_end.solved_flag || jnt_pos_end.error_flag){
            cout << "error: solved_flag [" << jnt_pos_end.solved_flag << "], error_flag [" << jnt_pos_end.error_flag << "]" << endl;
            res.error_flag = 1;
        }

        VectorXd theta_goal = jnt_pos_end.theta;
        KineRes cart_position_goal = kineFunc.fkine(theta_goal);

        Regress_Par regPar;
        regPar.dt = dt;
        regPar.tau = tau;
        regPar.x_goal = theta_goal.head(4);
        regPar.x_init = theta_init.head(4);
        // cout << theta_init << endl;
        // cout << regPar.x_init << endl;
        
        Plan_Res_MulDOF regRes;
        regRes = dmpFunc.dmpRegress(trainRes,regPar);
        regRes = dmpFunc.limitAmplitude(regRes,theta_init.head(4),theta_goal.head(4),jntLimit);
        regRes = dmpFunc.lnrCmp(regRes,theta_goal.head(4));
        int n1 = regRes.t.size();

        double t_end = regRes.t(n1-1);
        VectorXd inter_t_seg(3);
        VectorXd inter_jnt5_pos_seg(3);
        VectorXd inter_jnt6_pos_seg(3);
        VectorXd inter_jnt7_pos_seg(3);
        inter_t_seg << 0, t_end/2, t_end;
        inter_jnt5_pos_seg << theta_init(4), theta_init(4), theta_goal(4);
        inter_jnt6_pos_seg << theta_init(5), theta_init(5), theta_goal(5);
        inter_jnt7_pos_seg << theta_init(6), theta_init(6), theta_goal(6);
        Plan_Res inter_jnt5_traj = interPlanFunc.quinitic_poly_inter(inter_t_seg,inter_jnt5_pos_seg,dt);
        Plan_Res inter_jnt6_traj = interPlanFunc.quinitic_poly_inter(inter_t_seg,inter_jnt6_pos_seg,dt);
        Plan_Res inter_jnt7_traj = interPlanFunc.quinitic_poly_inter(inter_t_seg,inter_jnt7_pos_seg,dt);
        int n2 = inter_jnt5_traj.t.size();

        MatrixXd res_pos(n1,7);
        MatrixXd res_vel(n1,7);
        MatrixXd res_acc(n1,7);
        for(int i = 0; i < 4; i++){
            res_pos.col(i) = regRes.pos.col(i);
            res_vel.col(i) = regRes.vel.col(i);
            res_acc.col(i) = regRes.acc.col(i);
        }
        for(int i = 0; i < n1; i++){
            if(i < n2){
                res_pos(i,4) = inter_jnt5_traj.pos(i);
                res_pos(i,5) = inter_jnt6_traj.pos(i);
                res_pos(i,6) = inter_jnt7_traj.pos(i);
                res_vel(i,4) = inter_jnt5_traj.vel(i);
                res_vel(i,5) = inter_jnt6_traj.vel(i);
                res_vel(i,6) = inter_jnt7_traj.vel(i);
                res_acc(i,4) = inter_jnt5_traj.acc(i);
                res_acc(i,5) = inter_jnt6_traj.acc(i);
                res_acc(i,6) = inter_jnt7_traj.acc(i);
            }else{
                res_pos(i,4) = inter_jnt5_traj.pos(n2-1);
                res_pos(i,5) = inter_jnt6_traj.pos(n2-1);
                res_pos(i,6) = inter_jnt7_traj.pos(n2-1);
                res_vel(i,4) = inter_jnt5_traj.vel(n2-1);
                res_vel(i,5) = inter_jnt6_traj.vel(n2-1);
                res_vel(i,6) = inter_jnt7_traj.vel(n2-1);
                res_acc(i,4) = inter_jnt5_traj.acc(n2-1);
                res_acc(i,5) = inter_jnt6_traj.acc(n2-1);
                res_acc(i,6) = inter_jnt7_traj.acc(n2-1);
            }
            
        }
        // res_pos.col(4) = inter_jnt5_traj.pos;
        // res_pos.col(5) = inter_jnt6_traj.pos;
        // res_pos.col(6) = inter_jnt7_traj.pos;
        // res_vel.col(4) = inter_jnt5_traj.vel;
        // res_vel.col(5) = inter_jnt6_traj.vel;
        // res_vel.col(6) = inter_jnt7_traj.vel;
        // res_acc.col(4) = inter_jnt5_traj.acc;
        // res_acc.col(5) = inter_jnt6_traj.acc;
        // res_acc.col(6) = inter_jnt7_traj.acc;

        if(ikine_info_show){
            cout << "res_ikine: " << theta_goal(0)*180/PI << ", " << theta_goal(1)*180/PI << ", " << theta_goal(2)*180/PI << ", " << theta_goal(3)*180/PI << ", " << theta_goal(4)*180/PI << ", " << theta_goal(5)*180/PI << ", " << theta_goal(6)*180/PI << endl;
            cout << "cart_position_set: " << "[" << x_goal(0) << ", " << x_goal(1) << ", " << x_goal(2) << "]" << endl;
            cout << "cart_position " << "[" << cart_position_goal.cart_end_position(0) << ", " << cart_position_goal.cart_end_position(1) << ", " << cart_position_goal.cart_end_position(2) << "]" << endl;
        }
        if(plan_info_show){
            cout << "jnt_position_start_set: " << theta_init(0)*180/PI << ", " << theta_init(1)*180/PI << ", " << theta_init(2)*180/PI << ", " << theta_init(3)*180/PI << ", " << theta_init(4)*180/PI << ", " << theta_init(5)*180/PI << ", " << theta_init(6)*180/PI << endl;
            cout << "jnt_position_end_set: " << theta_goal(0)*180/PI << ", " << theta_goal(1)*180/PI << ", " << theta_goal(2)*180/PI << ", " << theta_goal(3)*180/PI << ", " << theta_goal(4)*180/PI << ", " << theta_goal(5)*180/PI << ", " << theta_goal(6)*180/PI << endl;
            cout << "jnt_position_start: " << res_pos(0,0)*180/PI << ", " << res_pos(0,1)*180/PI << ", " << res_pos(0,2)*180/PI << ", " << res_pos(0,3)*180/PI << ", " << res_pos(0,4)*180/PI << ", " << res_pos(0,5) << ", " << res_pos(0,6) << endl; 
            cout << "jnt_position_end " << res_pos(n1-1,0)*180/PI << ", " << res_pos(n1-1,1)*180/PI << ", " << res_pos(n1-1,2)*180/PI << ", " << res_pos(n1-1,3)*180/PI << ", " << res_pos(n1-1,4)*180/PI << ", " << res_pos(n1-1,5)*180/PI << ", " << res_pos(n1-1,6)*180/PI << endl;
        }
        
        res.t = regRes.t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;

        return res;
    }

    Plan_Res_MulDOF INTERPOLATION::quinitic_poly_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, double dt){
        int m = position_seg.rows() + 1; //经过点数
        MatrixXd jntPos_seg(7,m);
        int error_flag = 0;
        jntPos_seg.col(0) = jnt_pos_init;
        JntPos jnt_pos;
        for(int i = 1; i < m; i++){
            jnt_pos = kineFunc.invKine_JntPos(position_seg.row(i-1), phi(i-1), pose_seg.row(i-1));
            if(!jnt_pos.solved_flag || jnt_pos.error_flag){
                error_flag = 1;
                cout << "error: position[" << i << "], solve_flag[" << jnt_pos.solved_flag << "], error_flag[" << jnt_pos.error_flag << "]" << endl;
            }
            jntPos_seg.col(i) = jnt_pos.theta;
        }
        Plan_Res res_singleDOF;
        res_singleDOF = interPlanFunc.quinitic_poly_inter(t_seg,jntPos_seg.row(0),dt);
        
        int n = jntPos_seg.rows(); //关节数
        int num = res_singleDOF.pos.rows();
        MatrixXd x = MatrixXd::Zero(num,n);
        MatrixXd dx = MatrixXd::Zero(num,n);
        MatrixXd ddx = MatrixXd::Zero(num,n);
        VectorXd time(num);
        for(int i; i < n; i++){
            res_singleDOF = interPlanFunc.quinitic_poly_inter(t_seg,jntPos_seg.row(i),dt);
            x.col(i) = res_singleDOF.pos;
            dx.col(i) = res_singleDOF.vel;
            ddx.col(i) = res_singleDOF.acc;
            time = res_singleDOF.t;
        }

        Plan_Res_MulDOF res;
        res.t = time;
        res.pos = x;
        res.vel = dx;
        res.acc = ddx;
        res.error_flag = error_flag;
        return res;
    }

    Plan_Res_MulDOF INTERPOLATION::cub_spline_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, MatrixXd vel_ends, double dt){
        int m = position_seg.rows() + 1; //经过点数
        MatrixXd jntPos_seg(7,m);
        int error_flag = 0;
        jntPos_seg.col(0) = jnt_pos_init;
        JntPos jnt_pos;
        for(int i = 1; i < m; i++){
            jnt_pos = kineFunc.invKine_JntPos(position_seg.row(i-1), phi(i-1), pose_seg.row(i-1));
            if(!jnt_pos.solved_flag || jnt_pos.error_flag){
                error_flag = 1;
                cout << "error: position[" << i << "], solve_flag[" << jnt_pos.solved_flag << "], error_flag[" << jnt_pos.error_flag << "]" << endl;
            }
            jntPos_seg.col(i) = jnt_pos.theta;
        }
        Plan_Res res_singleDOF;
        res_singleDOF = interPlanFunc.cub_spline_inter(t_seg,jntPos_seg.row(0),dt,vel_ends(0,0),vel_ends(0,1));
        
        int n = jntPos_seg.rows();
        int cnt = res_singleDOF.pos.rows();

        VectorXd res_t = VectorXd::Zero(cnt);
        MatrixXd res_pos = MatrixXd::Zero(cnt,n);
        MatrixXd res_vel = MatrixXd::Zero(cnt,n);
        MatrixXd res_acc = MatrixXd::Zero(cnt,n);
        
        for(int i = 0; i < n; i++){
            res_singleDOF = interPlanFunc.cub_spline_inter(t_seg,jntPos_seg.row(i),dt,vel_ends(i,0),vel_ends(i,1));
            res_t = res_singleDOF.t;
            res_pos.col(i) = res_singleDOF.pos;
            res_vel.col(i) = res_singleDOF.vel;
            res_acc.col(i) = res_singleDOF.acc;
        }

        Plan_Res_MulDOF res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;
        res.error_flag = error_flag;

        return res;
    }

    Plan_Res_MulDOF ADJUST::traj_splic(Plan_Res_MulDOF action1, Plan_Res_MulDOF action2){
        int n1 = action1.t.rows();
        int n2 = action2.t.rows();
        int n = n1 + n2;
        VectorXd res_t(n);
        MatrixXd res_pos(n,7);
        MatrixXd res_vel(n,7);
        MatrixXd res_acc(n,7);
        res_t.head(n1) = action1.t;
        res_t.tail(n2) = action2.t.array() + action1.t(n1-1);
        res_pos.block(0,0,n1,7) = action1.pos;
        res_pos.block(n1,0,n2,7) = action2.pos;
        res_vel.block(0,0,n1,7) = action1.vel;
        res_vel.block(n1,0,n2,7) = action2.vel;
        res_acc.block(0,0,n1,7) = action1.acc;
        res_acc.block(n1,0,n2,7) = action2.acc;

        Plan_Res_MulDOF res;
        res.t = res_t;
        res.pos = res_pos;
        res.vel = res_vel;
        res.acc = res_acc;

        return res;
    }

    Plan_Res_MulDOF ADJUST::traj_extend(Plan_Res_MulDOF action, int n, double dt){
        int n0 = action.t.size();

        Plan_Res_MulDOF res;
        if(n0 == n){
            res = action;
            // cout << 1 << endl;
        }else if(n0 > n){
            res = action;
            res.error_flag = 1;
            cout << "error: The expansion length is smaller than the original length!" << endl;
            // cout << 2 << endl;

        }else{
            VectorXd res_t(n);
            MatrixXd res_pos(n,7);
            MatrixXd res_vel(n,7);
            MatrixXd res_acc(n,7);
            res_t.head(n0) = action.t;
            res_pos.block(0,0,n0,7) = action.pos;
            res_vel.block(0,0,n0,7) = action.vel;
            res_acc.block(0,0,n0,7) = action.acc;
            for(int i = n0; i < n; i++){
                res_t(i) = action.t(n0-1) + (i-n0+1)*dt;
                res_pos.row(i) = action.pos.row(n0-1);
                res_vel.row(i) = action.vel.row(n0-1);
                res_acc.row(i) = action.acc.row(n0-1);  
            }
            res.t = res_t;
            res.pos = res_pos;
            res.vel = res_vel;
            res.acc = res_acc;
            // cout << 3 << endl;

        }
        return res;
    }
}