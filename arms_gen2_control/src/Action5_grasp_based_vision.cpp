#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592654

double dt = 0.0005;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::MOVE moveLib;
ACT_LIB::INTER interLib;
FILE *fpWrite=fopen("res.txt","w");


std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }
    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action05_grasp_based_vision");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

    /*
    ctrl_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
   CTRL_MODE ctrl_mode = rviz_ctrl;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    Vector3d bias_hand1;
    Vector3d bias_hand2;
    bias_hand1 << 0.0, 0.06, 0.0;
    bias_hand2 << 0.0, 0.06, 0.02;

    /*
    set standby position
    */ 
    Vector3d cart_pos_standby;
    cart_pos_standby << 0.35, -0.25, -0.1;
    Vector3d pose_standby;
    pose_standby << PI/4, 0, -PI/2;

    /*
    set init position
    */ 
    VectorXd jnt_init(7);
    jnt_init << 0,-PI*5/180,0,0,0,0,0;
    Vector3d pose_init;
    KineRes kin = kineFunc.fkine(jnt_init);
    Vector3d cart_pos_init = kin.cart_end_position;
    pose_init << PI/2 - kin.eul_r, kin.eul_p, kin.eul_y;

    /*
    set pour position
    */
    double L = 0.17;
    Vector3d cart_pos_hold;
    cart_pos_hold << 0.35, -0.05, -0.2;
    Vector3d pose_hold1;
    Vector3d pose_hold2;
    pose_hold1 << PI/4, 0, 0;
    pose_hold2 << PI/4, 0, 0;
    Vector3d bias_pos_hold1 = kineFunc.biasHand(pose_hold1, bias_hand1);
    Vector3d bias_pos_hold2 = kineFunc.biasHand(pose_hold2, bias_hand1);

    Vector3d cart_pos_pour;
    cart_pos_pour << cart_pos_hold(0)+L*sin(PI/4), -(cart_pos_hold(1)+L*cos(PI/4)), -0.1;
    Vector3d pose_pour1;
    Vector3d pose_pour2;
    pose_pour1 << PI/4, 0, 0;
    pose_pour2 << PI/4, 0, -PI/2;
    Vector3d bias_pos_pour1 = kineFunc.biasHand(pose_pour1, bias_hand1);
    Vector3d bias_pos_pour2 = kineFunc.biasHand(pose_pour2, bias_hand1);


    /*
    set/get current position
    */ 
    while(!flag && ros::ok())
    {
        ROS_INFO("waiting for initial position");
        usleep(1000);
        ros::spinOnce();
    }

/*     jnt_pos_current_r << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180;
    jnt_pos_current_l << PI*39.4737/180, -PI*64.2084/180, -PI*51.3894/180, PI*103.035/180, -PI*22.0213/180, PI*12.5936/180, -PI*3.9285/180; */
   // jnt_pos_current_r << 0,-PI*5/180,0,0,0,0,0;
    // jnt_pos_current_l << 0,-PI*5/180,0,0,0,0,0;
    Vector3d cart_pos_current_r = kineFunc.fkine(jnt_pos_current_r).cart_end_position;
    Vector3d cart_pos_current_l = kineFunc.fkine(jnt_pos_current_l).cart_end_position;

    /*
    get grasp position
    */ 
    Vector3d cart_pos_grasp_l;
    Vector3d pose_grasp_l;

    #pragma region GET POSITIONS BASED ON VISION
    ROS_INFO("Get postions based on vision");
    while(!detect_flag && ros::ok())
    {
        ros::spinOnce();
        usleep(1000);
        ROS_INFO("no detect");
    }
    cout << container.data() << endl;
    /* 接受两个目标物体的位置信息 */
    while (container.size() >= 4) 
    {
        cart_pos_grasp_l(0) = container[0];
        cart_pos_grasp_l(1) = -container[1];
        cart_pos_grasp_l(2) = container[2];
        container.erase(container.begin(), container.begin() + 4);
    }
    cart_pos_grasp_l(0) -= 0.02;
    cart_pos_grasp_l(1) -= 0.05;
    // cart_pos_grasp_l(2) += 0.10;

    cout << "cart_pos_grasp_l: " << cart_pos_grasp_l.transpose() << endl;
    #pragma endregion
    pose_grasp_l << PI/2, 0, 0;

    int act1_done_flag = 0;
    /*
    traj_init2grasp
    */ 
    Vector3d bias_pos_grasp;
    bias_pos_grasp << -0.07, -0.03, 0.03;
    Plan_Res_MulDOF action1_init2grasp_bias_l = dmpLib.action1_dmp(cart_pos_grasp_l+bias_pos_grasp,pose_grasp_l,dt);
    if(action1_init2grasp_bias_l.error_flag){
        ROS_ERROR("ACTION1_INIT2GRASP_BIAS_L PLANNING FAILED!");
        return 0;
    }
    int n1 = action1_init2grasp_bias_l.t.size();

    VectorXd t_act1_traj1(2);
    t_act1_traj1 << 0, 2;
    VectorXd phi_act1_traj1(1);
    phi_act1_traj1 << -PI/4;
    MatrixXd pos_act1_traj1(1,3);
    MatrixXd pose_act1_traj1(1,3);
    pos_act1_traj1.row(0) = cart_pos_grasp_l;
    pose_act1_traj1.row(0) = pose_grasp_l;
    Plan_Res_MulDOF action1_bias2grasp_l = interPlan.quinitic_poly_inter_mulDOF(action1_init2grasp_bias_l.pos.row(n1-1),t_act1_traj1,pos_act1_traj1,pose_act1_traj1,phi_act1_traj1,dt);
    if(action1_bias2grasp_l.error_flag){
        ROS_ERROR("ACTION1_BIAS2GRASP_L INTERPOLATION FAILED!");
        return 0;
    }
    Plan_Res_MulDOF action1_traj1_l = adjustFunc.traj_splic(action1_init2grasp_bias_l,action1_bias2grasp_l);

    int n = action1_traj1_l.t.size();
    cout << n << endl;
    Plan_Res_MulDOF action1_r;
    Plan_Res_MulDOF action1_l;
    action1_r = interLib.stay_still(jnt_pos_current_r,n,dt);
    
    action1_l = action1_traj1_l;
    jnt_end_r = action1_r.pos.row(n-1);
    jnt_end_l = action1_l.pos.row(n-1);

    dataSave.save2txt(action1_r,action1_l,fpWrite);
    moveLib.move(action1_r,action1_l,ctrl_mode);
    fclose(fpWrite);


    /*
    手部抓取
    */
    grasp_msg.data = 5;//抓取
    while(!grasp_finshed_l && ros::ok())
    {
        grasp_pub_l.publish(grasp_msg);
        // grasp_pub_r.publish(grasp_msg);
        ros::spinOnce();
        usleep(1000);
    }
    ROS_INFO("hand grasp sucessed!");

    return 0;
}