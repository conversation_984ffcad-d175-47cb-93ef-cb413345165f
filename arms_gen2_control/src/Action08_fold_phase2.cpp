/*
叠毛巾
    1、抓取边角
    2、拖拽，调整第一次折叠后离身体的距离
    3、横向折叠，松手
    4、右手抓近右边角，左手按住毛巾左侧
    5、纵向折叠，右手到松手点后，左手先撤出，右手再松手
    6、右手按住毛巾，拖至中间
*/ 
#include "ros/ros.h"
#include "string"
#include <stdio.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>

#include "arms_gen2_control/Def_Class.h"

using namespace std;

#define PI 3.141592653589

double dt = 0.0005;
double tau = 1;

DATA_PROC_FUNC::DATA_SAVE dataSave;
PLANNING_FUNC::KINEMATICS kineFunc;
TRAJ_PLAN::INTERPOLATION interPlan;
TRAJ_PLAN::ADJUST adjustFunc;
ACT_LIB::DMP dmpLib;
ACT_LIB::INTER interLib;
ACT_LIB::MOVE moveLib;

std::vector<double> container;
bool detect_flag = false;
std_msgs::Int8 grasp_msg;
bool grasp_finshed_l = false;
bool grasp_finshed_r = false;

VectorXd jnt_pos_current_r(7);
VectorXd jnt_pos_current_l(7);
bool flag = false;
void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg) {
    flag = true;
    for(int i = 0; i < 7; i++)
    {
        jnt_pos_current_l[i] = msg->position[i];
        jnt_pos_current_r[i] = msg->position[i+7];
    }
    return;
}

void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "apple: " << value << endl;
    }
}

void graspcallback_left(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_l = msg->data;
    return;
}
void graspcallback_right(const std_msgs::Bool::ConstPtr &msg)
{
    grasp_finshed_r = msg->data;
    return;
}

FILE *fpWrite=fopen("res.txt","w");

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc,argv,"Action08_fold_towel");
    ros::NodeHandle nh;

    ros::Subscriber sub = nh.subscribe("motor_state/arm_motor_state_actual", 1000, jointStateCallback);
    ros::Subscriber vision_pose_base = nh.subscribe("/gripper_det_box", 1, camera_arrayCallback_base);
    ros::Publisher grasp_pub_l = nh.advertise<std_msgs::Int8>("/grasp_control_l",1);
    ros::Subscriber grasp_sub_l = nh.subscribe("LeftGraspFinashed",10,graspcallback_left);
    ros::Publisher grasp_pub_r = nh.advertise<std_msgs::Int8>("/grasp_control_r",1);
    ros::Subscriber grasp_sub_r = nh.subscribe("RightGraspFinashed",10,graspcallback_right);

    /*
    move_mode:
        0: no move
        1: rviz sim
        2: gazebo sim
        3: real move
    */
    CTRL_MODE ctrl_mode = robot_ctrl;
    // CTRL_MODE ctrl_mode = stop;

    VectorXd jnt_end_r(7);
    VectorXd jnt_end_l(7);

    Vector3d bias_hand;
    bias_hand << 0.035, 0.07, 0.035;

    double L_towel = 0.34;

    /*
        获取抓取点
        */ 
        Vector3d pos_grasp_2nd_r;
        Vector3d pose_grasp_2nd_r;
        pose_grasp_2nd_r << PI/4, PI/12, -PI/2;

        Vector3d pos_grasp_2nd_l;
        Vector3d pose_grasp_2nd_l;
        pose_grasp_2nd_l << PI/4, PI/12, -PI/2;

        #pragma region GET POSITIONS BASED ON VISION
            ROS_INFO("Get postions based on vision");
            detect_flag = false;
            while(!detect_flag && ros::ok())
            {
                ros::spinOnce();
                usleep(1000);
                ROS_INFO("no detect");
            }
            /* 接受两个目标物体的位置信息 */
            if (container.size() >= 6) 
            {
                pos_grasp_2nd_l(0) = container[0];
                pos_grasp_2nd_l(1) = -container[1];
                pos_grasp_2nd_l(2) = container[2];
                pos_grasp_2nd_r(0) = container[3];
                pos_grasp_2nd_r(1) = container[4];
                pos_grasp_2nd_r(2) = container[5];
                container.erase(container.begin(), container.begin() + 6);
            }
            cout << "cart_pos_grasp_l: " << pos_grasp_2nd_l(0) << ", " << pos_grasp_2nd_l(1) << ", " << pos_grasp_2nd_l(2) << endl;
            cout << "cart_pos_grasp_r: " << pos_grasp_2nd_r(0) << ", " << pos_grasp_2nd_r(1) << ", " << pos_grasp_2nd_r(2) << endl;
            // cart_pos_grasp_l = cart_pos_grasp_l + Vector3d(0.08, -0.085, 0.025);
            // cart_pos_grasp_r = cart_pos_grasp_r + Vector3d(0.06, -0.045, -0.01);
            // pos_grasp_2nd_l = pos_grasp_2nd_l + Vector3d(0.075, -0.032, 0.115) + Vector3d(-0.015, 0, 0);
            // pos_grasp_2nd_r = pos_grasp_2nd_r + Vector3d(0.040, -0.015, 0.090) + Vector3d(-0.025, -0.01, 0);
            // pos_grasp_2nd_l = pos_grasp_2nd_l + Vector3d(0.051, -0.059, 0.100) + Vector3d(-0.015, 0, 0);
            // pos_grasp_2nd_r = pos_grasp_2nd_r + Vector3d(0.013, -0.017, 0.078) + Vector3d(-0.015, 0, 0);
            pos_grasp_2nd_l = pos_grasp_2nd_l + Vector3d(0.056, -0.053, 0.10) + Vector3d(-0.015, 0, 0);
            pos_grasp_2nd_r = pos_grasp_2nd_r + Vector3d(0.007, -0.015, 0.073) + Vector3d(-0.015, 0, 0);

            // pos_grasp_2nd_r = Vector3d(0.34, -0.16, -0.430449);
            // pos_grasp_2nd_l = Vector3d(0.31, -0.16, -0.431511);
        #pragma endregion
        

    Vector3d pos_grasp_pre_r;
    pos_grasp_pre_r << 0.415, -0.175, -0.260;
    Vector3d pos_grasp_pre_l;
    pos_grasp_pre_l << 0.425, -0.205, -0.240;
    Vector3d pose_grasp_pre_r;
    pose_grasp_pre_r << PI/2.5,PI/4,-PI/2;
    Vector3d pose_grasp_pre_l;
    pose_grasp_pre_l << PI/2.5,PI/4,-PI/2;

    Plan_Res_MulDOF act1_pre_r;
    Plan_Res_MulDOF act1_pre_l;
    act1_pre_r = dmpLib.action1_dmp(pos_grasp_pre_r,pose_grasp_pre_r,dt,1,true,-PI/4);
    if(act1_pre_r.error_flag){
        ROS_ERROR("ACTION1_INIT2GRASP_PRE_R PLANNING FAILED!");
        return 0;
    }
    act1_pre_l = dmpLib.action1_dmp(pos_grasp_pre_l,pose_grasp_pre_l,dt,1,true,-PI/4);
    if(act1_pre_l.error_flag){
        ROS_ERROR("ACTION1_INIT2GRASP_PRE_L PLANNING FAILED!");
        return 0;
    }

    int n1_pre = act1_pre_r.t.size();
    jnt_end_r = act1_pre_r.pos.row(n1_pre-1);
    jnt_end_l = act1_pre_l.pos.row(n1_pre-1);

    ROS_INFO("ACTION1 STARTING!");
        ROS_INFO("- ACTION1_INIT2GRASP_PRE STARTING!");
        dataSave.save2txt(act1_pre_r,act1_pre_l,fpWrite);
        moveLib.move(act1_pre_r,act1_pre_l,ctrl_mode);
        ROS_INFO("- ACTION1_INIT2GRASP_PRE FINISHED!");

    int count = 0;
    grasp_msg.data = 7;
    while(count < 1000)
    {
        grasp_pub_l.publish(grasp_msg);
        grasp_pub_r.publish(grasp_msg);
        count++;
    }
    sleep(3);
    ROS_INFO("hand grasp sucessed!");
    ROS_INFO("ACTION1 FINISHED!"); 


    sleep(3);

    #pragma region ACTION3
        /*
        3、第二次折叠
            - 右手抓近右边角，左手按住毛巾左侧
            - 纵向折叠，右手到松手点后，左手先撤出，右手再松手
        */
        int act3_enable_flag = 1;
        if(!act3_enable_flag){
            return 0;
        }

        

        /*
        左右手移动到抓取点
        */
        VectorXd t_act3_p1(3);
        t_act3_p1 << 0, 4, 7;
        VectorXd phi_act3_p1_r(2);
        VectorXd phi_act3_p1_l(2);
        phi_act3_p1_r << -PI/4, -PI/4;
        phi_act3_p1_l << -PI/4, -PI/4;

        MatrixXd pos_act3_p1_r(2,3);
        pos_act3_p1_r.row(0) = pos_grasp_2nd_r + Vector3d(-0.02, 0, 0.06);
        pos_act3_p1_r.row(1) = pos_grasp_2nd_r;
        MatrixXd pose_act3_p1_r(2,3);
        pose_act3_p1_r.row(0) = pose_grasp_2nd_r;
        pose_act3_p1_r.row(1) = pose_grasp_2nd_r;

        MatrixXd pos_act3_p1_l(2,3);
        pos_act3_p1_l.row(0) = pos_grasp_2nd_l + Vector3d(-0.02, 0, 0.06);
        pos_act3_p1_l.row(1) = pos_grasp_2nd_l;
        MatrixXd pose_act3_p1_l(2,3);
        pose_act3_p1_l.row(0) = pose_grasp_2nd_l;
        pose_act3_p1_l.row(1) = pose_grasp_2nd_l;

        Plan_Res_MulDOF act3_p1_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p1,pos_act3_p1_r,pose_act3_p1_r,phi_act3_p1_r,dt);
        if(act3_p1_r.error_flag){
            ROS_ERROR("ACTION3_PHASE1_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p1_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p1,pos_act3_p1_l,pose_act3_p1_l,phi_act3_p1_l,dt);
        if(act3_p1_l.error_flag){
            ROS_ERROR("ACTION3_PHASE1_L PLANNING FAILED!");
            return 0;
        }

        int n3_p1 = act3_p1_r.t.size();
        jnt_end_r = act3_p1_r.pos.row(n3_p1-1);
        jnt_end_l = act3_p1_l.pos.row(n3_p1-1);

        ROS_INFO("- ACTION3_PHASE1 STARTING!");
        dataSave.save2txt(act3_p1_r,act3_p1_l,fpWrite);
        moveLib.move(act3_p1_r,act3_p1_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE1 FINISHED!");

        /*
        抓取
        */
        count = 0;
        grasp_msg.data = 8;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        // sleep(3);
        ROS_INFO("hand grasp sucessed!");

        /*
        向右拖动毛巾
        */
        VectorXd t_act3_p2(2);
        t_act3_p2 << 0, 5;
        VectorXd phi_act3_p2_r(1);
        VectorXd phi_act3_p2_l(1);
        phi_act3_p2_r << -PI/2;
        phi_act3_p2_l << -PI/2;

        MatrixXd pos_act3_p2_r(1,3);
        pos_act3_p2_r.row(0) = pos_grasp_2nd_r + Vector3d(0.03, -0.15, 0.03);
        
        MatrixXd pose_act3_p2_r(1,3);
        pose_act3_p2_r.row(0) = Vector3d(PI/5, PI/12, -PI/2);

        MatrixXd pos_act3_p2_l(1,3);
        pos_act3_p2_l.row(0) = pos_grasp_2nd_l + Vector3d(0.03, 0.155, 0.03);
        
        MatrixXd pose_act3_p2_l(1,3);
        pose_act3_p2_l.row(0) = Vector3d(PI/8, PI/12, -PI/2);

        Plan_Res_MulDOF act3_p2_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p2,pos_act3_p2_r,pose_act3_p2_r,phi_act3_p2_r,dt);
        if(act3_p2_r.error_flag){
            ROS_ERROR("ACTION3_PHASE2_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p2_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p2,pos_act3_p2_l,pose_act3_p2_l,phi_act3_p2_l,dt);
        if(act3_p2_l.error_flag){
            ROS_ERROR("ACTION3_PHASE2_L PLANNING FAILED!");
            return 0;
        }

        int n3_p2 = act3_p2_r.t.size();
        jnt_end_r = act3_p2_r.pos.row(n3_p2-1);
        jnt_end_l = act3_p2_l.pos.row(n3_p2-1);

        ROS_INFO("- ACTION3_PHASE2 STARTING!");
        dataSave.save2txt(act3_p2_r,act3_p2_l,fpWrite);
        moveLib.move(act3_p2_r,act3_p2_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE2 FINISHED!");

        /*
        左手松开
        */
        count = 0;
        grasp_msg.data = 7;
        while(count < 1000)
        {
            grasp_pub_l.publish(grasp_msg);
            // grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("left hand release sucessed!");

        /*
        右手开始折叠操作
        */
        VectorXd t_act3_p3(3);
        t_act3_p3 << 0, 4, 8;
        VectorXd phi_act3_p3_r(2);
        VectorXd phi_act3_p3_l(2);
        phi_act3_p3_r << -PI/2, -PI/2;
        phi_act3_p3_l << -PI/2, -PI/2;

        MatrixXd pos_act3_p3_r(2,3);
        pos_act3_p3_r.row(0) = pos_grasp_2nd_r + Vector3d(0.02, 0.0, 0.1);
        pos_act3_p3_r.row(1) = pos_grasp_2nd_r + Vector3d(0.0, 0.17, 0.07);
        MatrixXd pose_act3_p3_r(2,3);
        pose_act3_p3_r.row(0) = Vector3d(PI/4, PI/12, -PI/2);
        pose_act3_p3_r.row(1) = Vector3d(PI/8, PI/12, -PI/2);

        MatrixXd pos_act3_p3_l(2,3);
        pos_act3_p3_l.row(0) = pos_grasp_2nd_l + Vector3d(0.02, -0.02, 0.05);
        pos_act3_p3_l.row(1) = pos_grasp_2nd_l + Vector3d(0.02, -0.02, 0.05);
        MatrixXd pose_act3_p3_l(2,3);
        pose_act3_p3_l.row(0) = Vector3d(PI/4, PI/12, -PI/2);
        pose_act3_p3_l.row(1) = Vector3d(PI/4, PI/12, -PI/2);

        Plan_Res_MulDOF act3_p3_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act3_p3,pos_act3_p3_r,pose_act3_p3_r,phi_act3_p3_r,dt);
        if(act3_p3_r.error_flag){
            ROS_ERROR("ACTION3_PHASE3_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act3_p3_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act3_p3,pos_act3_p3_l,pose_act3_p3_l,phi_act3_p3_l,dt);
        if(act3_p3_l.error_flag){
            ROS_ERROR("ACTION3_PHASE3_L PLANNING FAILED!");
            return 0;
        }

        int n3_p3 = act3_p3_r.t.size();
        jnt_end_r = act3_p3_r.pos.row(n3_p3-1);
        jnt_end_l = act3_p3_l.pos.row(n3_p3-1);

        ROS_INFO("- ACTION3_PHASE3 STARTING!");
        dataSave.save2txt(act3_p3_r,act3_p3_l,fpWrite);
        moveLib.move(act3_p3_r,act3_p3_l,ctrl_mode);
        ROS_INFO("- ACTION3_PHASE3 FINISHED!");

        /*
        右手松开
        */
        count = 0;
        grasp_msg.data = 7;
        while(count < 1000)
        {
            // grasp_pub_l.publish(grasp_msg);
            grasp_pub_r.publish(grasp_msg);
            count++;
        }
        sleep(3);
        ROS_INFO("hand release sucessed!");


        ROS_INFO("ACTION3 FINISHED!\n");
    #pragma endregion

    sleep(60);

    #pragma region ACTION4
        // /*
        // 4、毛巾拖到右手边
        //     - 右手按住毛巾中间
        //     - 移动
        // */
        int act4_enable_flag = 1;
        if(!act4_enable_flag){
            return 0;
        }

        // Vector3d pos_push;
        // pos_push = Vector3d(pos_grasp_2nd_r(0),0.05-L_towel,pos_grasp_2nd_r(2)) + Vector3d(L_towel/4,3*L_towel/4,0);
        // Vector3d pose_push1;
        // Vector3d pose_push2;
        // pose_push1 << PI/3,0,-PI/2;
        // pose_push2 << PI/3,0,-PI/2;

        // VectorXd t_act4(3);
        // t_act4 << 0, 4, 10;
        // VectorXd phi_act4(2);
        // phi_act4 << -PI/2,-PI/2;
        // MatrixXd pos_act4(2,3);
        // pos_act4.row(0) = pos_push;
        // pos_act4.row(1) = pos_push + Vector3d(0.0,-0.2,0);
        // MatrixXd pose_act4(2,3);
        // pose_act4.row(0) = pose_push1;
        // pose_act4.row(1) = pose_push2;

        // Plan_Res_MulDOF act4_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act4,pos_act4,pose_act4,phi_act4,dt);
        // if(act4_r.error_flag){
        //     ROS_ERROR("ACTION3_PHASE3_R PLANNING FAILED!");
        //     return 0;
        // }
        // int n4 = act4_r.t.size();
        // Plan_Res_MulDOF act4_l = interLib.stay_still(jnt_end_l,n4,dt);

        // ROS_INFO("ACTION4 STARTING!");
        // dataSave.save2txt(act4_r,act4_l,fpWrite);
        // moveLib.move(act4_r,act4_l,ctrl_mode);
        // ROS_INFO("ACTION4 FINISHED!\n");

        Vector3d pose_grasp_act4;
        pose_grasp_act4 << PI/4, 0, 0;
        Vector3d start_vec_act4 = {0.35, -0.30, -0.07};

        VectorXd t_act4(2);
        t_act4 << 0, 5;
        VectorXd phi_act4(1);
        phi_act4 << -PI/4;
        MatrixXd pos_act4(1,3);
        pos_act4.row(0) = start_vec_act4;
        MatrixXd pose_act4(2,3);
        pose_act4.row(0) = pose_grasp_act4;

        Plan_Res_MulDOF act4_r = interPlan.quinitic_poly_inter_mulDOF(jnt_end_r,t_act4,pos_act4,pose_act4,phi_act4,dt);
        if(act4_r.error_flag){
            ROS_ERROR("ACTION4_R PLANNING FAILED!");
            return 0;
        }
        Plan_Res_MulDOF act4_l = interPlan.quinitic_poly_inter_mulDOF(jnt_end_l,t_act4,pos_act4,pose_act4,phi_act4,dt);
        if(act4_l.error_flag){
            ROS_ERROR("ACTION4_L PLANNING FAILED!");
            return 0;
        }
        // int n4 = act4_r.t.size();
        // Plan_Res_MulDOF act4_l = interLib.stay_still(jnt_end_l,n4,dt);

        ROS_INFO("ACTION4 STARTING!");
        dataSave.save2txt(act4_r,act4_l,fpWrite);
        moveLib.move(act4_r,act4_l,ctrl_mode);
        ROS_INFO("ACTION4 FINISHED!\n");


    #pragma endregion

    fclose(fpWrite);
    return 0;
}