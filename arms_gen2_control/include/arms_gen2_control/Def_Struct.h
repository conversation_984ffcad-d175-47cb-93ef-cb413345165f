#ifndef _DEF_STRUCT_H
#define _DEF_STRUCT_H

#include <vector>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/LU>
#include <stdexcept> // For std::out_of_range

#include <visualization_msgs/Marker.h>
#include <geometry_msgs/Pose.h>

#include <coal/math/transform.h>
#include <coal/mesh_loader/loader.h>
#include <coal/BVH/BVH_model.h>
#include <coal/collision.h>
#include <coal/collision_data.h>
#include <memory>

using namespace Eigen;

struct Demon_Traj
{
    VectorXd time;
    VectorXd pos_ta1;
    VectorXd pos_ta2;
    VectorXd pos_ta3;
    VectorXd pos_ta4;
    VectorXd vel_ta1;
    VectorXd vel_ta2;
    VectorXd vel_ta3;
    VectorXd vel_ta4;
    VectorXd acc_ta1;
    VectorXd acc_ta2;
    VectorXd acc_ta3;
    VectorXd acc_ta4;
    VectorXd ArmJnt;
};
struct Train_Par
{
    int nbfs;       // 基函数个数
    double alpha_y;
    double k;
    int k_f;
};
struct Train_Res
{
    MatrixXd w;
	VectorXd c;
	VectorXd h;
	int N;
	double alpha_x;
	double alpha_y;
	double beta_y;
	double t_end;
	int k_f;
};
struct Regress_Par
{
    Vector4d x_init;
    Vector4d x_goal;
    double tau;
    double dt;
};
// struct Plan_Res_MulDOF
// {
//     MatrixXd pos;
//     MatrixXd vel;
//     MatrixXd acc;
//     VectorXd t;
//     int error_flag = 0;
// };
struct Plan_Res_MulDOF
{
    Eigen::MatrixXd pos;
    Eigen::MatrixXd vel;
    Eigen::MatrixXd acc;
    Eigen::VectorXd t;
    int error_flag = 0;
    int n;
    bool flagHandCtrl = false;
    Eigen::MatrixXd pos_hand; 
    int n_hand;
};
struct ArmTraj{
    Plan_Res_MulDOF right;
    Plan_Res_MulDOF left;
};
struct Plan_Res
{
    VectorXd pos;
    VectorXd vel;
    VectorXd acc;
    VectorXd t;
};
struct JntPos
{
    VectorXd theta;
    int error_flag = 0;
    int solved_flag = 0;
};
struct KineRes
{
    double eul_r;
    double eul_p;
    double eul_y;
    Vector3d cart_end_position;
    Matrix3d transMatrix;
};

typedef enum ctrl_mode{
    stop = 0,
    rviz_ctrl,
    gazebo_ctrl,
    robot_ctrl
}CTRL_MODE;

template<typename T>
class DynamicArray {
private:
    T* data;         // 指向动态分配的数组
    size_t capacity; // 数组的容量
    size_t length;   // 数组当前长度

public:
    // 构造函数
    DynamicArray(size_t initial_capacity = 10) : capacity(initial_capacity), length(0) {
        data = new T[capacity];
    }
    // 析构函数
    ~DynamicArray() {
        delete[] data;
    }
    // 拷贝构造函数
    DynamicArray(const DynamicArray& other) : capacity(other.capacity), length(other.length) {
        data = new T[capacity];
        for (size_t i = 0; i < length; ++i) {
            data[i] = other.data[i];
        }
    }
    // 赋值运算符
    DynamicArray& operator=(const DynamicArray& other) {
        if (this != &other) {
            delete[] data;
            capacity = other.capacity;
            length = other.length;
            data = new T[capacity];
            for (size_t i = 0; i < length; ++i) {
                data[i] = other.data[i];
            }
        }
        return *this;
    }
    // 添加元素
    void push_back(const T& value) {
        if (length == capacity) {
            reserve(capacity * 2);
        }
        data[length++] = value;
    }
    // 获取元素
    T& operator[](size_t index) {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取常量元素
    const T& operator[](size_t index) const {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取数组长度
    size_t size() const {
        return length;
    }
    // 调整数组容量
    void reserve(size_t new_capacity) {
        if (new_capacity > capacity) {
            T* new_data = new T[new_capacity];
            for (size_t i = 0; i < length; ++i) {
                new_data[i] = data[i];
            }
            delete[] data;
            data = new_data;
            capacity = new_capacity;
        }
    }
};

#endif