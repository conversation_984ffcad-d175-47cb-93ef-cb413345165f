#ifndef _DEF_CLASS_H
#define _DEF_CLASS_H

#include "Def_Struct.h"

#include <stdio.h>
#include <iostream>
#include <fstream>
#include <string>

#include <serial/serial.h>

#include <chrono>
#include <ctime>

#include <mutex>
#include <future>
#include <thread>

#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl_parser/kdl_parser.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainfksolver.hpp>

#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>

#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Float64.h>
#include <sensor_msgs/JointState.h>

#include "ros/ros.h"
#include "ros/callback_queue.h"

using namespace std;

namespace DATA_PROC {
    extern Eigen::VectorXd jnt_pos_actual_r;
    extern Eigen::VectorXd jnt_pos_actual_l;

    class Data_Pub {
        public:
            Data_Pub(bool flag_colli_det = true);
            ~Data_Pub();

            int initPort(serial::Serial *port, uint8_t hand_id);
            void checkPortOpen(serial::Serial *com_port);
            bool setANGLE(int angle0, int angle1, int angle2, int angle3, int angle4, int angle5, uint8_t hand_id);
            bool setSPEED(int speed0, int speed1, int speed2, int speed3, int speed4, int speed5, uint8_t hand_id);
            bool setFORCE(int force0, int force1, int force2, int force3, int force4, int force5, uint8_t hand_id);
            void getFORCE_ACT(std::array<float, 6>& curforce_, int control_hand_id);
            void rightHand();
            void rightHand(const std::vector<int>& pos_cmd);
            void rightHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd);
            void leftHand();
            void leftHand(const std::vector<int>& pos_cmd);
            void leftHand(const std::vector<int>& pos_cmd, const std::vector<int>& vel_cmd);
            void doubleHand();
            void doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& pos_cmd_l);
            void doubleHand(const std::vector<int>& pos_cmd_r, const std::vector<int>& vel_cmd_r, const std::vector<int>& pos_cmd_l, const std::vector<int>& vel_cmd_l);

        private:
            ros::NodeHandle nh_;
            // 话题发布对象
            ros::Publisher pub_motor_;
            ros::Publisher pub_rviz_;
            // 话题数据载体
            sensor_msgs::JointState rviz_msgs_;
            std_msgs::Float64MultiArray motor_msgs_;
            // 因时手通信相关
            std::string port_name_;
            uint8_t hand_id1_;
            uint8_t hand_id2_;
            uint32_t baudrate_;
            uint8_t test_flags_;
            uint8_t Serial_flags_;
            serial::Serial *com_port_;
            float act_position_;
            uint8_t hand_state_;
            static constexpr double WAIT_FOR_RESPONSE_INTERVAL_ = 0.5;
            static constexpr double INPUT_BUFFER_SIZE_ = 64;
            // 因时手默认位置
            std::vector<int> default_pos_;
            // 文件指针
            FILE *fwPubPos_;
            FILE *fwPubVel_;
            FILE *fwPubAcc_;
            // 机器人convex模型
            DynamicArray<std::shared_ptr<coal::ConvexBase>> robotModel_;
            // 是否进行碰撞检测
            bool is_colli_det_;
    };
}

namespace DATA_PROC_FUNC {
    class DATA_SAVE {
        public:
            void save2txt(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, FILE *fpWrite);
    };
    class DATA_PUB {
        public:
            void pub2rviz(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
            void pub2gazebo(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
            void pub2motors(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l);
    };
}

namespace PLANNING_FUNC {
    class DMP_FUNC {
        public:
            Demon_Traj dataExt(string pathName1, string pathName2);
            Train_Res dmpTrain(Demon_Traj data, Train_Par par);
            Plan_Res_MulDOF dmpRegress(Train_Res trainRes, Regress_Par par);
            Plan_Res_MulDOF lnrCmp(Plan_Res_MulDOF traj, Vector4d g);
            Plan_Res_MulDOF limitAmplitude(Plan_Res_MulDOF traj, Vector4d y0, Vector4d g, Vector4d jntLimit);
    };
    class KINEMATICS {
        public:
            JntPos invKine_JntPos(Vector3d g, double armJnt, Vector3d pose);
            KineRes fkine(VectorXd theta);
            Vector3d biasHand(Vector3d pose, Vector3d baisVec);
    };
    class INTERPOLATION {
        public:
            Plan_Res quinitic_poly_inter(VectorXd t_seg, VectorXd pos_seg, double dt);
            Plan_Res cub_spline_inter(VectorXd t_seg, VectorXd pos_seg, double dt, double v0=0.0, double vn=0.0);
    };
}

namespace TRAJ_PLAN {
    class DMP{
        public:
            Plan_Res_MulDOF dmp(string pathJntPos, string pathAngArm, Train_Par trainPar, VectorXd theta_init, Vector3d x_goal,Vector3d pose, Vector4d jntLimit, double dt, double tau = 1, bool useSelfDefPhi = false, double phi = 0, bool ikine_info_show = false, bool plan_info_show = false);
    };
    class INTERPOLATION{
        public:
            Plan_Res_MulDOF quinitic_poly_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, double dt);
            Plan_Res_MulDOF cub_spline_inter_mulDOF(VectorXd jnt_pos_init, VectorXd t_seg, MatrixXd position_seg, MatrixXd pose_seg, VectorXd phi, MatrixXd vel_ends, double dt);
    };
    class ADJUST{
        public:
            Plan_Res_MulDOF traj_splic(Plan_Res_MulDOF action1, Plan_Res_MulDOF action2);
            Plan_Res_MulDOF traj_extend(Plan_Res_MulDOF action, int n, double dt);
    };
}

namespace ACT_LIB {
    class DMP{
        public:
            Plan_Res_MulDOF action1_dmp(Vector3d position, Vector3d pose, double dt, double tau = 1, bool useSelfDefPhi = false, double phi = 0, bool ikine_info_show = false, bool plan_info_show = false);
    };
    class INTER{
        public:
            Plan_Res_MulDOF stay_still(VectorXd jnt_pos, int n, double dt);
    };
    class MOVE{
        public:
            void move(Plan_Res_MulDOF motion_r, Plan_Res_MulDOF motion_l, int move_mode);
    };
}

#endif