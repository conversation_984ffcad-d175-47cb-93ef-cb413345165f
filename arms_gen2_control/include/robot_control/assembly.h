#pragma once

ros::Publisher car_vir_pos_pub, car_act_pos_pub, car_err_pos_pub, car_vir_vel_pub, car_act_vel_pub;

VectorXd jnt_pos_act_r(7);
VectorXd jnt_pos_act_l(7);
VectorXd jnt_vel_act_r(7);
VectorXd jnt_vel_act_l(7);
VectorXd car_pos_act_last_r(6);
VectorXd car_pos_act_last_l(6);
VectorXd car_pos_act_r(6);
VectorXd car_pos_act_l(6);
VectorXd car_vel_act_r(6);
VectorXd car_vel_act_l(6);

VectorXd jnt_pos_vir_r(7);
VectorXd jnt_pos_vir_l(7);
VectorXd jnt_vel_vir_r(7);
VectorXd jnt_vel_vir_l(7);
VectorXd car_pos_vir_last_r(6);
VectorXd car_pos_vir_last_l(6);
VectorXd car_pos_vir_r(6);
VectorXd car_pos_vir_l(6);
VectorXd car_vel_vir_r(6);
VectorXd car_vel_vir_l(6);

Eigen::VectorXd f_push(double t)
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double dd = -0.0016 * t;
    a << 0, 0, 0, 0, 0, 0;
    b << 0, 0, 0, 0, 0, 0;
    c << 0, 0, 0, 0, 0, 0; 
    d << 0, 0, dd, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_rub(double t)//摩擦
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/4;//频率
    double aa = PI/6;
    a << 0, 0, 0, 0, 0, aa;
    b << 0, 0, 0, 0, 0, v;
    c << 0, 0, 0, 0, 0, 0; 
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_wiggle(double t)//摆动
{
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/2;//频率
    // double aa = PI/10;
    double aa = 0.005*t;//0.03
    a << 0, 0, 0, aa, aa, 0;
    b << 0, 0, 0, v, v, 0;
    c << 0, 0, 0, 0, PI_2, 0; 
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

Eigen::VectorXd f_spiral(double t)//螺旋
{       
    Eigen::VectorXd a(6), b(6), c(6), d(6); 
    double v = PI/2;//频率 PI
    // double aa = 0.25*sin(PI_2/20*t) + 0.05;
    double aa = 0.00065*t;//0.0003*t
    a << aa, aa, 0, 0, 0, 0;
    b << v, v, 0, 0, 0, 0;
    c << -0, PI_2, 0, 0, 0, 0; //0, PI_2, 0, 0, 0, 0
    d << 0, 0, 0, 0, 0, 0;

    // 计算 sin(t * b + c) 逐元素运算
    Eigen::VectorXd sin_part = (t * b + c).array().sin();
    // 计算 w = diag(a) * sin_part + d
    Eigen::VectorXd w = a.asDiagonal() * sin_part + d;

    // std::cout << "w: " << std::endl << w << std::endl;

    return w;
}

// 函数：计算并输出末端执行器速度
VectorXd computeEndVelocity(KDL::Chain chain, VectorXd jnt_pos_current, VectorXd jnt_vel_current) {
    int n = chain.getNrOfJoints();
    KDL::JntArray joint_angles(n);
    for (size_t i = 0; i < n; i++) {
        joint_angles(i) = jnt_pos_current[i];
    }
    // 创建用于存储雅可比矩阵的 KDL::Jacobian 对象
    KDL::Jacobian jacobian(n);

    // 创建正向运动学求解器
    KDL::ChainJntToJacSolver jnt_to_jac_solver(chain);

    // 计算雅可比矩阵
    jnt_to_jac_solver.JntToJac(joint_angles, jacobian);


    // 计算末端执行器速度 (6D 向量，包含线速度和角速度)
    VectorXd car_vel_current(6);
    MatrixXd jacobian_matrix = jacobian.data;

    car_vel_current = jacobian_matrix * jnt_vel_current;

    return car_vel_current;
    
}

// 函数：计算并输出末端执行器速度
// VectorXd computeEndpose(KDL::Chain chain, VectorXd jnt_pos_current) {

//     std::array<double, 7> arr;

//     for (size_t i = 0; i < 7; i++) {
//         arr[i] = jnt_pos_current(i);  // 通过索引访问 Eigen::VectorXd 的元素
//     }

//     geometry_msgs::Pose pose = kdl_fk(chain, arr);



//     return car_vel_current;
    
// }

void pub_motors(Plan_Res_Arm motion_r, Plan_Res_Arm motion_l, int move_mode, int stage){

    sensor_msgs::JointState jnt_state_msgs_rviz;

    int num_jnt = 14;

    std_msgs::Float64MultiArray jnt_state_msgs;
    jnt_state_msgs.data.resize(28);

    int n = motion_r.t.rows();
    int num = 7;
    ros::Rate rate(1000);
    int q = 0;int q1 = 0;
    VectorXd p1(7);VectorXd p2(7);
    while(ros::ok()){
        for(int i = 0; i < n; i++){

            //电流限制
            for (size_t j = 0; j < 7; j++)
            {
                if (abs(jnt_effort_l[j])  > 1.2)
                {
                    if (p1[j] > 10)
                    {
                        std::cout << "左臂" << j+1 << "关节电流超限" << jnt_effort_l[j] << std::endl;
                        return;
                    }else{
                        p1[j]++;
                    }                

                }else{
                    p1[j] = 0;
                }
                if (abs(jnt_effort_r[j])  > 0.7)
                {
                    if (p2[j] > 10)
                    {
                        std::cout << "右臂" << j+1 << "关节电流超限: " << jnt_effort_r[j] << std::endl;
                        return;
                    }else{
                        p2[j]++;
                    }                

                }else{
                    p2[j] = 0;
                }

            }

            if(move_mode == 3){

                if (stage == 1)
                {
                    //接触条件
                    if (abs((car_pos_vir_r[7] - car_pos_act_r[7])*1000) > 0.22)//0.4
                    {
                        if (q > 50)
                        {
                            ROS_INFO("接触到孔（右）");
                            return;
                        }else{
                            q++;
                        }                

                    } else{
                        q = 0;
                    }
                    if (abs((car_pos_vir_l[1] - car_pos_act_l[1])*1000) > 0.25)//0.2
                    {
                        if (q1 > 170)
                        {
                            ROS_INFO("接触到孔（左）");
                            return;
                        }else{
                            q1++;
                        }                

                    } else{
                        q1 = 0;
                    }           
                }else if(stage == 2)
                {
                    /* code */
                }

                for(int j = 0; j < num; j++){
                    jnt_state_msgs.data[j] = motion_l.pos(i,j);
                    jnt_state_msgs.data[j+num] = motion_r.pos(i,j);
                    jnt_state_msgs.data[j+2*num] = 0;
                    jnt_state_msgs.data[j+3*num] = 0;
                }
            }

            jnt_state_msgs_rviz.header.stamp = ros::Time::now();
            jnt_state_msgs_rviz.header.frame_id = "pub2rviz";

            jnt_state_msgs_rviz.name.resize(num_jnt);
            jnt_state_msgs_rviz.position.resize(num_jnt);
            jnt_state_msgs_rviz.velocity.resize(num_jnt);
            
            for(int j = 0; j < num; j++){
                jnt_state_msgs_rviz.name[j] = "r_arm_Joint" + to_string(j+1);
                jnt_state_msgs_rviz.position[j] = motion_r.pos(i,j);
                jnt_state_msgs_rviz.velocity[j] = motion_r.vel(i,j);
                jnt_state_msgs_rviz.name[j+num] = "l_arm_Joint" + to_string(j+1);
                jnt_state_msgs_rviz.position[j+num] = motion_l.pos(i,j);
                jnt_state_msgs_rviz.velocity[j+num] = motion_l.vel(i,j);
                // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);

            }
            // jnt_state_msgs.position[0] = 0.2;

            rate.sleep();
        }
        if (stage > 0){
            ROS_ERROR("未接触到孔");
        }
        
        break;
    }
}