cmake_minimum_required(VERSION 3.0.2)
project(arms_gen2_control)

## Compile as C++11, supported in ROS Kinetic and newer
# add_compile_options(-std=c++11)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  message_generation
  serial
)
find_package(Python3 COMPONENTS Development NumPy)
find_package(orocos_kdl REQUIRED)
find_package(kdl_parser REQUIRED)

#########
## COAL ##
#########
# Using coal library instead of hpp-fcl
# 设置conda环境路径
#set(CONDA_ENV_PATH $ENV{HOME}/anaconda3/envs/coal)
set(CONDA_ENV_PATH /root/miniconda3/envs/coal)
# 添加conda环境中的库路径
list(APPEND CMAKE_PREFIX_PATH ${CONDA_ENV_PATH})
# 查找coal库
find_package(coal REQUIRED)
set(CMAKE_INSTALL_RPATH "${CONDA_ENV_PATH}/include")
include_directories(
include
  ${catkin_INCLUDE_DIRS}
  ${CONDA_ENV_PATH}/include
)

# 设置运行时库路径（RPATH）
set(CMAKE_INSTALL_RPATH "${CONDA_ENV_PATH}/lib")

## Generate services in the 'srv' folder
add_service_files(
  FILES
  get_angle_act.srv
  get_current.srv
  get_error.srv
  get_force_act.srv
  set_angle.srv
  set_current_limit.srv
  set_speed.srv
)

## Generate added messages and services with any dependencies listed here
generate_messages(
  DEPENDENCIES
  std_msgs
)

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if your package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
 INCLUDE_DIRS include
 LIBRARIES def_lib
#  CATKIN_DEPENDS roscpp rospy std_msgs
#  DEPENDS system_lib
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
include_directories(
include
  ${catkin_INCLUDE_DIRS}
)

## Declare a C++ library
# add_library(${PROJECT_NAME}
#   src/${PROJECT_NAME}/arms_gen2_control.cpp
# )
add_library(def_lib
  include/arms_gen2_control/Def_Struct.h
  include/arms_gen2_control/Def_Class.h
  src/lib01_SubFunc.cpp
  src/lib02_PlanFunc.cpp
  src/lib03_PreDefinedActions.cpp
)

include_directories(/usr/include/eigen3)

add_library(robot_lib
  src/robot_control.cpp
)

target_link_libraries(robot_lib
  ${catkin_LIBRARIES}
  ${orocos_kdl_LIBRARIES}
  ${kdl_parser_LIBRARIES}
  ${COAL_LIBRARIES}
)

## Add cmake target dependencies of the library
## as an example, code may need to be generated before libraries
## either from message generation or dynamic reconfigure
# add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Declare a C++ executable
## With catkin_make all packages are built within a single CMake context
## The recommended prefix ensures that target names across packages don't collide
add_executable(Demo01 src/Demo01.cpp)
add_executable(Demo02 src/Demo02.cpp)
add_executable(demo03 src/demo03.cpp)
add_executable(Action01_return2init src/Action01_return2init.cpp)
add_executable(Action001_return2init src/Action001_return2init.cpp)
add_executable(Action02_XYZmove src/Action02_XYZmove.cpp)
add_executable(Action03_welcome src/Action03_welcome.cpp)
# add_executable(Action04_pour_water src/Action04_pour_water.cpp)
add_executable(Action05_pour_water_baseLLM src/Action05_pour_water_baseLLM.cpp)
add_executable(Action06_pour_water_ver2 src/Action06_pour_water_ver2.cpp)
add_executable(Action07_fold_towel src/Action07_fold_towel.cpp)
add_executable(Action08_fold_phase2 src/Action08_fold_phase2.cpp)
add_executable(Action09_give_fruit src/Action09_give_fruit.cpp)
add_executable(Action10_give_fruit_based_LLM src/Action10_give_fruit_based_LLM.cpp)
add_executable(Action11_put_fruit src/Action11_put_fruit.cpp)
add_executable(Action5_grasp_based_vision src/Action5_grasp_based_vision.cpp)
## Rename C++ executable without prefix
## The above recommended prefix causes long target names, the following renames the
## target back to the shorter version for ease of user use
## e.g. "rosrun someones_pkg node" instead of "rosrun someones_pkg someones_pkg_node"
# set_target_properties(${PROJECT_NAME}_node PROPERTIES OUTPUT_NAME node PREFIX "")

## Add cmake target dependencies of the executable
## same as for the library above
# add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(def_lib ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Demo01 ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Demo02 ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(demo03 ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action01_return2init ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action001_return2init ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action02_XYZmove ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action03_welcome ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
# add_dependencies(Action04_pour_water ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action05_pour_water_baseLLM ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action06_pour_water_ver2 ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action07_fold_towel ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action08_fold_phase2 ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action09_give_fruit ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action10_give_fruit_based_LLM ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action11_put_fruit ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_dependencies(Action5_grasp_based_vision ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})


## Specify libraries to link a library or executable target against
# target_link_libraries(${PROJECT_NAME}_node
#   ${catkin_LIBRARIES}
# )
target_link_libraries(def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Demo01
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Demo02
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(demo03
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action01_return2init
  def_lib
  robot_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action001_return2init
  def_lib
  robot_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action02_XYZmove
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action03_welcome
  def_lib
  ${catkin_LIBRARIES}
)
# target_link_libraries(Action04_pour_water
#   def_lib
#   ${catkin_LIBRARIES}
# )
target_link_libraries(Action05_pour_water_baseLLM
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action06_pour_water_ver2
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action07_fold_towel
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action08_fold_phase2
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action09_give_fruit
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action10_give_fruit_based_LLM
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action11_put_fruit
  def_lib
  ${catkin_LIBRARIES}
)
target_link_libraries(Action5_grasp_based_vision
  def_lib
  ${catkin_LIBRARIES}
)

add_executable(Action12_give_fruit src/Action12_give_fruit.cpp)
target_link_libraries(Action12_give_fruit
  robot_lib
  def_lib
)
add_executable(Action04_pour_water src/Action04_pour_water.cpp)
target_link_libraries(Action04_pour_water
  robot_lib
  def_lib
)
add_executable(Action13_give_fruit_based_LLM src/Action13_give_fruit_based_LLM.cpp)
target_link_libraries(Action13_give_fruit_based_LLM
  robot_lib
  def_lib
)
#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# catkin_install_python(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables for installation
## See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_executables.html
# install(TARGETS ${PROJECT_NAME}_node
#   RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark libraries for installation
## See http://docs.ros.org/melodic/api/catkin/html/howto/format1/building_libraries.html
# install(TARGETS ${PROJECT_NAME}
#   ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   RUNTIME DESTINATION ${CATKIN_GLOBAL_BIN_DESTINATION}
# )

## Mark cpp header files for installation
# install(DIRECTORY include/${PROJECT_NAME}/
#   DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
#   FILES_MATCHING PATTERN "*.h"
#   PATTERN ".svn" EXCLUDE
# )

## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   # myfile1
#   # myfile2
#   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_arms_gen2_control.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
